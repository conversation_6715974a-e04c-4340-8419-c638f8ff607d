-- ---------------------------------- 生产质量二期-------------------------------

-- 来料检验数据
CREATE TABLE `supplier_pro_material_stock`
(
    id                   bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    supplier_sub_code    varchar(255) NOT NULL DEFAULT '' COMMENT '子件编码',
    supplier_sub_name    varchar(255) NOT NULL DEFAULT '' COMMENT '子件名称',
    sub_supplier_code    varchar(255) NOT NULL DEFAULT '' COMMENT '分供方代码',
    sub_supplier_name    varchar(255) NOT NULL DEFAULT '' COMMENT '分供方名称',
    sub_supplier_address varchar(255) NOT NULL DEFAULT '' COMMENT '分供方地址 , 分供方发货地址：省市区（县），不用于详细地址',
    component_code       varchar(255) NOT NULL DEFAULT '' COMMENT '分供方子件编码',
    component_name       varchar(255) NOT NULL DEFAULT '' COMMENT '分供方子件名称',
    sub_batch_no         varchar(255) NOT NULL DEFAULT '' COMMENT '子件批次号',
    sub_batch_num        bigint       NOT NULL DEFAULT 0 COMMENT '子件批次数量',
    sub_batch_sn         varchar(255) NOT NULL DEFAULT '' COMMENT '子件SN码',
    emp_code             varchar(255) NOT NULL DEFAULT '' COMMENT '检验人员编号',
    emp_name             varchar(255) NOT NULL DEFAULT '' COMMENT '检验人员姓名',
    device_code          varchar(255) NOT NULL DEFAULT '' COMMENT '检测设备编号',
    device_name          varchar(255) NOT NULL DEFAULT '' COMMENT '检测设备名称',
    feature_name         varchar(255) NOT NULL DEFAULT '' COMMENT '参数名称/特性名称',
    feature_unit         varchar(255) NOT NULL DEFAULT '' COMMENT '参数单位/特性单位',
    standard_value       varchar(255) NOT NULL DEFAULT '' COMMENT '参数/特性标准值',
    feature_upper        varchar(255) NOT NULL DEFAULT '' COMMENT '参数/特性上限值',
    feature_lower        varchar(255) NOT NULL DEFAULT '' COMMENT '参数/特性下限值',
    feature_value        varchar(255) NOT NULL DEFAULT '' COMMENT '参数/特性实测值',
    check_no             varchar(255) NOT NULL DEFAULT '' COMMENT '来料检验单号',
    check_result         varchar(255) NOT NULL DEFAULT '' COMMENT '来料检验结果，OK合格/NG不合格',
    check_time           varchar(255) NOT NULL DEFAULT '' COMMENT '检验时间,格式(yyyy-MM-dd HH:mm:ss)',
    sampling_rate        bigint       NOT NULL DEFAULT 0 COMMENT '控制项要求频率',
    limit_update_time    varchar(255) NOT NULL DEFAULT '' COMMENT '上下限更新时间，格式(yyyy-MM-dd HH:mm:ss)',
    vendor_field_desc    varchar(255) NOT NULL DEFAULT '' COMMENT '控制项描述',
    vendor_field_code    varchar(255) NOT NULL DEFAULT '' COMMENT '控制项代码',
    dead_line            varchar(255) NOT NULL DEFAULT '' COMMENT '库存有效日期,格式(yyyy-MM-dd HH:mm:ss)',
    has_push             tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='来料检验数据';

-- 排产数据
CREATE TABLE `supplier_pro_scheduling`
(
    id                     bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no               varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code          varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name          varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    plant_id               varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name             varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    vendor_product_no      varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件号',
    vendor_product_name    varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件名称',
    chery_product_no       varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name     varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    plan_no                varchar(255) NOT NULL DEFAULT '' COMMENT '计划单号',
    manufacture_no         varchar(255) NOT NULL DEFAULT '' COMMENT '生产工单号',
    product_batch_no       varchar(255) NOT NULL DEFAULT '' COMMENT '生产批次号',
    manufacture_num        bigint       NOT NULL DEFAULT 0 COMMENT '批次计划数量',
    manufacture_input_num  bigint       NOT NULL DEFAULT 0 COMMENT '批次投入数量',
    manufacture_output_num bigint       NOT NULL DEFAULT 0 COMMENT '批次产出数量',
    plan_status            varchar(255) NOT NULL DEFAULT '' COMMENT '排产状态 , 0 未生产 1.生产中，2.已完工，3.已取消，4.已终止',
    plan_begin_time        varchar(255) NOT NULL DEFAULT '' COMMENT '计划开始时间 , 时间格式：yyyy-MM-dd HH:mm:ss',
    plan_end_time          varchar(255) NOT NULL DEFAULT '' COMMENT '计划结束时间 , 时间格式：yyyy-MM-dd HH:mm:ss',
    actual_begin_time      varchar(255) NOT NULL DEFAULT '' COMMENT '实际开始时间 , 时间格式：yyyy-MM-dd HH:mm:ss',
    actual_end_time        varchar(255) NOT NULL DEFAULT '' COMMENT '实际结束时间 , 时间格式：yyyy-MM-dd HH:mm:ss',
    has_push               tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time            datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time            datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by              varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by              varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='排产数据';

-- 供应商基础信息
CREATE TABLE `supplier_info`
(
    id                    bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no              varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code         varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name         varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    plant_id              varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name            varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    workshop_id           varchar(255) NOT NULL DEFAULT '' COMMENT '车间代码',
    workshop_name         varchar(255) NOT NULL DEFAULT '' COMMENT '车间名称',
    production_line_id    varchar(255) NOT NULL DEFAULT '' COMMENT '产线代码',
    production_line_name  varchar(255) NOT NULL DEFAULT '' COMMENT '产线名称',
    station_id            varchar(255) NOT NULL DEFAULT '' COMMENT '工位代码',
    station_name          varchar(255) NOT NULL DEFAULT '' COMMENT '工位名称',
    key_station           varchar(255) NOT NULL DEFAULT '' COMMENT '是否关键工位（Y/N）',
    data_update_time      varchar(255) NOT NULL DEFAULT '' COMMENT '供应商修改时间,格式(yyyy-MM-dd HH:mm:ss)',
    production_line_order bigint       NOT NULL DEFAULT 0 COMMENT '产线顺序',
    station_order         bigint       NOT NULL DEFAULT 0 COMMENT '工位顺序',
    vendor_product_no     varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件号',
    vendor_product_name   varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件名称',
    chery_product_no      varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name    varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    has_push              tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time           datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time           datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by             varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by             varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='供应商基础信息';

-- 人员资质信息
CREATE TABLE `supplier_employee`
(
    id                   bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    plant_id             varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name           varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    workshop_id          varchar(255) NOT NULL DEFAULT '' COMMENT '车间代码',
    workshop_name        varchar(255) NOT NULL DEFAULT '' COMMENT '车间名称',
    production_line_id   varchar(255) NOT NULL DEFAULT '' COMMENT '产线代码',
    production_line_name varchar(255) NOT NULL DEFAULT '' COMMENT '产线名称',
    station_id           varchar(255) NOT NULL DEFAULT '' COMMENT '工位代码',
    station_name         varchar(255) NOT NULL DEFAULT '' COMMENT '工位名称',
    operator_id          varchar(255) NOT NULL DEFAULT '' COMMENT '工位人员账号',
    operator_name        varchar(255) NOT NULL DEFAULT '' COMMENT '工位人员姓名',
    have_quantity        varchar(255) NOT NULL DEFAULT '' COMMENT '是否有资质(Y,N)',
    data_update_time     varchar(255) NOT NULL DEFAULT '' COMMENT '供应商修改时间,格式(yyyy-MM-dd HH:mm:ss)',
    position_id          varchar(255) NOT NULL DEFAULT '' COMMENT '岗位代码',
    position_name        varchar(255) NOT NULL DEFAULT '' COMMENT '岗位名称',
    qualification_level  varchar(255) NOT NULL DEFAULT '' COMMENT '资质等级(Level_4，Level_3，Level_2, Level_1)',
    check_in_time        varchar(255) NOT NULL DEFAULT '' COMMENT '资质获取时间',
    check_out_time       varchar(255) NOT NULL DEFAULT '' COMMENT '资质失去时间',
    has_push             tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='人员资质信息';

-- BOM主数据
CREATE TABLE `supplier_bom`
(
    id                  bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no            varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code       varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name       varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    bom_code            varchar(255) NOT NULL DEFAULT '' COMMENT 'BOM编码',
    bom_name            varchar(255) NOT NULL DEFAULT '' COMMENT 'BOM名称',
    bom_version         varchar(255) NOT NULL DEFAULT '' COMMENT 'BOM版本',
    chery_product_no    varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name  varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    vendor_product_no   varchar(255) NOT NULL DEFAULT '' COMMENT '供应商父件编码',
    vendor_product_name varchar(255) NOT NULL DEFAULT '' COMMENT '供应商父件名称',
    vendor_product_type varchar(255) NOT NULL DEFAULT '' COMMENT '父件类型(成品, 半成品)',
    material_unit       varchar(255) NOT NULL DEFAULT '' COMMENT '父件单位',
    sub_material_code   varchar(255) NOT NULL DEFAULT '' COMMENT '子件编码',
    sub_material_name   varchar(255) NOT NULL DEFAULT '' COMMENT '子件名称',
    sub_material_type   varchar(255) NOT NULL DEFAULT '' COMMENT '子件类型(半成品, 原材料)',
    sub_material_unit   varchar(255) NOT NULL DEFAULT '' COMMENT '子件单位',
    sub_material_quota  bigint(11)   NOT NULL DEFAULT 0 COMMENT '子件用量',
    data_update_time    varchar(255) NOT NULL DEFAULT '' COMMENT 'BOM变更时间,格式(yyyy-MM-dd HH:mm:ss)',
    has_push            tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time         datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time         datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by           varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by           varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='BOM主数据';

-- 过程控制项质量数据
CREATE TABLE `supplier_pro_cps`
(
    id                   bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    vendor_product_no    varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件号',
    vendor_product_name  varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件名称',
    vendor_product_sn    varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成SN码',
    vendor_product_batch varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成批次号',
    chery_product_no     varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name   varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    chery_product_sn     varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞SN码',
    product_batch_no     varchar(255) NOT NULL DEFAULT '' COMMENT '生产批次号',
    manufacture_no       varchar(255) NOT NULL DEFAULT '' COMMENT '生产工单号',
    plant_id             varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name           varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    workshop_id          varchar(255) NOT NULL DEFAULT '' COMMENT '车间代码',
    workshop_name        varchar(255) NOT NULL DEFAULT '' COMMENT '车间名称',
    production_line_id   varchar(255) NOT NULL DEFAULT '' COMMENT '产线代码',
    production_line_name varchar(255) NOT NULL DEFAULT '' COMMENT '产线名称',
    station_id           varchar(255) NOT NULL DEFAULT '' COMMENT '工位代码',
    station_name         varchar(255) NOT NULL DEFAULT '' COMMENT '工位名称',
    emp_code             varchar(255) NOT NULL DEFAULT '' COMMENT '工位人员编号',
    emp_name             varchar(255) NOT NULL DEFAULT '' COMMENT '工位人员姓名',
    vendor_field_name    varchar(255) NOT NULL DEFAULT '' COMMENT '控制项名称',
    vendor_field_code    varchar(255) NOT NULL DEFAULT '' COMMENT '控制项代码',
    gather_spot          varchar(255) NOT NULL DEFAULT '' COMMENT '控制项点位',
    sampling_rate        bigint       NOT NULL DEFAULT 0 COMMENT '控制项要求频率',
    limit_update_time    varchar(255) NOT NULL DEFAULT '' COMMENT '上下限更新时间,格式(yyyy-MM-dd HH:mm:ss)',
    vendor_field_desc    varchar(255) NOT NULL DEFAULT '' COMMENT '控制项描述',
    carrier_code         varchar(255) NOT NULL DEFAULT '' COMMENT '载体编码',
    intput_qty           bigint       NOT NULL DEFAULT 0 COMMENT '投入数量',
    ftt_qty              bigint       NOT NULL DEFAULT 0 COMMENT '一次合格数量',
    parameter            varchar(255) NOT NULL DEFAULT '' COMMENT '参数 , 是传Y，否传N',
    characteristic       varchar(255) NOT NULL DEFAULT '' COMMENT '特性 , 是传Y，否传N',
    cc                   varchar(255) NOT NULL DEFAULT '' COMMENT 'CC项 , 是传Y，否传N',
    sc                   varchar(255) NOT NULL DEFAULT '' COMMENT 'SC项 , 是传Y，否传N',
    spc                  varchar(255) NOT NULL DEFAULT '' COMMENT 'SPC , 是传Y，否传N',
    standard_value       varchar(255) NOT NULL DEFAULT '' COMMENT '控制项标准值',
    upper_limit          bigint       NOT NULL DEFAULT 0 COMMENT '控制项上限',
    lower_limit          bigint       NOT NULL DEFAULT 0 COMMENT '控制项下限',
    decimal_value        bigint       NOT NULL DEFAULT 0 COMMENT '控制项实测值',
    unit_cn              varchar(255) NOT NULL DEFAULT '' COMMENT '控制项值的单位名称-中文',
    unit_en              varchar(255) NOT NULL DEFAULT '' COMMENT '控控制项单位英文',
    check_result         varchar(255) NOT NULL DEFAULT '' COMMENT '检测结果',
    detection_mode       varchar(255) NOT NULL DEFAULT '' COMMENT '在线检测(inline,offline,both) , 可选项: inline - 在生产线上进行检测. offline - 从生产线上拿下来进行检测. both - inline 和 offline 同时存在',
    work_shift           varchar(255) NOT NULL DEFAULT '' COMMENT '班次(白班，晚班，中班)',
    collect_time         varchar(255) NOT NULL DEFAULT '' COMMENT '采集时间,格式(yyyy-MM-dd HH:mm:ss)',
    check_mode           varchar(255) NOT NULL DEFAULT '' COMMENT '检测方式(人工,设备)',
    device_code          varchar(255) NOT NULL DEFAULT '' COMMENT '检测设备编号',
    device_name          varchar(255) NOT NULL DEFAULT '' COMMENT '检测设备名称',
    has_push             tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='过程控制项质量数据';

-- 生产过程数据
CREATE TABLE `supplier_pro_data`
(
    id                        bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no                  varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code             varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name             varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    plant_id                  varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name                varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    workshop_id               varchar(255) NOT NULL DEFAULT '' COMMENT '车间代码',
    workshop_name             varchar(255) NOT NULL DEFAULT '' COMMENT '车间名称',
    production_line_id        varchar(255) NOT NULL DEFAULT '' COMMENT '产线代码',
    production_line_name      varchar(255) NOT NULL DEFAULT '' COMMENT '产线名称',
    station_id                varchar(255) NOT NULL DEFAULT '' COMMENT '工位代码',
    station_name              varchar(255) NOT NULL DEFAULT '' COMMENT '工位名称',
    emp_code                  varchar(255) NOT NULL DEFAULT '' COMMENT '工位人员编号',
    emp_name                  varchar(255) NOT NULL DEFAULT '' COMMENT '工位人员姓名',
    vendor_product_name       varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件名称',
    vendor_product_no         varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件号',
    vendor_product_batch      varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成批次号',
    vendor_product_sn         varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成SN码',
    sub_prod_no               varchar(255) NOT NULL DEFAULT '' COMMENT '子件编码',
    sub_prod_name             varchar(255) NOT NULL DEFAULT '' COMMENT '子件名称',
    sub_batch_no              varchar(255) NOT NULL DEFAULT '' COMMENT '子件批次号',
    child_package_info        varchar(255) NOT NULL DEFAULT '' COMMENT '子件分包号',
    sub_prod_num              bigint       NOT NULL DEFAULT 0 COMMENT '子件扣料数量',
    sub_prod_sn               varchar(255) NOT NULL DEFAULT '' COMMENT '子件SN码',
    child_source              varchar(255) NOT NULL DEFAULT '' COMMENT '子件物料来源',
    sub_supplier_code         varchar(255) NOT NULL DEFAULT '' COMMENT '分供方代码',
    sub_supplier_name         varchar(255) NOT NULL DEFAULT '' COMMENT '分供方名称',
    chery_product_no          varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name        varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    chery_product_sn          varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞SN码',
    manufacture_no            varchar(255) NOT NULL DEFAULT '' COMMENT '生产工单号',
    product_batch_no          varchar(255) NOT NULL DEFAULT '' COMMENT '生产批次号',
    work_shift                varchar(255) NOT NULL DEFAULT '' COMMENT '班次(白班，晚班，中班)',
    material_input_time       varchar(255) NOT NULL DEFAULT '' COMMENT '进工位的时间,格式(yyyy-MM-dd HH:mm:ss)',
    material_output_time      varchar(255) NOT NULL DEFAULT '' COMMENT '出工位的时间,格式(yyyy-MM-dd HH:mm:ss)',
    vendor_field_num          varchar(255) NOT NULL DEFAULT '' COMMENT '装配设备编号',
    vendor_field_name         varchar(255) NOT NULL DEFAULT '' COMMENT '装配设备名称',
    instrument_quality_status varchar(255) NOT NULL DEFAULT '' COMMENT '设备判定的质量状态 , 合格与否，NG不合适 OK合适',
    manual_quality_status     varchar(255) NOT NULL DEFAULT '' COMMENT '人工判定的质量状态, 合格与否，NG不合适 OK合适',
    final_quality_status      varchar(255) NOT NULL DEFAULT '' COMMENT '最终质量状态, 合格与否，NG不合适 OK合适',
    collect_time              varchar(255) NOT NULL DEFAULT '' COMMENT '采集时间,格式(yyyy-MM-dd HH:mm:ss)',
    date_time                 varchar(255) NOT NULL DEFAULT '' COMMENT '子件绑定扫码时间,格式(yyyy-MM-dd HH:mm:ss)',
    parent_hardware_revision  varchar(255) NOT NULL DEFAULT '' COMMENT '父件硬件版本号',
    parent_software_revision  varchar(255) NOT NULL DEFAULT '' COMMENT '父件软件版本号',
    child_hardware_revision   varchar(255) NOT NULL DEFAULT '' COMMENT '子件硬件版本号',
    child_software_revision   varchar(255) NOT NULL DEFAULT '' COMMENT '子件软件版本号',
    has_push                  tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time               datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time               datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by                 varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by                 varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='生产过程数据';

-- 产品一次合格率
CREATE TABLE `supplier_pro_first_passyield`
(
    id                        bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no                  varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code             varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name             varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    vendor_product_no         varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件号',
    vendor_product_name       varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件名称',
    plant_id                  varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name                varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    workshop_id               varchar(255) NOT NULL DEFAULT '' COMMENT '车间代码',
    workshop_name             varchar(255) NOT NULL DEFAULT '' COMMENT '车间名称',
    production_line_id        varchar(255) NOT NULL DEFAULT '' COMMENT '产线代码',
    production_line_name      varchar(255) NOT NULL DEFAULT '' COMMENT '产线名称',
    chery_product_no          varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name        varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    manufacture_no            varchar(255) NOT NULL DEFAULT '' COMMENT '生产工单号',
    product_batch_no          varchar(255) NOT NULL DEFAULT '' COMMENT '生产批次号',
    work_order_number         bigint       NOT NULL DEFAULT 0 COMMENT '批次计划数量:每个批次号对应的计划数量',
    defective_number          bigint       NOT NULL DEFAULT 0 COMMENT '不合格数',
    acceptable_number         bigint       NOT NULL DEFAULT 0 COMMENT '合格数:当班次合格数汇总',
    once_pass_rate_real_value bigint       NOT NULL DEFAULT 0 COMMENT '一次合格率实际值',
    once_pass_rate_tag_value  bigint       NOT NULL DEFAULT 0 COMMENT '一次合格率目标值',
    work_shift                varchar(255) NOT NULL DEFAULT '' COMMENT '班次(白班，晚班，中班)',
    statistical_time          varchar(255) NOT NULL DEFAULT '' COMMENT '生产日期,格式(yyyy-MM-dd HH:mm:ss)',
    date_time                 varchar(255) NOT NULL DEFAULT '' COMMENT '值统计时间,格式(yyyy-MM-dd HH:mm:ss)',
    has_push                  tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time               datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time               datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by                 varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by                 varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='产品一次合格率';

-- 工位一次合格率
CREATE TABLE `supplier_pro_station_first_passyield`
(
    id                        bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no                  varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code             varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name             varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    plant_id                  varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name                varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    workshop_id               varchar(255) NOT NULL DEFAULT '' COMMENT '车间代码',
    workshop_name             varchar(255) NOT NULL DEFAULT '' COMMENT '车间名称',
    production_line_id        varchar(255) NOT NULL DEFAULT '' COMMENT '产线代码',
    production_line_name      varchar(255) NOT NULL DEFAULT '' COMMENT '产线名称',
    station_id                varchar(255) NOT NULL DEFAULT '' COMMENT '工位代码',
    station_name              varchar(255) NOT NULL DEFAULT '' COMMENT '工位名称',
    chery_product_no          varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name        varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    vendor_product_no         varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件号',
    vendor_product_name       varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件名称',
    product_batch_no          varchar(255) NOT NULL DEFAULT '' COMMENT '生产批次号',
    manufacture_no            varchar(255) NOT NULL DEFAULT '' COMMENT '生产工单号',
    work_order_number         bigint       NOT NULL DEFAULT 0 COMMENT '批次计划数量',
    defective_number          bigint       NOT NULL DEFAULT 0 COMMENT '不合格数',
    acceptable_number         bigint       NOT NULL DEFAULT 0 COMMENT '合格数',
    once_pass_rate_real_value bigint       NOT NULL DEFAULT 0 COMMENT '一次合格率实际值',
    once_pass_rate_tag_value  bigint       NOT NULL DEFAULT 0 COMMENT '一次合格率目标值',
    work_shift                varchar(255) NOT NULL DEFAULT '' COMMENT '班次 , 班次如何区分需备注(白班，晚班，中班)',
    statistical_time          varchar(255) NOT NULL DEFAULT '' COMMENT '生产日期,格式(yyyy-MM-dd HH:mm:ss)',
    date_time                 varchar(255) NOT NULL DEFAULT '' COMMENT '值统计时间,格式(yyyy-MM-dd HH:mm:ss)',
    has_push                  tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time               datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time               datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by                 varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by                 varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='工位一次合格率';

-- 设备OEE达成率
CREATE TABLE `supplier_pro_oee_achievement_rate`
(
    id                   bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    plant_id             varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name           varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    workshop_id          varchar(255) NOT NULL DEFAULT '' COMMENT '车间代码',
    workshop_name        varchar(255) NOT NULL DEFAULT '' COMMENT '车间名称',
    production_line_id   varchar(255) NOT NULL DEFAULT '' COMMENT '产线代码',
    production_line_name varchar(255) NOT NULL DEFAULT '' COMMENT '产线名称',
    station_id           varchar(255) NOT NULL DEFAULT '' COMMENT '工位代码',
    station_name         varchar(255) NOT NULL DEFAULT '' COMMENT '工位名称',
    device_id            varchar(255) NOT NULL DEFAULT '' COMMENT '设备代码',
    device_name          varchar(255) NOT NULL DEFAULT '' COMMENT '设备名称',
    chery_product_no     varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name   varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    vendor_product_no    varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件号',
    vendor_product_name  varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件名称',
    product_batch_no     varchar(255) NOT NULL DEFAULT '' COMMENT '生产批次号',
    manufacture_no       varchar(255) NOT NULL DEFAULT '' COMMENT '生产工单号',
    rate                 bigint       NOT NULL DEFAULT 0 COMMENT 'OEE实际值',
    rate_tag_value       bigint       NOT NULL DEFAULT 0 COMMENT 'OEE目标值',
    work_shift           varchar(255) NOT NULL DEFAULT '' COMMENT '班次 , 班次如何区分需备注(白班，晚班，中班)',
    statistical_time     varchar(255) NOT NULL DEFAULT '' COMMENT '生产日期,格式(yyyy-MM-dd HH:mm:ss)',
    date_time            varchar(255) NOT NULL DEFAULT '' COMMENT '值统计时间,格式(yyyy-MM-dd HH:mm:ss)',
    has_push             tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='设备OEE达成率';

-- 缺陷业务数据
CREATE TABLE `supplier_pro_flaw`
(
    id                   bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    plant_id             varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name           varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    workshop_id          varchar(255) NOT NULL DEFAULT '' COMMENT '车间代码',
    workshop_name        varchar(255) NOT NULL DEFAULT '' COMMENT '车间名称',
    production_line_id   varchar(255) NOT NULL DEFAULT '' COMMENT '产线代码',
    production_line_name varchar(255) NOT NULL DEFAULT '' COMMENT '产线名称',
    station_id           varchar(255) NOT NULL DEFAULT '' COMMENT '工位代码',
    station_name         varchar(255) NOT NULL DEFAULT '' COMMENT '工位名称',
    defects_code         varchar(255) NOT NULL DEFAULT '' COMMENT '缺陷代码',
    defects_name         varchar(255) NOT NULL DEFAULT '' COMMENT '缺陷名称',
    class_of_name        varchar(255) NOT NULL DEFAULT '' COMMENT '缺陷分类(外观,尺寸,材料,功能,性能,其他)',
    vendor_product_no    varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件号',
    vendor_product_name  varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件名称',
    vendor_product_batch varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成批次号',
    vendor_product_sn    varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成SN码',
    chery_product_no     varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name   varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    chery_product_sn     varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞SN码',
    product_batch_no     varchar(255) NOT NULL DEFAULT '' COMMENT '生产批次号',
    manufacture_no       varchar(255) NOT NULL DEFAULT '' COMMENT '生产工单号',
    work_shift           varchar(255) NOT NULL DEFAULT '' COMMENT '班次(白班，晚班，中班)',
    numberofdefect       bigint       NOT NULL DEFAULT 0 COMMENT '缺陷件数',
    defects_desc         varchar(255) NOT NULL DEFAULT '' COMMENT '缺陷描述',
    defects_level        varchar(255) NOT NULL DEFAULT '' COMMENT '缺陷等级 , （1.严重、2.一般、3.轻微 ）',
    statistical_time     varchar(255) NOT NULL DEFAULT '' COMMENT '缺陷录入时间,格式(yyyy-MM-dd HH:mm:ss)',
    has_push             tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='缺陷业务数据';

-- 环境业务数据
CREATE TABLE `supplier_pro_environment`
(
    id                    bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no              varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code         varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name         varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    plant_id              varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name            varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    workshop_id           varchar(255) NOT NULL DEFAULT '' COMMENT '车间代码',
    workshop_name         varchar(255) NOT NULL DEFAULT '' COMMENT '车间名称',
    production_line_id    varchar(255) NOT NULL DEFAULT '' COMMENT '产线代码',
    production_line_name  varchar(255) NOT NULL DEFAULT '' COMMENT '产线名称',
    env_indicator_name    varchar(255) NOT NULL DEFAULT '' COMMENT '环境指标名称 , 例如：温度、湿度、洁净度等',
    num_value             bigint       NOT NULL DEFAULT 0 COMMENT '指标实测值 , （最大支持11位整数+5位小数）',
    upper_limit           bigint       NOT NULL DEFAULT 0 COMMENT '上限值 , （最大支持11位整数+5位小数）',
    lower_limit           bigint       NOT NULL DEFAULT 0 COMMENT '下限值 , （最大支持11位整数+5位小数）',
    chinese_unit          varchar(255) NOT NULL DEFAULT '' COMMENT '单位 , 相应的单位名称，如度数',
    equipment_code        varchar(255) NOT NULL DEFAULT '' COMMENT '采集仪器代码 , 环境采集的仪器/工具代码',
    equipment_name        varchar(255) NOT NULL DEFAULT '' COMMENT '采集仪器名称 , 环境采集的仪器/工具名称',
    data_collection_point varchar(255) NOT NULL DEFAULT '' COMMENT '数据采集的点位',
    collect_time          varchar(255) NOT NULL DEFAULT '' COMMENT '数据采集的时间,格式(yyyy-MM-dd HH:mm:ss)',
    has_push              tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time           datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time           datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by             varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by             varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='环境业务数据';

-- 物料主数据
CREATE TABLE `supplier_pro_material_data`
(
    id                       bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no                 varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code            varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name            varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    vendor_product_no        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件号',
    vendor_product_name      varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件名称',
    type                     varchar(255) NOT NULL DEFAULT '' COMMENT '类型(成品,半成品,原材料)',
    vendor_hardware_revision varchar(255) NOT NULL DEFAULT '' COMMENT '供应商零件版本号',
    chery_product_no         varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name       varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    oem_hardware_revision    varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞硬件版本号',
    oem_software_revision    varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞软件版本号',
    oem_model                varchar(255) NOT NULL DEFAULT '' COMMENT '车型',
    oem_project_name         varchar(255) NOT NULL DEFAULT '' COMMENT '项目名称',
    launched                 varchar(255) NOT NULL DEFAULT '' COMMENT '是否SOP(Y/N)',
    date_time                varchar(255) NOT NULL DEFAULT '' COMMENT '数据同步执行时间,格式(yyyy-MM-dd HH:mm:ss)',
    plant_id                 varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name               varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    procurement_type         varchar(255) NOT NULL DEFAULT '' COMMENT '芯片采购类型(AVAP,CS,CMcontro)',
    mpn_code                 varchar(255) NOT NULL DEFAULT '' COMMENT '芯片MPN标识码',
    mpn_name                 varchar(255) NOT NULL DEFAULT '' COMMENT '芯片MPN标识名称',
    valid_days               varchar(255) NOT NULL DEFAULT '' COMMENT '物料有效期（天）',
    has_push                 tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time              datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time              datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by                varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by                varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='物料主数据';

-- OEE时间明细
CREATE TABLE `supplier_pro_oee_time_details`
(
    id                   bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    rec_id               varchar(255) NOT NULL DEFAULT '' COMMENT '记录ID',
    supplier_code        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    plant_id             varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    plant_name           varchar(255) NOT NULL DEFAULT '' COMMENT '工厂名称',
    workshop_id          varchar(255) NOT NULL DEFAULT '' COMMENT '车间代码',
    workshop_name        varchar(255) NOT NULL DEFAULT '' COMMENT '车间名称',
    production_line_id   varchar(255) NOT NULL DEFAULT '' COMMENT '产线代码',
    production_line_name varchar(255) NOT NULL DEFAULT '' COMMENT '产线名称',
    station_id           varchar(255) NOT NULL DEFAULT '' COMMENT '工位代码',
    station_name         varchar(255) NOT NULL DEFAULT '' COMMENT '工位名称',
    device_type          varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备分类(1设备；2模具；)',
    device_id            varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备代码',
    device_name          varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备名称',
    type                 varchar(255) NOT NULL DEFAULT '' COMMENT '大类(1计划工作,2计划停机,3非计划停机)',
    sub_type             varchar(255) NOT NULL DEFAULT '' COMMENT '小类(1工作；2维修；3保养；4停机)',
    sub_type_description varchar(255) NOT NULL DEFAULT '' COMMENT '小类描述',
    plan_begin_time      varchar(255) NOT NULL DEFAULT '' COMMENT '计划开始时间,格式(yyyy-MM-dd HH:mm:ss)',
    plan_end_time        varchar(255) NOT NULL DEFAULT '' COMMENT '计划结束时间,格式(yyyy-MM-dd HH:mm:ss)',
    start_time           varchar(255) NOT NULL DEFAULT '' COMMENT '实际开始时间,格式(yyyy-MM-dd HH:mm:ss)',
    end_time             varchar(255) NOT NULL DEFAULT '' COMMENT '实际结束时间,格式(yyyy-MM-dd HH:mm:ss)',
    has_push             tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='OEE时间明细';

-- 附件类数据
CREATE TABLE `supplier_pro_attachment_data`
(
    id                   bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    type                 varchar(255) NOT NULL DEFAULT '' COMMENT '数据类型(1产前管理；2人员资质；3监控视频)',
    file_name            varchar(255) NOT NULL DEFAULT '' COMMENT '文件名',
    file_url             varchar(255) NOT NULL DEFAULT '' COMMENT '图文地址',
    date_time            varchar(255) NOT NULL DEFAULT '' COMMENT '生成时间,格式(yyyy-MM-dd HH:mm:ss)',
    production_line_name varchar(255) NOT NULL DEFAULT '' COMMENT '产线名称',
    production_line_id   varchar(255) NOT NULL DEFAULT '' COMMENT '产线代码',
    station_name         varchar(255) NOT NULL DEFAULT '' COMMENT '工位名称',
    station_id           varchar(255) NOT NULL DEFAULT '' COMMENT '工位代码',
    device_name          varchar(255) NOT NULL DEFAULT '' COMMENT '设备名称',
    device_id            varchar(255) NOT NULL DEFAULT '' COMMENT '设备代码',
    vendor_product_no    varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件号',
    vendor_product_name  varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件名称',
    chery_product_no     varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name   varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    vendor_product_sn    varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成SN码',
    has_push             tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='附件类数据';

-- 工艺装备
CREATE TABLE `supplier_pro_process_equipment`
(
    id                      bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no                varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code           varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name           varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    device_type             varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备分类(1,2,3,4)',
    device_id               varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备代码',
    device_name             varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备名称',
    manufacturer            varchar(255) NOT NULL DEFAULT '' COMMENT '生产厂家',
    model_number            varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备型号',
    production_date         varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备制造日期',
    material                varchar(255) NOT NULL DEFAULT '' COMMENT '主要材质',
    current_location        varchar(255) NOT NULL DEFAULT '' COMMENT '当前存放地点',
    device_status           varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备状态(报废,正常)',
    design_life_units       varchar(255) NOT NULL DEFAULT '' COMMENT '设计寿命单位',
    design_life_value       bigint       NOT NULL DEFAULT 0 COMMENT '设计寿命',
    current_usage_count     bigint       NOT NULL DEFAULT 0 COMMENT '当前剩余寿命',
    effective_days          bigint       NOT NULL DEFAULT 0 COMMENT '每月有效工作天数',
    max_process_hours       bigint       NOT NULL DEFAULT 0 COMMENT '每天最大加工工时',
    regular_process_hours   bigint       NOT NULL DEFAULT 0 COMMENT '每天常规加工工时',
    device_start_date       varchar(255) NOT NULL DEFAULT '' COMMENT '投产日期',
    device_end_date         varchar(255) NOT NULL DEFAULT '' COMMENT '报废停产日期',
    machine_costs           bigint       NOT NULL DEFAULT 0 COMMENT '单台设备投资金额',
    machine_purchase_period bigint       NOT NULL DEFAULT 0 COMMENT '设备购买周期',
    machine_type            varchar(255) NOT NULL DEFAULT '' COMMENT '设备类型(1,2,3,4,5,6,7,8,9)',
    unitper_hour            bigint       NOT NULL DEFAULT 0 COMMENT '单位小时产出',
    cavity_count            bigint       NOT NULL DEFAULT 0 COMMENT '穴腔数量',
    mold_size               varchar(255) NOT NULL DEFAULT '' COMMENT '模具尺寸规格',
    copy_mold_costs         bigint       NOT NULL DEFAULT 0 COMMENT '模具复制模费用',
    overhaul_count          bigint       NOT NULL DEFAULT 0 COMMENT '模具大修次数',
    calibration_date        varchar(255) NOT NULL DEFAULT '' COMMENT '检具最近校准日期',
    calibration_due_days    varchar(255) NOT NULL DEFAULT '' COMMENT '检具校准到期天数',
    unit_type               varchar(255) NOT NULL DEFAULT '' COMMENT '检具检测单位',
    has_push                tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time             datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time             datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by               varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by               varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='工艺装备';

-- 工艺
CREATE TABLE `supplier_pro_process`
(
    id                      bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no                varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code           varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name           varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    chery_product_no        varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件号',
    chery_product_name      varchar(255) NOT NULL DEFAULT '' COMMENT '奇瑞零件名称',
    vendor_product_no       varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件号',
    vendor_product_name     varchar(255) NOT NULL DEFAULT '' COMMENT '供应商总成零件名称',
    tech_code               varchar(255) NOT NULL DEFAULT '' COMMENT '工艺编码',
    tech_name               varchar(255) NOT NULL DEFAULT '' COMMENT '工艺名称',
    tech_version            varchar(255) NOT NULL DEFAULT '' COMMENT '工艺版本',
    is_enabled              varchar(255) NOT NULL DEFAULT '' COMMENT '是否启用',
    max_processing_capacity varchar(255) NOT NULL DEFAULT '' COMMENT '最大加工能力',
    promise_ratio           bigint       NOT NULL DEFAULT 0 COMMENT '承诺比例',
    make_same_period        bigint       NOT NULL DEFAULT 0 COMMENT '按滚动预测需求的供货周期',
    has_push                tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time             datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time             datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by               varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by               varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='工艺';
-- 工艺子集
CREATE TABLE `supplier_pro_process_child`
(
    id                    bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no              varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    parent_id             bigint       NOT NULL DEFAULT 0 COMMENT '父级id',
    process_code          varchar(255) NOT NULL DEFAULT '' COMMENT '工序编码',
    process_name          varchar(255) NOT NULL DEFAULT '' COMMENT '工序名称',
    process_order         bigint       NOT NULL DEFAULT 0 COMMENT '工序顺序号',
    rhythm                bigint       NOT NULL DEFAULT 0 COMMENT '工序节拍',
    process_supplier_code varchar(255) NOT NULL DEFAULT '' COMMENT '委外工序供应商编码',
    process_supplier_name varchar(255) NOT NULL DEFAULT '' COMMENT '委外工序供应商名称',
    process_location      varchar(255) NOT NULL DEFAULT '' COMMENT '工序所在地',
    process_cycle_time    bigint       NOT NULL DEFAULT 0 COMMENT '工序间流转时间',
    process_yield_target  bigint       NOT NULL DEFAULT 0 COMMENT '工序良率目标',
    insert_time           datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time           datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by             varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by             varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='工艺子集（关联supplier_pro_process）';
-- 工艺子子集
CREATE TABLE `supplier_pro_process_grand_child`
(
    id          bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no    varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    parent_id   bigint       NOT NULL DEFAULT 0 COMMENT '父级id',
    device_type varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备分类',
    device_id   varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备代码',
    device_name varchar(255) NOT NULL DEFAULT '' COMMENT '工艺装备名称',
    insert_time datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by   varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by   varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='工艺子子集（关联supplier_pro_process_child）';


-- ---------------------------------- 计划物流二期-------------------------------

-- 整车月度生产计划
CREATE TABLE `supplier_pro_planing`
(
    id               bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no         varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    release_edition  varchar(255) NULL     DEFAULT NULL COMMENT '需求发布版次:唯一版次ID ',
    models           varchar(255) NULL     DEFAULT NULL COMMENT '车型',
    salse_department varchar(255) NULL     DEFAULT NULL COMMENT '销售单位',
    type             varchar(255) NULL     DEFAULT NULL COMMENT '类型',
    assembly         varchar(255) NULL     DEFAULT NULL COMMENT '动力总成',
    pattern          varchar(255) NULL     DEFAULT NULL COMMENT '版型',
    omterior         varchar(255) NULL     DEFAULT NULL COMMENT '内饰',
    material_code    varchar(255) NULL     DEFAULT NULL COMMENT '物料号',
    start_month      varchar(255) NULL     DEFAULT NULL COMMENT '起始月份-格式：yyyy-MM',
    quantity1        bigint       NULL     DEFAULT NULL COMMENT '数量1',
    quantity2        bigint       NULL     DEFAULT NULL COMMENT '数量2',
    quantity3        bigint       NULL     DEFAULT NULL COMMENT '数量3',
    quantity4        bigint       NULL     DEFAULT NULL COMMENT '数量4',
    quantity5        bigint       NULL     DEFAULT NULL COMMENT '数量5',
    quantity6        bigint       NULL     DEFAULT NULL COMMENT '数量6',
    plant            varchar(255) NULL     DEFAULT NULL COMMENT '工厂',
    models2          varchar(255) NULL     DEFAULT NULL COMMENT '二级车型代码-细',
    displacement     varchar(255) NULL     DEFAULT NULL COMMENT '排量',
    create_by_user   varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time      varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user   varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time      varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete        bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version          bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time      datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time      datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by        varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by        varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='整车月度生产计划';

-- M+6月物料需求计划
CREATE TABLE `supplier_mrp_month`
(
    id                   bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    release_edition      varchar(255) NULL     DEFAULT NULL COMMENT '需求发布版次:M+6月物料需求计划风险确认接口对应需求版次，需求ID',
    material_code        varchar(255) NULL     DEFAULT NULL COMMENT '零件号:奇瑞零件号',
    material_description varchar(255) NULL     DEFAULT NULL COMMENT '零件名称',
    plant_id             varchar(255) NULL     DEFAULT NULL COMMENT '工厂代码',
    plant_name           varchar(255) NULL     DEFAULT NULL COMMENT '工厂名称',
    start_month          varchar(255) NULL     DEFAULT NULL COMMENT '起始月份-格式：yyyy-MM ',
    quantity_demand1     bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量1',
    quantity_demand2     bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量2',
    quantity_demand3     bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量3',
    quantity_demand4     bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量4',
    quantity_demand5     bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量5',
    quantity_demand6     bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量6',
    quantity_demand7     bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量7',
    quantity_demand8     bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量8',
    quantity_demand9     bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量9',
    quantity_demand10    bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量10',
    quantity_demand11    bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量11',
    quantity_demand12    bigint(11)   NULL     DEFAULT NULL COMMENT '需求数量12',
    is_update            varchar(255) NULL     DEFAULT NULL COMMENT '当文件夹数据发生变更时(更新需求=1/否则=0)',
    create_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time          varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time          varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete            bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version              bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='M+6月物料需求计划';

-- 日物料需求计划
CREATE TABLE `supplier_mrp_date`
(
    id                   bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    release_edition      varchar(255) NULL     DEFAULT NULL COMMENT '需求发布版次:用于日物料需求计划风险确认接口反馈数据的ID',
    material_code        varchar(255) NULL     DEFAULT NULL COMMENT '零件号:奇瑞零件号',
    material_description varchar(255) NULL     DEFAULT NULL COMMENT '零件名称 ',
    plant_id             varchar(255) NULL     DEFAULT NULL COMMENT '工厂代码',
    plant_name           varchar(255) NULL     DEFAULT NULL COMMENT '工厂名称',
    start_date           varchar(255) NULL     DEFAULT NULL COMMENT '起始日期-格式：yyyy-MM-dd',
    quantity_demand1     bigint       NULL     DEFAULT NULL COMMENT '需求数量1',
    quantity_demand2     bigint       NULL     DEFAULT NULL COMMENT '需求数量2',
    quantity_demand3     bigint       NULL     DEFAULT NULL COMMENT '需求数量3',
    quantity_demand4     bigint       NULL     DEFAULT NULL COMMENT '需求数量4',
    quantity_demand5     bigint       NULL     DEFAULT NULL COMMENT '需求数量5',
    quantity_demand6     bigint       NULL     DEFAULT NULL COMMENT '需求数量6',
    quantity_demand7     bigint       NULL     DEFAULT NULL COMMENT '需求数量7',
    quantity_demand8     bigint       NULL     DEFAULT NULL COMMENT '需求数量8',
    quantity_demand9     bigint       NULL     DEFAULT NULL COMMENT '需求数量9',
    quantity_demand10    bigint       NULL     DEFAULT NULL COMMENT '需求数量10',
    quantity_demand11    bigint       NULL     DEFAULT NULL COMMENT '需求数量11',
    quantity_demand12    bigint       NULL     DEFAULT NULL COMMENT '需求数量12',
    quantity_demand13    bigint       NULL     DEFAULT NULL COMMENT '需求数量13',
    quantity_demand14    bigint       NULL     DEFAULT NULL COMMENT '需求数量14',
    quantity_demand15    bigint       NULL     DEFAULT NULL COMMENT '需求数量15',
    quantity_demand16    bigint       NULL     DEFAULT NULL COMMENT '需求数量16',
    quantity_demand17    bigint       NULL     DEFAULT NULL COMMENT '需求数量17',
    quantity_demand18    bigint       NULL     DEFAULT NULL COMMENT '需求数量18',
    quantity_demand19    bigint       NULL     DEFAULT NULL COMMENT '需求数量19',
    quantity_demand20    bigint       NULL     DEFAULT NULL COMMENT '需求数量20',
    quantity_demand21    bigint       NULL     DEFAULT NULL COMMENT '需求数量21',
    quantity_demand22    bigint       NULL     DEFAULT NULL COMMENT '需求数量22',
    quantity_demand23    bigint       NULL     DEFAULT NULL COMMENT '需求数量23',
    quantity_demand24    bigint       NULL     DEFAULT NULL COMMENT '需求数量24',
    quantity_demand25    bigint       NULL     DEFAULT NULL COMMENT '需求数量25',
    quantity_demand26    bigint       NULL     DEFAULT NULL COMMENT '需求数量26',
    quantity_demand27    bigint       NULL     DEFAULT NULL COMMENT '需求数量27',
    quantity_demand28    bigint       NULL     DEFAULT NULL COMMENT '需求数量28',
    quantity_demand29    bigint       NULL     DEFAULT NULL COMMENT '需求数量29',
    quantity_demand30    bigint       NULL     DEFAULT NULL COMMENT '需求数量30',
    quantity_demand31    bigint       NULL     DEFAULT NULL COMMENT '需求数量31',
    is_update            varchar(255) NULL     DEFAULT NULL COMMENT '当文件夹数据发生变更时(更新需求=1/否则=0)',
    create_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time          varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time          varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete            bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version              bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='日物料需求计划';

-- 计划协议
CREATE TABLE `supplier_sa_week`
(
    id                   bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    schedule_agreement   varchar(255) NULL     DEFAULT NULL COMMENT '计划协议号',
    serial_number        varchar(255) NULL     DEFAULT NULL COMMENT '行项目号',
    material_code        varchar(255) NULL     DEFAULT NULL COMMENT '零件号:奇瑞零件号',
    material_description varchar(255) NULL     DEFAULT NULL COMMENT '零件名称',
    purchasing_group     varchar(255) NULL     DEFAULT NULL COMMENT '采购组',
    plant_id             varchar(255) NULL     DEFAULT NULL COMMENT '工厂代码',
    quantity_demand      bigint       NULL     DEFAULT NULL COMMENT '需求数量',
    date_received        varchar(255) NULL     DEFAULT NULL COMMENT '交货日期-格式：yyyy-MM-dd',
    create_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time          varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time          varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete            bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version              bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='计划协议';

-- 采购订单
CREATE TABLE `supplier_po`
(
    id                   bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    purchase_order       varchar(255) NULL     DEFAULT NULL COMMENT '采购订单号',
    serial_number        varchar(255) NULL     DEFAULT NULL COMMENT '行项目号',
    plant_id             varchar(255) NULL     DEFAULT NULL COMMENT '工厂代码',
    plant_name           varchar(255) NULL     DEFAULT NULL COMMENT '工厂名称',
    voucher_date         varchar(255) NULL     DEFAULT NULL COMMENT '凭证日期-格式：yyyy-MM-dd',
    purchaser            varchar(255) NULL     DEFAULT NULL COMMENT '需方联系人',
    supplier             varchar(255) NULL     DEFAULT NULL COMMENT '供方联系人',
    material_code        varchar(255) NULL     DEFAULT NULL COMMENT '物料编码',
    material_description varchar(255) NULL     DEFAULT NULL COMMENT '物料描述',
    quantity_demand      bigint       NULL     DEFAULT NULL COMMENT '需求数量',
    material_unit        varchar(255) NULL     DEFAULT NULL COMMENT '物料单位',
    delivery_date        varchar(255) NULL     DEFAULT NULL COMMENT '交货日期-格式：yyyy-MM-dd',
    delivery_place       varchar(255) NULL     DEFAULT NULL COMMENT '交货地点',
    quantity_delivery    bigint       NULL     DEFAULT NULL COMMENT '到货数量',
    note                 varchar(255) NULL     DEFAULT NULL COMMENT '备注:含批次号信息',
    item_type            varchar(255) NULL     DEFAULT NULL COMMENT '项目类别文本',
    trade_terms          varchar(255) NULL     DEFAULT NULL COMMENT '国际贸易条件',
    country              varchar(255) NULL     DEFAULT NULL COMMENT '出口国家',
    batch                varchar(255) NULL     DEFAULT NULL COMMENT '批次',
    create_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time          varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time          varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete            bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version              bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='采购订单';

-- 过焊装未过总装
CREATE TABLE `supplier_pro_hschedul`
(
    id                   bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    models               varchar(255) NULL     DEFAULT NULL COMMENT '车型',
    vin                  varchar(255) NULL     DEFAULT NULL COMMENT 'VIN',
    production_line_id   varchar(255) NULL     DEFAULT NULL COMMENT '产线代码',
    production_line_name varchar(255) NULL     DEFAULT NULL COMMENT '产线名称',
    material_code        varchar(255) NULL     DEFAULT NULL COMMENT '物料编码',
    material_description varchar(255) NULL     DEFAULT NULL COMMENT '物料描述',
    production_type      varchar(255) NULL     DEFAULT NULL COMMENT '生产备注(报工类型)',
    on_line_time         varchar(255) NULL     DEFAULT NULL COMMENT '上线日期时间-格式：yyyy-MM-dd HH:mm:ss',
    create_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time          varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time          varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete            bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version              bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='过焊装未过总装';

-- 过涂装未过总装
CREATE TABLE `supplier_pro_tschedul`
(
    id                   bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    models               varchar(255) NULL     DEFAULT NULL COMMENT '车型',
    vin                  varchar(255) NULL     DEFAULT NULL COMMENT 'VIN',
    production_line_id   varchar(255) NULL     DEFAULT NULL COMMENT '产线代码',
    production_line_name varchar(255) NULL     DEFAULT NULL COMMENT '产线名称',
    material_code        varchar(255) NULL     DEFAULT NULL COMMENT '物料编码',
    material_description varchar(255) NULL     DEFAULT NULL COMMENT '物料描述',
    on_line_time         varchar(255) NULL     DEFAULT NULL COMMENT '上线日期时间-格式：yyyy-MM-dd HH:mm:ss',
    final_workshop       varchar(255) NULL     DEFAULT NULL COMMENT '总装车间',
    final_onLine_time    varchar(255) NULL     DEFAULT NULL COMMENT '总装上线日期时间-格式：yyyy-MM-dd HH:mm:ss',
    create_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time          varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time          varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete            bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version              bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='过涂装未过总装';

-- 排序供货
CREATE TABLE `supplier_pro_cschedul`
(
    id                     bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no               varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    models                 varchar(255) NULL     DEFAULT NULL COMMENT '车型',
    vin                    varchar(255) NULL     DEFAULT NULL COMMENT 'VIN',
    production_line_id     varchar(255) NULL     DEFAULT NULL COMMENT '产线代码',
    production_line_name   varchar(255) NULL     DEFAULT NULL COMMENT '产线名称',
    material_code          varchar(255) NULL     DEFAULT NULL COMMENT '物料编码',
    material_description   varchar(255) NULL     DEFAULT NULL COMMENT '物料描述',
    sort_date              varchar(255) NULL     DEFAULT NULL COMMENT '排序日期:时间格式 yyyy-MM-dd',
    sort_time              varchar(255) NULL     DEFAULT NULL COMMENT '排序时间:时间格式 HH:mm:ss',
    on_line_date           varchar(255) NULL     DEFAULT NULL COMMENT '上线日期',
    on_line_time           varchar(255) NULL     DEFAULT NULL COMMENT '上线时间:时间格式：20:34:12',
    model_category         varchar(255) NULL     DEFAULT NULL COMMENT '车型类别',
    assembly_material_code varchar(255) NULL     DEFAULT NULL COMMENT '动力总成物料号',
    motor_material_code    varchar(255) NULL     DEFAULT NULL COMMENT '发动机物料号',
    plant                  varchar(255) NULL     DEFAULT NULL COMMENT '工厂',
    create_by_user         varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time            varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user         varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time            varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete              bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version                bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time            datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time            datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by              varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by              varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='排序供货';

-- 看板配送单
CREATE TABLE `supplier_del_state`
(
    id                    bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no              varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    delivery_number       varchar(255) NULL     DEFAULT NULL COMMENT '配送单号',
    serial_number         varchar(255) NULL     DEFAULT NULL COMMENT '行项目号',
    serial_srate          varchar(255) NULL     DEFAULT NULL COMMENT '配送单状态',
    material_code         varchar(255) NULL     DEFAULT NULL COMMENT '零件号',
    material_description  varchar(255) NULL     DEFAULT NULL COMMENT '零件名称',
    plant_id              varchar(255) NULL     DEFAULT NULL COMMENT '工厂代码',
    receiving_crossings   varchar(255) NULL     DEFAULT NULL COMMENT '收货道口',
    quantity_delivery     bigint       NULL     DEFAULT NULL COMMENT '数量',
    data_create_time      varchar(255) NULL     DEFAULT NULL COMMENT '创建时间-格式：yyyy-MM-dd HH:mm:ss',
    supplier_receive_time varchar(255) NULL     DEFAULT NULL COMMENT '供应商接收时间-格式：yyyy-MM-dd HH:mm:ss',
    road_shipped_time     varchar(255) NULL     DEFAULT NULL COMMENT '道口发货时间-格式：yyyy-MM-dd HH:mm:ss',
    road_receive_time     varchar(255) NULL     DEFAULT NULL COMMENT '道口收货时间-格式：yyyy-MM-dd HH:mm:ss',
    last_requrie_time     varchar(255) NULL     DEFAULT NULL COMMENT '要求到货时间-格式：yyyy-MM-dd HH:mm:ss',
    create_by_user        varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time           varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user        varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time           varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete             bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version               bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time           datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time           datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by             varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by             varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='看板配送单';

-- 退货单
CREATE TABLE `supplier_return`
(
    id                   bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    return_number        varchar(255) NULL     DEFAULT NULL COMMENT '退货单号',
    serial_number        varchar(255) NULL     DEFAULT NULL COMMENT '行项目号',
    serial_srate         varchar(255) NULL     DEFAULT NULL COMMENT '退货单状态',
    pick_up_location     varchar(255) NULL     DEFAULT NULL COMMENT '取货地',
    demand_pickup_time   varchar(255) NULL     DEFAULT NULL COMMENT '需求取货时间:格式：yyyy-MM-dd HH:mm:ss',
    pick_up_crossings    varchar(255) NULL     DEFAULT NULL COMMENT '取货道口',
    feedback             varchar(255) NULL     DEFAULT NULL COMMENT '反馈信息:供应商在LES系统反馈的信息',
    plant                varchar(255) NULL     DEFAULT NULL COMMENT '工厂:示例：1000-奇瑞汽车超一工厂',
    material_code        varchar(255) NULL     DEFAULT NULL COMMENT '零件号',
    material_description varchar(255) NULL     DEFAULT NULL COMMENT '零件名称',
    quantity_delivery    bigint       NULL     DEFAULT NULL COMMENT '数量 ',
    return_type          varchar(255) NULL     DEFAULT NULL COMMENT '退货类型:0-不合格品；1-合格品',
    lot_number           varchar(255) NULL     DEFAULT NULL COMMENT '批次号',
    judge                varchar(255) NULL     DEFAULT NULL COMMENT '判定人',
    return_reason        varchar(255) NULL     DEFAULT NULL COMMENT '退货原因',
    create_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time          varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time          varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete            bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version              bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='退货单';

-- 奇瑞RDC共享库存
CREATE TABLE `supplier_inv_data`
(
    id                   bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    plant_id             varchar(255) NULL     DEFAULT NULL COMMENT '工厂代码',
    plant_name           varchar(255) NULL     DEFAULT NULL COMMENT '工厂名称',
    material_code        varchar(255) NULL     DEFAULT NULL COMMENT '零件号',
    material_description varchar(255) NULL     DEFAULT NULL COMMENT '零件描述',
    quantity_current     bigint       NULL     DEFAULT NULL COMMENT '当前库存数量',
    stock_state          varchar(255) NULL     DEFAULT NULL COMMENT '库存状态',
    data_update_time     varchar(255) NULL     DEFAULT NULL COMMENT '更新时间:格式：yyyy-MM-dd HH:mm:ss',
    create_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time          varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user       varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time          varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete            bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version              bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='奇瑞RDC共享库存';

-- 日MRP状态监控
CREATE TABLE `supplier_mrp_state`
(
    id                          bigint       NOT NULL AUTO_INCREMENT COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no                    varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    plant_id                    varchar(255) NULL     DEFAULT NULL COMMENT '工厂代码',
    plant_name                  varchar(255) NULL     DEFAULT NULL COMMENT '工厂名称',
    demand_srate                varchar(255) NULL     DEFAULT NULL COMMENT '需求状态',
    demand_type                 varchar(255) NULL     DEFAULT NULL COMMENT '需求类型',
    material_code               varchar(255) NULL     DEFAULT NULL COMMENT '零件号:奇瑞零件号',
    material_description        varchar(255) NULL     DEFAULT NULL COMMENT '零件名称',
    summary_sign                varchar(255) NULL     DEFAULT NULL COMMENT '集货标识',
    date_required               varchar(255) NULL     DEFAULT NULL COMMENT '需求日期-格式:yyyy-MM-dd',
    quantity_demand             bigint       NULL     DEFAULT NULL COMMENT '需求数量',
    confirm_time                varchar(255) NULL     DEFAULT NULL COMMENT '需求确认时间',
    creat_quantity              bigint       NULL     DEFAULT NULL COMMENT '已建单数量',
    quantity_delivery           bigint       NULL     DEFAULT NULL COMMENT '已发货数量',
    quantity_receive            bigint       NULL     DEFAULT NULL COMMENT '已收货数量',
    quantity_in_transit         bigint       NULL     DEFAULT NULL COMMENT '在途数量',
    on_time_percentage          bigint       NULL     DEFAULT NULL COMMENT '按时到货比',
    summary_creat_quantity      bigint       NULL     DEFAULT NULL COMMENT '集货件已建单数量',
    summary_quantity_delivery   bigint       NULL     DEFAULT NULL COMMENT '集货件已发货数量',
    summary_quantity_receive    bigint       NULL     DEFAULT NULL COMMENT '集货件已收货数量',
    summary_quantity_in_transit bigint       NULL     DEFAULT NULL COMMENT '集货件已在途数量',
    create_by_user              varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time                 varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user              varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time                 varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete                   bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version                     bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time                 datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time                 datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by                   varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by                   varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='日MRP状态监控';

-- 日MRP预警推移
CREATE TABLE `supplier_mrp_warning`
(
    id                        bigint       NOT NULL COMMENT '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
    batch_no                  varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    plant_id                  varchar(255) NULL     DEFAULT NULL COMMENT '工厂代码',
    material_code             varchar(255) NULL     DEFAULT NULL COMMENT '零件号:奇瑞零件号',
    material_description      varchar(255) NULL     DEFAULT NULL COMMENT '零件名称',
    quantity_current          bigint       NULL     DEFAULT NULL COMMENT '当前库存',
    reckon_date               varchar(255) NULL     DEFAULT NULL COMMENT '需求日期-格式:yyyy-MM-dd',
    quantity_planned          bigint       NULL     DEFAULT NULL COMMENT '需求数量',
    quantity_planned_delivery bigint       NULL     DEFAULT NULL COMMENT '满足数量',
    quantity_in_transit       bigint       NULL     DEFAULT NULL COMMENT '在途数量',
    date_gap                  bigint       NULL     DEFAULT NULL COMMENT '日GAP:日需求数量与满足数量差异',
    inventory_gap             bigint       NULL     DEFAULT NULL COMMENT '库存GAP:库存推移差异',
    create_by_user            varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    create_time               varchar(255) NULL     DEFAULT NULL COMMENT '创建时间',
    update_by_user            varchar(255) NULL     DEFAULT NULL COMMENT '修改人',
    update_time               varchar(255) NULL     DEFAULT NULL COMMENT '修改时间',
    is_delete                 bigint       NULL     DEFAULT NULL COMMENT '是否删除(0:否,1是)(系统默认字段与业务数据无关)',
    version                   bigint       NULL     DEFAULT NULL COMMENT '版本号(系统默认字段与业务数据无关)',
    insert_time               datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time               datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by                 varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by                 varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='日MRP预警推移';

-- M+6月物料需求计划风险确认
CREATE TABLE `supplier_con_mmrp`
(
    id               bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no         varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code    varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    release_edition  varchar(255) NOT NULL DEFAULT '' COMMENT '需求发布版次 , 取自M+6月物料需求计划接口中的需求发布版次，针对与这个版次的需求进行风险反馈',
    material_code    varchar(255) NOT NULL DEFAULT '' COMMENT '零件号 , 奇瑞零件号',
    plant_id         varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    feedback_results varchar(255) NOT NULL DEFAULT '' COMMENT '反馈结果 , 1-异常；0-无异常（匹配峰值需求缺口，如果可满足峰值，即选择无异常）',
    venture_type     varchar(255) NOT NULL DEFAULT '' COMMENT '风险类型 , 当反馈结果=1时，此字段必输 1. 生产节拍不足 2. 人员不足 3. 原材料不足 4. 设备异常 5. 其他',
    venture_specific varchar(255) NOT NULL DEFAULT '' COMMENT '具体风险 , 当反馈结果=1时，此字段必输 描述具体风险',
    measures         varchar(255) NOT NULL DEFAULT '' COMMENT '应对措施 , 当反馈结果=1时，此字段必输 描述具体应对措施',
    start_month      varchar(255) NOT NULL DEFAULT '' COMMENT '起始月份-格式：yyyy-MM',
    quantity_meet1   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量1',
    quantity_meet2   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量2',
    quantity_meet3   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量3',
    quantity_meet4   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量4',
    quantity_meet5   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量5',
    quantity_meet6   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量6',
    quantity_meet7   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量7',
    quantity_meet8   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量8',
    quantity_meet9   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量9',
    quantity_meet10  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量10',
    quantity_meet11  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量11',
    quantity_meet12  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量12',
    has_push         tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time      datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time      datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by        varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by        varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='M+6月物料需求计划风险确认';

-- 日物料需求计划风险确认
CREATE TABLE `supplier_con_date`
(
    id               bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no         varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code    varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    release_edition  varchar(255) NOT NULL DEFAULT '' COMMENT '需求发布版次 , 取自M+6月物料需求计划接口中的需求发布版次，针对与这个版次的需求进行风险反馈',
    material_code    varchar(255) NOT NULL DEFAULT '' COMMENT '零件号 , 奇瑞零件号',
    plant_id         varchar(255) NOT NULL DEFAULT '' COMMENT '工厂代码',
    feedback_results varchar(255) NOT NULL DEFAULT '' COMMENT '反馈结果 , 1-异常；0-无异常（匹配峰值需求缺口，如果可满足峰值，即选择无异常）',
    venture_type     varchar(255) NOT NULL DEFAULT '' COMMENT '风险类型 , 当反馈结果=1时，此字段必输 1. 生产节拍不足 2. 人员不足 3. 原材料不足 4. 设备异常 5. 其他',
    venture_specific varchar(255) NOT NULL DEFAULT '' COMMENT '具体风险 , 当反馈结果=1时，此字段必输 描述具体风险',
    measures         varchar(255) NOT NULL DEFAULT '' COMMENT '应对措施 , 当反馈结果=1时，此字段必输 描述具体应对措施',
    start_month      varchar(255) NOT NULL DEFAULT '' COMMENT '起始月份-格式：yyyy-MM',
    quantity_meet1   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量1, 数量锁定，7日内满足数量必须等于需求数量',
    quantity_meet2   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量2',
    quantity_meet3   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量3',
    quantity_meet4   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量4',
    quantity_meet5   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量5',
    quantity_meet6   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量6',
    quantity_meet7   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量7',
    quantity_meet8   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量8 , 根据物料需求计划接口发布的需求天数，针对企业自身产能情况反馈可满足数量 注：目前发送数据为滚动12天的数据，13至31天的字段为预留，未来可能会增加至31天',
    quantity_meet9   bigint       NOT NULL DEFAULT 0 COMMENT '满足数量9',
    quantity_meet10  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量10',
    quantity_meet11  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量11',
    quantity_meet12  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量12',
    quantity_meet13  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量13',
    quantity_meet14  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量14',
    quantity_meet15  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量15',
    quantity_meet16  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量16',
    quantity_meet17  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量17',
    quantity_meet18  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量18',
    quantity_meet19  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量19',
    quantity_meet20  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量20',
    quantity_meet21  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量21',
    quantity_meet22  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量22',
    quantity_meet23  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量23',
    quantity_meet24  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量24',
    quantity_meet25  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量25',
    quantity_meet26  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量26',
    quantity_meet27  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量27',
    quantity_meet28  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量28',
    quantity_meet29  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量29',
    quantity_meet30  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量30',
    quantity_meet31  bigint       NOT NULL DEFAULT 0 COMMENT '满足数量31',
    has_push         tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time      datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time      datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by        varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by        varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='日物料需求计划风险确认';

-- 采购订单风险确认
CREATE TABLE `supplier_con_po`
(
    id               bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no         varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code    varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    purchase_order   varchar(255) NOT NULL DEFAULT '' COMMENT '采购订单号',
    serial_number    varchar(255) NOT NULL DEFAULT '' COMMENT '行项目号',
    quantity_meet    varchar(255) NOT NULL DEFAULT '' COMMENT '满足数量',
    feedback_results varchar(255) NOT NULL DEFAULT '' COMMENT '反馈结果 , 1-异常；0-无异常（匹配峰值需求缺口，如果可满足峰值，即选择无异常）',
    venture_type     varchar(255) NOT NULL DEFAULT '' COMMENT '风险类型 , 当反馈结果=1时，此字段必输 1. 生产节拍不足 2. 人员不足 3. 原材料不足 4. 设备异常 5. 其他',
    venture_specific varchar(255) NOT NULL DEFAULT '' COMMENT '具体风险 , 当反馈结果=1时，此字段必输 描述具体风险',
    measures         varchar(255) NOT NULL DEFAULT '' COMMENT '应对措施 , 当反馈结果=1时，此字段必输 描述具体应对措施',
    has_push         tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time      datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time      datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by        varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by        varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='采购订单风险确认';

-- 供应商共享库存
CREATE TABLE `supplier_sinv_data`
(
    id                   bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    batch_no             varchar(64)  NOT NULL DEFAULT '' COMMENT '批次号',
    supplier_code        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商代码',
    supplier_name        varchar(255) NOT NULL DEFAULT '' COMMENT '供应商名称',
    material_code        varchar(255) NOT NULL DEFAULT '' COMMENT '零件号',
    material_description varchar(255) NOT NULL DEFAULT '' COMMENT '零件名称',
    material_type        varchar(255) NOT NULL DEFAULT '' COMMENT '物料类型(成品,半成品,原材料)',
    quantity_current     bigint       NOT NULL DEFAULT 0 COMMENT '当前库存数量',
    quantity_plan        bigint       NOT NULL DEFAULT 0 COMMENT '原材料在途数量',
    inventory_status     varchar(255) NOT NULL DEFAULT '' COMMENT '库存状态(生产件,呆滞件,备件,KD件)',
    safety_stock         bigint       NOT NULL DEFAULT 0 COMMENT '安全库存',
    production_cycle     varchar(255) NOT NULL DEFAULT '' COMMENT '生产/采购周期:成品即半成品为生产周期（天），原材料为采购周期（天）',
    data_update_time     varchar(255) NOT NULL DEFAULT '' COMMENT '库存更新时间-格式：yyyy-MM-dd HH:mm:ss',
    supplier_batch       varchar(255) NOT NULL DEFAULT '' COMMENT '批次',
    supplieryxq_date     varchar(255) NOT NULL DEFAULT '' COMMENT '有效期截止日期',
    has_push             tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已推送 0未推送 1已推送',
    insert_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    modify_time          datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
    insert_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '插入人',
    modify_by            varchar(64)  NOT NULL DEFAULT '' COMMENT '调整人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='供应商共享库存';