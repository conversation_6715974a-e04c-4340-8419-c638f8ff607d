/**
 * bootstrap-suggest-plugin - v0.1.29
 * @description 这是一个基于 bootstrap 按钮式下拉菜单组件的搜索建议插件，必须使用于按钮式下拉菜单组件上。
 * <AUTHOR> - https://lzw.me
 * @GitHub https://github.com/lzwme/bootstrap-suggest-plugin.git
 * @since 2019-11-18 09:30:06
 */

!function(e){if("function"==typeof define&&define.amd)define(["jquery"],e);else if("object"==typeof exports&&"object"==typeof module)e(require("jquery"));else{if(!window.jQuery)throw new Error("Not found jQuery.");e(window.jQuery)}}(function(g){var c,s=g(window),l="ActiveXObject"in window,e=navigator.userAgent.match(/Chrome\/(\d+)/);e&&(e=+e[1]);var y=l||51<e,p="bsSuggest",h="onDataRequestSuccess",v="disabled",b=!0,m=!1;function w(e){return void 0===e}function x(e){return e.data()}function k(e,t){return w(t)?e.attr("alt"):e.attr("alt",t)}function S(e,t){return void 0!==t?e.attr("data-id",t):e.attr("data-id")}function C(e,t,n){if(t&&t.key){var i,o,a=n.separator||",",r=S(e);n&&n.multiWord?((i=e.val().split(a))[i.length-1]=t.key,r?(o=r.split(a)).push(t.id):o=[t.id],S(e,o.join(a)).val(i.join(a)).focus()):S(e,t.id||"").val(t.key).focus(),e.data("pre-val",e.val()).trigger("onSetSelectValue",[t,(n.data.value||n._lastData.value)[t.index]])}}function j(i,o,e){if(o.is(":visible")){var a=i.parent(),t=a.height(),n=a.width();e.autoDropup&&setTimeout(function(){var e=i.offset().top,t=s.scrollTop(),n=o.height();s.height()+t-e<n&&n+t<e?a.addClass("dropup"):a.removeClass("dropup")},10);var r={};return"left"===e.listAlign?r={left:i.siblings("div").width()-n,right:"auto"}:"right"===e.listAlign&&(r={left:"auto",right:0}),l&&!e.showBtn&&(a.hasClass("dropup")?(r.top="auto",r.bottom=t):(r.top=t,r.bottom="auto")),e.autoMinWidth||(r.minWidth=n),o.css(r),i}}function D(e,t){var n,i,o;if(-1===t.indexId&&!t.idField||t.multiWord)return e;i=t.inputBgColor,o=t.inputWarnColor;var a=e.val(),r=e.data("pre-val");return S(e)||!a?(e.css("background",i||""),!a&&r&&e.trigger("onUnsetSelectValue").data("pre-val","")):(n=e.css("backgroundColor").replace(/ /g,"").split(",",3).join(","),~o.indexOf(n)||e.trigger("onUnsetSelectValue").data("pre-val","").css("background",o)),e}function A(e,t,n){var i,o,a=e.parent().find("tbody tr."+n.listHoverCSS);a.length&&(i=(a.index()+3)*a.height(),(o=+t.css("maxHeight").replace("px",""))<i||t.scrollTop()>o?i-=o:i=0,t.scrollTop(i))}function F(e,t){e.find("tr."+t.listHoverCSS).removeClass(t.listHoverCSS)}function f(e){var t,n=b;for(t in e)if("value"===t){n=m;break}return n?(window.console&&window.console.trace,m):e.value.length?e:m}function T(e,t){var n=t.effectiveFields;return!("__index"===e||n.length&&!~g.inArray(e,n))}function H(e,t,n,i){n.html('<div style="padding:10px 5px 5px">'+e+"</div>").show(),j(t,n,i)}function q(e,t){var n=e.parent().find("ul:eq(0)");n.is(":visible")||(n.show(),e.trigger("onShowDropdown",[t?t.data.value:[]]))}function W(e,t){var n=e.parent().find("ul:eq(0)");n.is(":visible")&&(n.hide(),e.trigger("onHideDropdown",[t?t.data.value:[]]))}function B(e,t,n){var i,o,a,r,s,l,u=e.parent().find("ul:eq(0)"),d=0,f=['<table class="table table-condensed table-sm" style="margin:0">'],c=t.value;if(!t||!(i=c.length))return n.emptyTip?H(n.emptyTip,e,u,n):(u.empty(),W(e,n)),e;if(n._lastData&&JSON.stringify(n._lastData)===JSON.stringify(t)&&u.find("tr").length===i)return q(e,n),j(e,u,n);n._lastData=t;var p,h=n.effectiveFields.length?n.effectiveFields:g.map(c[0],function(e,t){return t});n.showHeader&&(f.push("<thead><tr>"),g.each(h,function(e,t){T(t,n)&&(f.push("<th>",n.effectiveFieldsAlias[t]||t,0===e?"("+i+")":"","</th>"),e++)}),f.push("</tr></thead>")),f.push("<tbody>");var v=Math.min(n.maxOptionCount,i);for(o=0;o<v;o++){for(a in d=0,r=[],s=(p=c[o])[n.idField],l=p[n.keyField],p)w(l)&&n.indexKey===d&&(l=p[a]),w(s)&&n.indexId===d&&(s=p[a]),d++;g.each(h,function(e,t){T(t,n)&&r.push('<td data-name="',t,'">',p[t],"</td>")}),f.push('<tr data-index="',p.__index||o,'" data-id="',s,'" data-key="',l,'">',r.join(""),"</tr>")}return f.push("</tbody></table>"),u.html(f.join("")),q(e,n),setTimeout(function(){if(!y){var e=u.find("table:eq(0)"),t=0,n=0;u.height()<e.height()&&+u.css("minWidth").replace("px","")<u.width()&&(t=18,n=20),u.css("paddingRight",t),e.css("marginBottom",n)}},301),j(e,u,n),e}function _(t,n){n=n||"";var e=t._preAjax;e&&e.abort&&4!==e.readyState&&e.abort();var i={type:"GET",dataType:t.jsonp?"jsonp":"json",timeout:5e3};t.jsonp&&(i.jsonp=t.jsonp);var o,a=t.fnAdjustAjaxParam;if(g.isFunction(a)){if(o=a(n,t),m===o)return;g.extend(i,o)}return i.url=function(){if(!n||i.data)return i.url||t.url;var e="?";return/=$/.test(t.url)?e="":/\?/.test(t.url)&&(e="&"),t.url+e+encodeURIComponent(n)}(),t._preAjax=g.ajax(i).done(function(e){t.data=t.fnProcessData(e)}).fail(function(e){t.fnAjaxFail&&t.fnAjaxFail(e,t)})}function U(e,t,n,i){return n=g.trim(n),i.ignorecase&&(e=e.toLocaleLowerCase(),n=n.toLocaleLowerCase()),n&&(T(t,i)||(o=t,a=i,~g.inArray(o,a.searchFields)))&&(~n.indexOf(e)||i.twoWayMatch&&~e.indexOf(n));var o,a}var n={url:null,jsonp:null,data:{value:[]},indexId:0,indexKey:0,idField:"",keyField:"",autoSelect:b,allowNoKeyword:b,getDataMethod:"firstByUrl",delayUntilKeyup:m,ignorecase:m,effectiveFields:[],effectiveFieldsAlias:{},searchFields:[],twoWayMatch:b,multiWord:m,separator:",",delay:300,emptyTip:"",searchingTip:"搜索中...",hideOnSelect:m,maxOptionCount:200,autoDropup:m,autoMinWidth:m,showHeader:m,showBtn:b,inputBgColor:"",inputWarnColor:"rgba(255,0,0,.1)",listStyle:{"padding-top":0,"max-height":"375px","max-width":"800px",overflow:"auto",width:"auto",transition:"0.3s","-webkit-transition":"0.3s","-moz-transition":"0.3s","-o-transition":"0.3s","word-break":"keep-all","white-space":"nowrap"},listAlign:"left",listHoverStyle:"background: #07d; color:#fff",listHoverCSS:"jhover",clearable:m,keyLeft:37,keyUp:38,keyRight:39,keyDown:40,keyEnter:13,fnProcessData:function(e){return f(e)},fnGetData:function(e,t,n,i){var o,a,r,s,l={value:[]},u=i.fnPreprocessKeyword;if(e=e||"",g.isFunction(u)&&(e=u(e,i)),i.url){var d;i.searchingTip&&(d=setTimeout(function(){H(i.searchingTip,t,t.parent().find("ul"),i)},600)),_(i,e).done(function(e){n(t,i.data,i),t.trigger(h,e),"firstByUrl"===i.getDataMethod&&(i.url=null)}).always(function(){d&&clearTimeout(d)})}else{if(f(o=i.data))if(e){for(s=o.value.length,a=0;a<s;a++)for(r in o.value[a])if(o.value[a][r]&&U(e,r,o.value[a][r]+"",i)){l.value.push(o.value[a]),l.value[l.value.length-1].__index=a;break}}else l=o;n(t,l,i)}},fnAdjustAjaxParam:null,fnPreprocessKeyword:null,fnAjaxFail:null},i={init:function(f){var t=this;return w((f=f||{}).showHeader)&&f.effectiveFields&&1<f.effectiveFields.length&&(f.showHeader=b),(f=g.extend(b,{},n,f)).processData&&(f.fnProcessData=f.processData),f.getData&&(f.fnGetData=f.getData),"firstByUrl"===f.getDataMethod&&f.url&&!f.delayUntilKeyup&&_(f).done(function(e){f.url=null,t.trigger(h,e)}),g("#"+p).length||g("head:eq(0)").append('<style id="'+p+'">.'+f.listHoverCSS+"{"+f.listHoverStyle+"}</style>"),t.each(function(){var e,n,t,i,o,a,r,s=g(this),l=s.parent(),u=(i=f,o=(t=s).prev("i.clearable"),i.clearable&&!o.length&&(o=g('<i class="clearable glyphicon glyphicon-remove fa fa-plus"></i>').prependTo(t.parent())),o.css({position:"absolute",top:"calc(50% - 6px)",transform:"rotate(45deg)",zIndex:4,cursor:"pointer",width:"14px",lineHeight:"14px",textAlign:"center",fontSize:12}).hide()),d=l.find("ul:eq(0)");(d.parent().css("position","relative"),a=s,r=f,!d.length||a.data(p)?m:(a.data(p,{options:r}),b))&&(f.showBtn||(s.css("borderRadius",4),l.css("width","100%").find(".btn:eq(0)").hide()),s.removeClass(v).prop(v,m).attr("autocomplete","off"),d.css(f.listStyle),f.inputBgColor||(f.inputBgColor=s.css("backgroundColor")),s.on("keydown.bs",function(e){var t,n;if(d.is(":visible")){if(t=d.find("."+f.listHoverCSS),n="",F(d,f),e.keyCode===f.keyDown){if(t.length?t.next().length?n=x(t.next().mouseover()):f.autoSelect&&S(s,"").val(k(s)):n=x(d.find("tbody tr:first").mouseover()),A(s,d,f),!f.autoSelect)return}else if(e.keyCode===f.keyUp){if(t.length?t.prev().length?n=x(t.prev().mouseover()):f.autoSelect&&S(s,"").val(k(s)):n=x(d.find("tbody tr:last").mouseover()),A(s,d,f),!f.autoSelect)return}else e.keyCode===f.keyEnter?(n=x(t),W(s,f)):S(s,"");C(s,n,f)}else S(s,"")}).on("compositionstart.bs",function(e){c=b}).on("compositionend.bs",function(e){c=m}).on("keyup.bs input.bs paste.bs",function(e){var t;e.keyCode&&D(s,f),~g.inArray(e.keyCode,[f.keyDown,f.keyUp,f.keyEnter])?s.val(s.val()):(clearTimeout(n),n=setTimeout(function(){c||(t=s.val(),g.trim(t)&&t===k(s)||(k(s,t),f.multiWord&&(t=t.split(f.separator).reverse()[0]),(t.length||f.allowNoKeyword)&&f.fnGetData(g.trim(t),s,B,f)))},f.delay||300))}).on("focus.bs",function(){j(s,d,f)}).on("blur.bs",function(){e||(W(s,f),c=!0,setTimeout(function(){c=m}))}).on("click.bs",function(){var e=s.val();if(g.trim(e)&&e===k(s)&&d.find("table tr").length)return q(s,f);d.is(":visible")||(f.multiWord&&(e=e.split(f.separator).reverse()[0]),(e.length||f.allowNoKeyword)&&f.fnGetData(g.trim(e),s,B,f))}),l.find(".btn:eq(0)").attr("data-toggle","").click(function(){if(d.is(":visible"))W(s,f);else{if(f.url){if(s.click().focus(),!d.find("tr").length)return m}else B(s,f.data,f);q(s,f)}return m}),d.mouseenter(function(){e=1,s.blur()}).mouseleave(function(){e=0,s.focus()}).on("mouseenter","tbody tr",function(){return F(d,f),g(this).addClass(f.listHoverCSS),m}).on("mousedown","tbody tr",function(){var e=x(g(this));C(s,e,f),k(s,e.key),D(s,f),f.hideOnSelect&&W(s,f)}),u.length&&(u.click(function(){S(s,"").val(""),D(s,f)}),l.mouseenter(function(){s.prop(v)||u.css("right",f.showBtn?Math.max(s.next().width(),33)+2:12).show()}).mouseleave(function(){u.hide()})))})},show:function(){return this.each(function(){g(this).click()})},hide:function(){return this.each(function(){W(g(this))})},disable:function(){return this.each(function(){g(this).attr(v,b).parent().find(".btn:eq(0)").prop(v,b)})},enable:function(){return this.each(function(){g(this).attr(v,m).parent().find(".btn:eq(0)").prop(v,m)})},destroy:function(){return this.each(function(){g(this).off("click.bs keydown.bs compositionstart.bs compositionend.bs keyup.bs input.bs paste.bs focus.bs click.bs").removeData(p).removeAttr("style").parent().find(".btn:eq(0)").off().show().attr("data-toggle","dropdown").prop(v,m).next().css("display","").off()})},version:function(){return"0.1.29"}};g.fn[p]=function(e){if("string"==typeof e&&i[e]){var t=b;return this.each(function(){if(!g(this).data(p))return t=m}),t||"init"===e||"version"===e?i[e].apply(this,[].slice.call(arguments,1)):this}return i.init.apply(this,arguments)}});
