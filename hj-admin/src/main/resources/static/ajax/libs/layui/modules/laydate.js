/*! laydate-v5.5.0 日期与时间组件 */
!function(window){var MOD_NAME="lay";var document=window.document;var lay=function(selector){return new Class(selector)};var Class=function(selector){var that=this;var elem=typeof selector==="object"?function(){return layui.isArray(selector)?selector:[selector]}():(this.selector=selector,document.querySelectorAll(selector||null));lay.each(elem,function(index,item){that.push(elem[index])})};Array.prototype.indexOf=Array.prototype.indexOf||function(searchElement,fromIndex){var rst=-1;fromIndex=fromIndex||0;layui.each(this,function(index,val){if(searchElement===val&&index>=fromIndex){rst=index;return !0}});return rst};Class.fn=Class.prototype=[];Class.fn.constructor=Class;lay.extend=function(){var ai=1;var length;var args=arguments;var clone=function(target,obj){target=target||(layui.type(obj)==="array"?[]:{});for(var i in obj){target[i]=(obj[i]&&obj[i].constructor===Object)?clone(target[i],obj[i]):obj[i]}return target};args[0]=typeof args[0]==="object"?args[0]:{};length=args.length;for(;ai<length;ai++){if(typeof args[ai]==="object"){clone(args[0],args[ai])}}return args[0]};lay.ie=function(){var agent=navigator.userAgent.toLowerCase();return(!!window.ActiveXObject||"ActiveXObject" in window)?((agent.match(/msie\s(\d+)/)||[])[1]||"11"):false}();lay.layui=layui||{};lay.getPath=layui.cache.dir;lay.stope=layui.stope;lay.each=function(){layui.each.apply(layui,arguments);return this};lay.digit=function(num,length){if(!(typeof num==="string"||typeof num==="number")){return""}var str="";num=String(num);length=length||2;for(var i=num.length;i<length;i++){str+="0"}return num<Math.pow(10,length)?str+num:num};lay.elem=function(elemName,attr){var elem=document.createElement(elemName);lay.each(attr||{},function(key,value){elem.setAttribute(key,value)});return elem};lay.hasScrollbar=function(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)};lay.getStyleRules=function(style,callback){if(!style){return}var sheet=style.sheet||style.styleSheet||{};var rules=sheet.cssRules||sheet.rules;if(typeof callback==="function"){layui.each(rules,function(i,item){if(callback(item,i)){return true}})}return rules};lay.style=function(options){options=options||{};var style=lay.elem("style");var styleText=options.text||"";var target=options.target;if(!styleText){return}if("styleSheet" in style){style.setAttribute("type","text/css");style.styleSheet.cssText=styleText}else{style.innerHTML=styleText}style.id="LAY-STYLE-"+(options.id||function(index){lay.style.index++;return"DF-"+index}(lay.style.index||0));if(target){var styleElem=lay(target).find("#"+style.id);styleElem[0]&&styleElem.remove();lay(target).append(style)}return style};lay.position=function(target,elem,opts){if(!elem){return}opts=opts||{};if(target===document||target===lay("body")[0]){opts.clickType="right"}var rect=opts.clickType==="right"?function(){var e=opts.e||window.event||{};return{left:e.clientX,top:e.clientY,right:e.clientX,bottom:e.clientY}}():target.getBoundingClientRect();var elemWidth=elem.offsetWidth;var elemHeight=elem.offsetHeight;var scrollArea=function(type){type=type?"scrollLeft":"scrollTop";return document.body[type]|document.documentElement[type]};var winArea=function(type){return document.documentElement[type?"clientWidth":"clientHeight"]};var margin="margin" in opts?opts.margin:5;var left=rect.left;var top=rect.bottom;if(opts.align==="center"){left=left-(elemWidth-target.offsetWidth)/2}else{if(opts.align==="right"){left=left-elemWidth+target.offsetWidth}}if(left+elemWidth+margin>winArea("width")){left=winArea("width")-elemWidth-margin}if(left<margin){left=margin}if(rect.bottom+elemHeight+margin>winArea()){if(rect.top>elemHeight+margin&&rect.top<=winArea()){top=rect.top-elemHeight-margin*2}else{if(!opts.allowBottomOut){top=winArea()-elemHeight-margin*2;if(top<0){top=0}}}}var position=opts.position;if(position){elem.style.position=position}elem.style.left=left+(position==="fixed"?0:scrollArea(1))+"px";elem.style.top=top+(position==="fixed"?0:scrollArea())+"px";if(!lay.hasScrollbar()){var rect1=elem.getBoundingClientRect();if(!opts.SYSTEM_RELOAD&&(rect1.bottom+margin)>winArea()){opts.SYSTEM_RELOAD=true;setTimeout(function(){lay.position(target,elem,opts)},50)}}};lay.options=function(elem,opts){opts=typeof opts==="object"?opts:{attr:opts};if(elem===document){return{}}var othis=lay(elem);var attrName=opts.attr||"lay-options";var attrValue=othis.attr(attrName);try{return new Function("return "+(attrValue||"{}"))()}catch(ev){layui.hint().error(opts.errorText||[attrName+'="'+attrValue+'"',"\n parseerror: "+ev].join("\n"),"error");return{}}};lay.isTopElem=function(elem){var topElems=[document,lay("body")[0]],matched=false;lay.each(topElems,function(index,item){if(item===elem){return matched=true}});return matched};lay.clipboard={writeText:function(options){var text=String(options.text);try{navigator.clipboard.writeText(text).then(options.done)["catch"](options.error)}catch(e){var elem=document.createElement("textarea");elem.value=text;elem.style.position="fixed";elem.style.opacity="0";elem.style.top="0px";elem.style.left="0px";document.body.appendChild(elem);elem.select();try{document.execCommand("copy");typeof options.done==="function"&&options.done()}catch(err){typeof options.error==="function"&&options.error(err)}finally{elem.remove?elem.remove():document.body.removeChild(elem)}}}};Class.addStr=function(str,new_str){str=str.replace(/\s+/," ");new_str=new_str.replace(/\s+/," ").split(" ");lay.each(new_str,function(ii,item){if(!new RegExp("\\b"+item+"\\b").test(str)){str=str+" "+item}});return str.replace(/^\s|\s$/,"")};Class.removeStr=function(str,new_str){str=str.replace(/\s+/," ");new_str=new_str.replace(/\s+/," ").split(" ");lay.each(new_str,function(ii,item){var exp=new RegExp("\\b"+item+"\\b");if(exp.test(str)){str=str.replace(exp,"")}});return str.replace(/\s+/," ").replace(/^\s|\s$/,"")};Class.fn.find=function(selector){var that=this;var elem=[];var isObject=typeof selector==="object";this.each(function(i,item){var children=isObject&&item.contains(selector)?selector:item.querySelectorAll(selector||null);lay.each(children,function(index,child){elem.push(child)})});return lay(elem)};Class.fn.each=function(fn){return lay.each.call(this,this,fn)};Class.fn.addClass=function(className,type){return this.each(function(index,item){item.className=Class[type?"removeStr":"addStr"](item.className,className)})};Class.fn.removeClass=function(className){return this.addClass(className,true)};Class.fn.hasClass=function(className){var has=false;this.each(function(index,item){if(new RegExp("\\b"+className+"\\b").test(item.className)){has=true}});return has};Class.fn.css=function(key,value){var that=this;var parseValue=function(v){return isNaN(v)?v:(v+"px")};return(typeof key==="string"&&value===undefined)?function(){if(that.length>0){return that[0].style[key]}}():that.each(function(index,item){typeof key==="object"?lay.each(key,function(thisKey,thisValue){item.style[thisKey]=parseValue(thisValue)}):item.style[key]=parseValue(value)})};Class.fn.width=function(value){var that=this;return value===undefined?function(){if(that.length>0){return that[0].offsetWidth}}():that.each(function(index,item){that.css("width",value)})};Class.fn.height=function(value){var that=this;return value===undefined?function(){if(that.length>0){return that[0].offsetHeight}}():that.each(function(index,item){that.css("height",value)})};Class.fn.attr=function(key,value){var that=this;return value===undefined?function(){if(that.length>0){return that[0].getAttribute(key)}}():that.each(function(index,item){item.setAttribute(key,value)})};Class.fn.removeAttr=function(key){return this.each(function(index,item){item.removeAttribute(key)})};Class.fn.html=function(html){var that=this;return html===undefined?function(){if(that.length>0){return that[0].innerHTML}}():this.each(function(index,item){item.innerHTML=html})};Class.fn.val=function(value){var that=this;return value===undefined?function(){if(that.length>0){return that[0].value}}():this.each(function(index,item){item.value=value})};Class.fn.append=function(elem){return this.each(function(index,item){typeof elem==="object"?item.appendChild(elem):item.innerHTML=item.innerHTML+elem})};Class.fn.remove=function(elem){return this.each(function(index,item){elem?item.removeChild(elem):item.parentNode.removeChild(item)})};Class.fn.on=function(eventName,fn){return this.each(function(index,item){item.attachEvent?item.attachEvent("on"+eventName,function(e){e.target=e.srcElement;fn.call(item,e)}):item.addEventListener(eventName,fn,false)})};Class.fn.off=function(eventName,fn){return this.each(function(index,item){item.detachEvent?item.detachEvent("on"+eventName,fn):item.removeEventListener(eventName,fn,false)})};window.lay=lay;if(window.layui&&layui.define){layui.define(function(exports){exports(MOD_NAME,lay)})}}(window,window.document);!function(window,document){var isLayui=window.layui&&layui.define,ready={getPath:(window.lay&&lay.getPath)?lay.getPath:"",link:function(href,fn,cssname){if(!laydate.path){return}if(window.lay&&lay.layui){lay.layui.link(laydate.path+href,fn,cssname)}}};var GLOBAL=window.LAYUI_GLOBAL||{};var MOD_NAME="laydate";var MOD_ID="layui-"+MOD_NAME+"-id";var laydate={v:"5.5.0",config:{weekStart:0,},index:(window.laydate&&window.laydate.v)?100000:0,path:GLOBAL.laydate_dir||ready.getPath,set:function(options){var that=this;that.config=lay.extend({},that.config,options);return that},ready:function(callback){var cssname="laydate";var ver="";var path=(isLayui?"modules/":"")+"laydate.css?v="+laydate.v+ver;isLayui?(layui["layui.all"]?(typeof callback==="function"&&callback()):layui.addcss(path,callback,cssname)):ready.link(path,callback,cssname);return this}};var thisModule=function(){var that=this;var options=that.config;var id=options.id;thisModule.that[id]=that;return that.inst={hint:function(content){that.hint.call(that,content)},reload:function(options){that.reload.call(that,options)},config:that.config}};var MOD_NAME="laydate";var ELEM=".layui-laydate";var THIS="layui-this";var SHOW="layui-show";var HIDE="layui-hide";var DISABLED="laydate-disabled";var LIMIT_YEAR=[100,200000];var ELEM_STATIC="layui-laydate-static";var ELEM_LIST="layui-laydate-list";var ELEM_SELECTED="laydate-selected";var ELEM_HINT="layui-laydate-hint";var ELEM_DAY_NOW="laydate-day-now";var ELEM_PREV="laydate-day-prev";var ELEM_NEXT="laydate-day-next";var ELEM_FOOTER="layui-laydate-footer";var ELEM_SHORTCUT="layui-laydate-shortcut";var ELEM_NOW=".laydate-btns-now";var ELEM_CONFIRM=".laydate-btns-confirm";var ELEM_TIME_TEXT="laydate-time-text";var ELEM_TIME_BTN="laydate-btns-time";var ELEM_PREVIEW="layui-laydate-preview";var ELEM_MAIN="layui-laydate-main";var ELEM_SHADE="layui-laydate-shade";var Class=function(options){var that=this;that.index=++laydate.index;that.config=lay.extend({},that.config,laydate.config,options);var elem=lay(options.elem||that.config.elem);if(elem.length>1){lay.each(elem,function(){laydate.render(lay.extend({},that.config,{elem:this}))});return that}options=lay.extend(that.config,lay.options(elem[0]));if(elem[0]&&elem.attr(MOD_ID)){var newThat=thisModule.getThis(elem.attr(MOD_ID));if(!newThat){return}return newThat.reload(options)}options.id="id" in options?options.id:(elem.attr("id")||that.index);options.index=that.index;laydate.ready(function(){that.init()})};var dateType="yyyy|y|MM|M|dd|d|HH|H|mm|m|ss|s";thisModule.formatArr=function(format){return(format||"").match(new RegExp(dateType+"|.","g"))||[]};Class.isLeapYear=function(year){return(year%4===0&&year%100!==0)||year%400===0};Class.prototype.config={type:"date",range:false,format:"yyyy-MM-dd",value:null,isInitValue:true,min:"1900-1-1",max:"2099-12-31",trigger:"click",show:false,showBottom:true,isPreview:true,btns:["clear","now","confirm"],lang:"cn",theme:"default",position:null,calendar:false,mark:{},holidays:null,zIndex:null,done:null,change:null,autoConfirm:true,shade:0};Class.prototype.lang=function(){var that=this,options=that.config,text={cn:{weeks:["日","一","二","三","四","五","六"],time:["时","分","秒"],timeTips:"选择时间",startTime:"开始时间",endTime:"结束时间",dateTips:"返回日期",month:["一","二","三","四","五","六","七","八","九","十","十一","十二"],tools:{confirm:"确定",clear:"清空",now:"现在"},timeout:"结束时间不能早于开始时间<br>请重新选择",invalidDate:"不在有效日期或时间范围内",formatError:["日期格式不合法<br>必须遵循下述格式：<br>","<br>已为你重置"],preview:"当前选中的结果"},en:{weeks:["Su","Mo","Tu","We","Th","Fr","Sa"],time:["Hours","Minutes","Seconds"],timeTips:"Select Time",startTime:"Start Time",endTime:"End Time",dateTips:"Select Date",month:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],tools:{confirm:"Confirm",clear:"Clear",now:"Now"},timeout:"End time cannot be less than start Time<br>Please re-select",invalidDate:"Invalid date",formatError:["The date format error<br>Must be followed：<br>","<br>It has been reset"],preview:"The selected result"}};return text[options.lang]||text["cn"]};Class.prototype.reload=function(options){var that=this;that.config=lay.extend({},that.config,options);that.init()};Class.prototype.init=function(){var that=this,options=that.config,isStatic=options.position==="static",format={year:"yyyy",month:"yyyy-MM",date:"yyyy-MM-dd",time:"HH:mm:ss",datetime:"yyyy-MM-dd HH:mm:ss"};options.elem=lay(options.elem);options.eventElem=lay(options.eventElem);if(!options.elem[0]){return}layui.type(options.theme)!=="array"&&(options.theme=[options.theme]);if(options.fullPanel){if(options.type!=="datetime"||options.range){delete options.fullPanel}}that.rangeStr=options.range?(typeof options.range==="string"?options.range:"-"):"";that.rangeLinked=!!(options.range&&options.rangeLinked&&(options.type==="date"||options.type==="datetime"));that.autoCalendarModel=function(){var state=that.rangeLinked;that.rangeLinked=(options.range&&(options.type==="date"||options.type==="datetime"))&&((!that.startDate||!that.endDate)||(that.startDate&&that.endDate&&that.startDate.year===that.endDate.year&&that.startDate.month===that.endDate.month));lay(that.elem)[that.rangeLinked?"addClass":"removeClass"]("layui-laydate-linkage");return that.rangeLinked!=state};that.autoCalendarModel.auto=that.rangeLinked&&options.rangeLinked==="auto";if(layui.type(options.range)==="array"){that.rangeElem=[lay(options.range[0]),lay(options.range[1])]}if(!format[options.type]){window.console&&console.error&&console.error("laydate type error:'"+options.type+"' is not supported");options.type="date"}if(options.format===format.date){options.format=format[options.type]||format.date}that.format=thisModule.formatArr(options.format);if(options.weekStart){if(!/^[0-6]$/.test(options.weekStart)){var lang=that.lang();options.weekStart=lang.weeks.indexOf(options.weekStart);if(options.weekStart===-1){options.weekStart=0}}}that.EXP_IF="";that.EXP_SPLIT="";lay.each(that.format,function(i,item){var EXP=new RegExp(dateType).test(item)?"\\d{"+function(){if(new RegExp(dateType).test(that.format[i===0?i+1:i-1]||"")){if(/^yyyy|y$/.test(item)){return 4}return item.length}if(/^yyyy$/.test(item)){return"1,4"}if(/^y$/.test(item)){return"1,308"}return"1,2"}()+"}":"\\"+item;that.EXP_IF=that.EXP_IF+EXP;that.EXP_SPLIT=that.EXP_SPLIT+"("+EXP+")"});that.EXP_IF_ONE=new RegExp("^"+that.EXP_IF+"$");that.EXP_IF=new RegExp("^"+(options.range?that.EXP_IF+"\\s\\"+that.rangeStr+"\\s"+that.EXP_IF:that.EXP_IF)+"$");that.EXP_SPLIT=new RegExp("^"+that.EXP_SPLIT+"$","");if(!that.isInput(options.elem[0])){if(options.trigger==="focus"){options.trigger="click"}}options.elem.attr("lay-key",that.index);options.eventElem.attr("lay-key",that.index);options.elem.attr(MOD_ID,options.id);options.mark=lay.extend({},(options.calendar&&options.lang==="cn")?{"0-1-1":"元旦","0-2-14":"情人","0-3-8":"妇女","0-3-12":"植树","0-4-1":"愚人","0-5-1":"劳动","0-5-4":"青年","0-6-1":"儿童","0-9-10":"教师","0-10-1":"国庆","0-12-25":"圣诞"}:{},options.mark);lay.each(["min","max"],function(i,item){var ymd=[];var hms=[];if(typeof options[item]==="number"){var day=options[item],tDate=new Date(),time=that.newDate({year:tDate.getFullYear(),month:tDate.getMonth(),date:tDate.getDate(),hours:i?23:0,minutes:i?59:0,seconds:i?59:0}).getTime(),STAMP=86400000,thisDate=new Date(day?(day<STAMP?time+day*STAMP:day):time);ymd=[thisDate.getFullYear(),thisDate.getMonth()+1,thisDate.getDate()];hms=[thisDate.getHours(),thisDate.getMinutes(),thisDate.getSeconds()]}else{if(typeof options[item]==="string"){ymd=(options[item].match(/\d+-\d+-\d+/)||[""])[0].split("-");hms=(options[item].match(/\d+:\d+:\d+/)||[""])[0].split(":")}else{if(typeof options[item]==="object"){return options[item]}}}options[item]={year:ymd[0]|0||new Date().getFullYear(),month:ymd[1]?(ymd[1]|0)-1:new Date().getMonth(),date:ymd[2]|0||new Date().getDate(),hours:hms[0]|0,minutes:hms[1]|0,seconds:hms[2]|0}});that.elemID="layui-laydate"+options.elem.attr("lay-key");if(options.show||isStatic){that.render()}isStatic||that.events();if(options.value&&options.isInitValue){if(layui.type(options.value)==="date"){that.setValue(that.parse(0,that.systemDate(options.value)))}else{that.setValue(options.value)}}};Class.prototype.render=function(){var that=this,options=that.config,lang=that.lang(),isStatic=options.position==="static",elem=that.elem=lay.elem("div",{id:that.elemID,"class":["layui-laydate",options.range?" layui-laydate-range":"",that.rangeLinked?" layui-laydate-linkage":"",isStatic?(" "+ELEM_STATIC):"",options.fullPanel?" laydate-theme-fullpanel":"",(function(){var themeStr="";lay.each(options.theme,function(index,theme){if(theme!=="default"&&!/^#/.test(theme)){themeStr+=" laydate-theme-"+theme}});return themeStr})()].join("")}),elemMain=that.elemMain=[],elemHeader=that.elemHeader=[],elemCont=that.elemCont=[],elemTable=that.table=[],divFooter=that.footer=lay.elem("div",{"class":ELEM_FOOTER}),divShortcut=that.shortcut=lay.elem("ul",{"class":ELEM_SHORTCUT});if(options.zIndex){elem.style.zIndex=options.zIndex}lay.each(new Array(2),function(i){if(!options.range&&i>0){return true}var divHeader=lay.elem("div",{"class":"layui-laydate-header"}),headerChild=[function(){var elem=lay.elem("i",{"class":"layui-icon laydate-icon laydate-prev-y"});elem.innerHTML="&#xe65a;";return elem}(),function(){var elem=lay.elem("i",{"class":"layui-icon laydate-icon laydate-prev-m"});elem.innerHTML="&#xe603;";return elem}(),function(){var elem=lay.elem("div",{"class":"laydate-set-ym"}),spanY=lay.elem("span"),spanM=lay.elem("span");elem.appendChild(spanY);elem.appendChild(spanM);return elem}(),function(){var elem=lay.elem("i",{"class":"layui-icon laydate-icon laydate-next-m"});elem.innerHTML="&#xe602;";return elem}(),function(){var elem=lay.elem("i",{"class":"layui-icon laydate-icon laydate-next-y"});elem.innerHTML="&#xe65b;";return elem}()],divContent=lay.elem("div",{"class":"layui-laydate-content"}),table=lay.elem("table"),thead=lay.elem("thead"),theadTr=lay.elem("tr");lay.each(headerChild,function(i,item){divHeader.appendChild(item)});thead.appendChild(theadTr);lay.each(new Array(6),function(i){var tr=table.insertRow(0);lay.each(new Array(7),function(j){if(i===0){var th=lay.elem("th");th.innerHTML=lang.weeks[(j+options.weekStart)%7];theadTr.appendChild(th)}tr.insertCell(j)})});table.insertBefore(thead,table.children[0]);divContent.appendChild(table);elemMain[i]=lay.elem("div",{"class":ELEM_MAIN+" laydate-main-list-"+i});elemMain[i].appendChild(divHeader);elemMain[i].appendChild(divContent);elemHeader.push(headerChild);elemCont.push(divContent);elemTable.push(table)});lay(divFooter).html(function(){var html=[],btns=[];if(options.type==="datetime"){html.push('<span lay-type="datetime" class="'+ELEM_TIME_BTN+'">'+lang.timeTips+"</span>")}if(!(!options.range&&options.type==="datetime")||options.fullPanel){html.push('<span class="'+ELEM_PREVIEW+'" title="'+lang.preview+'"></span>')}lay.each(options.btns,function(i,item){var title=lang.tools[item]||"btn";if(options.range&&item==="now"){return}if(isStatic&&item==="clear"){title=options.lang==="cn"?"重置":"Reset"}btns.push('<span lay-type="'+item+'" class="laydate-btns-'+item+'">'+title+"</span>")});html.push('<div class="laydate-footer-btns">'+btns.join("")+"</div>");return html.join("")}());if(options.shortcuts){elem.appendChild(divShortcut);lay(divShortcut).html(function(){var shortcutBtns=[];lay.each(options.shortcuts,function(i,item){shortcutBtns.push('<li data-index="'+i+'">'+item.text+"</li>")});return shortcutBtns.join("")}()).find("li").on("click",function(event){var btnSetting=options.shortcuts[this.dataset["index"]]||{};var value=(typeof btnSetting.value==="function"?btnSetting.value():btnSetting.value)||[];if(!layui.isArray(value)){value=[value]}var type=options.type;lay.each(value,function(i,item){var dateTime=[options.dateTime,that.endDate][i];if(type==="time"&&layui.type(item)!=="date"){if(that.EXP_IF.test(item)){item=(item.match(that.EXP_SPLIT)||[]).slice(1);lay.extend(dateTime,{hours:item[0]|0,minutes:item[2]|0,seconds:item[4]|0})}}else{lay.extend(dateTime,that.systemDate(layui.type(item)==="date"?item:new Date(item)))}if(type==="time"||type==="datetime"){that[["startTime","endTime"][i]]={hours:dateTime.hours,minutes:dateTime.minutes,seconds:dateTime.seconds,}}if(i===0){that.startDate=lay.extend({},dateTime)}else{that.endState=true}if(type==="year"||type==="month"||type==="time"){that.listYM[i]=[dateTime.year,dateTime.month+1]}else{if(i){that.autoCalendarModel.auto&&that.autoCalendarModel()}}});that.checkDate("limit").calendar(null,null,"init");var timeBtn=lay(that.footer).find("."+ELEM_TIME_BTN).removeClass(DISABLED);timeBtn&&timeBtn.attr("lay-type")==="date"&&timeBtn[0].click();that.done(null,"change");lay(this).addClass(THIS);if(options.position!=="static"){that.setValue(that.parse()).done().remove()}})}lay.each(elemMain,function(i,main){elem.appendChild(main)});options.showBottom&&elem.appendChild(divFooter);var style=lay.elem("style");var styleText=[];var colorTheme;var isPrimaryColor=true;lay.each(options.theme,function(index,theme){if(isPrimaryColor&&/^#/.test(theme)){colorTheme=true;isPrimaryColor=false;styleText.push(["#{{id}} .layui-laydate-header{background-color:{{theme}};}","#{{id}} li.layui-this,#{{id}} td.layui-this>div{background-color:{{theme}} !important;}",options.theme.indexOf("circle")!==-1?"":"#{{id}} .layui-this{background-color:{{theme}} !important;}","#{{id}} .laydate-day-now{color:{{theme}} !important;}","#{{id}} .laydate-day-now:after{border-color:{{theme}} !important;}"].join("").replace(/{{id}}/g,that.elemID).replace(/{{theme}}/g,theme));return}if(!isPrimaryColor&&/^#/.test(theme)){styleText.push(["#{{id}} .laydate-selected>div{background-color:{{theme}} !important;}","#{{id}} .laydate-selected:hover>div{background-color:{{theme}} !important;}"].join("").replace(/{{id}}/g,that.elemID).replace(/{{theme}}/g,theme))}});if(options.shortcuts&&options.range){styleText.push("#{{id}}.layui-laydate-range{width: 628px;}".replace(/{{id}}/g,that.elemID))}if(styleText.length){styleText=styleText.join("");if("styleSheet" in style){style.setAttribute("type","text/css");style.styleSheet.cssText=styleText}else{style.innerHTML=styleText}colorTheme&&lay(elem).addClass("laydate-theme-molv");elem.appendChild(style)}that.remove(Class.thisElemDate);laydate.thisId=options.id;isStatic?options.elem.append(elem):(document.body.appendChild(elem),that.position());var shade=options.shade?('<div class="'+ELEM_SHADE+'" style="'+("z-index:"+(parseInt(layui.getStyle(elem,"z-index"))-1)+"; background-color: "+(options.shade[1]||"#000")+"; opacity: "+(options.shade[0]||options.shade))+'"></div>'):"";elem.insertAdjacentHTML("beforebegin",shade);that.checkDate().calendar(null,0,"init");that.changeEvent();Class.thisElemDate=that.elemID;that.renderAdditional();typeof options.ready==="function"&&options.ready(lay.extend({},options.dateTime,{month:options.dateTime.month+1}));that.preview()};Class.prototype.remove=function(prev){var that=this,options=that.config,elem=lay("#"+(prev||that.elemID));if(!elem[0]){return that}if(!elem.hasClass(ELEM_STATIC)){that.checkDate(function(){elem.remove();delete that.startDate;delete that.endDate;delete that.endState;delete that.startTime;delete that.endTime;delete laydate.thisId;typeof options.close==="function"&&options.close(that)})}lay("."+ELEM_SHADE).remove();return that};Class.prototype.position=function(){var that=this,options=that.config;lay.position(options.elem[0],that.elem,{position:options.position});return that};Class.prototype.hint=function(opts){var that=this;var options=that.config;var div=lay.elem("div",{"class":ELEM_HINT});if(!that.elem){return}if(typeof opts==="object"){opts=opts||{}}else{opts={content:opts}}div.innerHTML=opts.content||"";lay(that.elem).find("."+ELEM_HINT).remove();that.elem.appendChild(div);clearTimeout(that.hinTimer);that.hinTimer=setTimeout(function(){lay(that.elem).find("."+ELEM_HINT).remove()},"ms" in opts?opts.ms:3000)};Class.prototype.getAsYM=function(Y,M,type){type?M--:M++;if(M<0){M=11;Y--}if(M>11){M=0;Y++}return[Y,M]};Class.prototype.systemDate=function(newDate){var thisDate=newDate||new Date();return{year:thisDate.getFullYear(),month:thisDate.getMonth(),date:thisDate.getDate(),hours:newDate?newDate.getHours():0,minutes:newDate?newDate.getMinutes():0,seconds:newDate?newDate.getSeconds():0}};Class.prototype.checkDate=function(fn){var that=this,thisDate=new Date(),options=that.config,lang=that.lang(),dateTime=options.dateTime=options.dateTime||that.systemDate(),thisMaxDate,error,elem=options.elem[0],valType=that.isInput(elem)?"val":"html",value=function(){if(that.rangeElem){var vals=[that.rangeElem[0].val(),that.rangeElem[1].val()];if(vals[0]&&vals[1]){return vals.join(" "+that.rangeStr+" ")}}return that.isInput(elem)?elem.value:(options.position==="static"?"":lay(elem).attr("lay-date"))}(),checkValid=function(dateTime){if(!dateTime){return}if(dateTime.year>LIMIT_YEAR[1]){dateTime.year=LIMIT_YEAR[1],error=true}if(dateTime.month>11){dateTime.month=11,error=true}if(dateTime.seconds>59){dateTime.seconds=0,dateTime.minutes++,error=true}if(dateTime.minutes>59){dateTime.minutes=0,dateTime.hours++,error=true}if(dateTime.hours>23){dateTime.hours=0,error=true}thisMaxDate=laydate.getEndDate(dateTime.month+1,dateTime.year);if(dateTime.date>thisMaxDate){dateTime.date=thisMaxDate,error=true}},initDate=function(dateTime,value,index){var startEnd=["startTime","endTime"];value=(value.match(that.EXP_SPLIT)||[]).slice(1);index=index||0;if(options.range){that[startEnd[index]]=that[startEnd[index]]||{}}lay.each(that.format,function(i,item){var thisv=parseFloat(value[i]);if(value[i].length<item.length){error=true}if(/yyyy|y/.test(item)){if(thisv<LIMIT_YEAR[0]){thisv=LIMIT_YEAR[0],error=true}dateTime.year=thisv}else{if(/MM|M/.test(item)){if(thisv<1){thisv=1,error=true}dateTime.month=thisv-1}else{if(/dd|d/.test(item)){if(thisv<1){thisv=1,error=true}dateTime.date=thisv}else{if(/HH|H/.test(item)){if(thisv<0){thisv=0,error=true}if(thisv>23){thisv=23,error=true}dateTime.hours=thisv;options.range&&(that[startEnd[index]].hours=thisv)}else{if(/mm|m/.test(item)){if(thisv<0){thisv=0,error=true}if(thisv>59){thisv=59,error=true}dateTime.minutes=thisv;options.range&&(that[startEnd[index]].minutes=thisv)}else{if(/ss|s/.test(item)){if(thisv<0){thisv=0,error=true}if(thisv>59){thisv=59,error=true}dateTime.seconds=thisv;options.range&&(that[startEnd[index]].seconds=thisv)}}}}}}});checkValid(dateTime)};if(fn==="limit"){if(options.range){checkValid(that.rangeLinked?that.startDate:dateTime);that.endDate&&checkValid(that.endDate)}else{checkValid(dateTime)}return that}value=value||options.value;if(typeof value==="string"){value=value.replace(/\s+/g," ").replace(/^\s|\s$/g,"")}var getEndDate=function(){if(options.range){that.endDate=that.endDate||lay.extend({},options.dateTime,function(){var obj={},dateTime=options.dateTime,EYM=that.getAsYM(dateTime.year,dateTime.month);if(options.type==="year"){obj.year=dateTime.year+1}else{if(options.type!=="time"){obj.year=EYM[0];obj.month=EYM[1]}}if(options.type==="datetime"||options.type==="time"){obj.hours=23;obj.minutes=obj.seconds=59}return obj}())}};getEndDate();if(typeof value==="string"&&value){if(that.EXP_IF.test(value)){if(options.range){value=value.split(" "+that.rangeStr+" ");lay.each([options.dateTime,that.endDate],function(i,item){initDate(item,value[i],i)})}else{initDate(dateTime,value)}}else{that.hint(lang.formatError[0]+(options.range?(options.format+" "+that.rangeStr+" "+options.format):options.format)+lang.formatError[1]);error=true}}else{if(value&&layui.type(value)==="date"){options.dateTime=that.systemDate(value)}else{options.dateTime=that.systemDate();delete that.startTime;delete that.endDate;getEndDate();delete that.endTime}}(function(){if(that.rangeElem){var vals=[that.rangeElem[0].val(),that.rangeElem[1].val()],arrDate=[options.dateTime,that.endDate];lay.each(vals,function(_i,_v){if(that.EXP_IF_ONE.test(_v)){initDate(arrDate[_i],_v,_i)}})}})();checkValid(dateTime);if(options.range){checkValid(that.endDate)}if(error&&value){that.setValue(options.range?(that.endDate?that.parse():""):that.parse())}var minMaxError;if(that.getDateTime(dateTime)>that.getDateTime(options.max)){dateTime=options.dateTime=lay.extend({},options.max);minMaxError=true}else{if(that.getDateTime(dateTime)<that.getDateTime(options.min)){dateTime=options.dateTime=lay.extend({},options.min);minMaxError=true}}if(options.range){if(that.getDateTime(that.endDate)<that.getDateTime(options.min)||that.getDateTime(that.endDate)>that.getDateTime(options.max)){that.endDate=lay.extend({},options.max);minMaxError=true}that.startTime={hours:options.dateTime.hours,minutes:options.dateTime.minutes,seconds:options.dateTime.seconds,};that.endTime={hours:that.endDate.hours,minutes:that.endDate.minutes,seconds:that.endDate.seconds,};if(options.type==="month"){options.dateTime.date=1;that.endDate.date=1}}if(minMaxError&&value){that.setValue(that.parse());that.hint("value "+lang.invalidDate+lang.formatError[1])}that.startDate=that.startDate||value&&lay.extend({},options.dateTime);that.autoCalendarModel.auto&&that.autoCalendarModel();that.endState=!options.range||!that.rangeLinked||!!(that.startDate&&that.endDate);fn&&fn();return that};Class.prototype.mark=function(td,YMD){var that=this,mark,options=that.config;lay.each(options.mark,function(key,title){var keys=key.split("-");if((keys[0]==YMD[0]||keys[0]==0)&&(keys[1]==YMD[1]||keys[1]==0)&&keys[2]==YMD[2]){mark=title||YMD[2]}});mark&&td.find("div").html('<span class="laydate-day-mark">'+mark+"</span>");return that};Class.prototype.holidays=function(td,YMD){var that=this;var options=that.config;var type=["","work"];if(layui.type(options.holidays)!=="array"){return that}lay.each(options.holidays,function(idx,item){lay.each(item,function(i,dayStr){if(dayStr===td.attr("lay-ymd")){td.find("div").html('<span class="laydate-day-holidays"'+(type[idx]?('type="'+type[idx]+'"'):"")+">"+YMD[2]+"</span>")}})});return that};Class.prototype.limit=function(opts){opts=opts||{};var that=this;var options=that.config;var timestamp={};var dateTime=opts.index>(opts.time?0:41)?that.endDate:options.dateTime;var isOut;lay.each({now:lay.extend({},dateTime,opts.date||{}),min:options.min,max:options.max},function(key,item){timestamp[key]=that.newDate(lay.extend({year:item.year,month:opts.type==="year"?0:item.month,date:(opts.type==="year"||opts.type==="month")?1:item.date},function(){var hms={};lay.each(opts.time,function(i,keys){hms[keys]=item[keys]});return hms}())).getTime()});isOut=timestamp.now<timestamp.min||timestamp.now>timestamp.max;opts.elem&&opts.elem[isOut?"addClass":"removeClass"](DISABLED);return isOut};Class.prototype.thisDateTime=function(index){var that=this,options=that.config;return index?that.endDate:options.dateTime};Class.prototype.calendar=function(value,index,type){index=index?1:0;var that=this,options=that.config,dateTime=value||that.thisDateTime(index),thisDate=new Date(),startWeek,prevMaxDate,thisMaxDate,lang=that.lang(),isAlone=options.type!=="date"&&options.type!=="datetime",tds=lay(that.table[index]).find("td"),elemYM=lay(that.elemHeader[index][2]).find("span");if(dateTime.year<LIMIT_YEAR[0]){dateTime.year=LIMIT_YEAR[0],that.hint(lang.invalidDate)}if(dateTime.year>LIMIT_YEAR[1]){dateTime.year=LIMIT_YEAR[1],that.hint(lang.invalidDate)}if(!that.firstDate){that.firstDate=lay.extend({},dateTime)}thisDate.setFullYear(dateTime.year,dateTime.month,1);startWeek=(thisDate.getDay()+(7-options.weekStart))%7;prevMaxDate=laydate.getEndDate(dateTime.month||12,dateTime.year);thisMaxDate=laydate.getEndDate(dateTime.month+1,dateTime.year);lay.each(tds,function(index_,item){var YMD=[dateTime.year,dateTime.month],st;item=lay(item);item.removeAttr("class");if(index_<startWeek){st=prevMaxDate-startWeek+index_;item.addClass("laydate-day-prev");YMD=that.getAsYM(dateTime.year,dateTime.month,"sub")}else{if(index_>=startWeek&&index_<thisMaxDate+startWeek){st=index_-startWeek;if(!that.rangeLinked){st+1===dateTime.date&&item.addClass(THIS)}}else{st=index_-thisMaxDate-startWeek;item.addClass("laydate-day-next");YMD=that.getAsYM(dateTime.year,dateTime.month)}}YMD[1]++;YMD[2]=st+1;item.attr("lay-ymd",YMD.join("-")).html("<div>"+YMD[2]+"</div>");that.mark(item,YMD).holidays(item,YMD).limit({elem:item,date:{year:YMD[0],month:YMD[1]-1,date:YMD[2]},index:index_})});lay(elemYM[0]).attr("lay-ym",dateTime.year+"-"+(dateTime.month+1));lay(elemYM[1]).attr("lay-ym",dateTime.year+"-"+(dateTime.month+1));if(options.lang==="cn"){lay(elemYM[0]).attr("lay-type","year").html(dateTime.year+" 年");lay(elemYM[1]).attr("lay-type","month").html((dateTime.month+1)+" 月")}else{lay(elemYM[0]).attr("lay-type","month").html(lang.month[dateTime.month]);lay(elemYM[1]).attr("lay-type","year").html(dateTime.year)}if(isAlone){if(options.range){if(value||type!=="init"){that.listYM=[[(that.startDate||options.dateTime).year,(that.startDate||options.dateTime).month+1],[that.endDate.year,that.endDate.month+1]];that.list(options.type,0).list(options.type,1);options.type==="time"?that.setBtnStatus("时间",lay.extend({},that.systemDate(),that.startTime),lay.extend({},that.systemDate(),that.endTime)):that.setBtnStatus(true)}}else{that.listYM=[[dateTime.year,dateTime.month+1]];that.list(options.type,0)}}if(options.range&&type==="init"){if(that.rangeLinked){var EYM=that.getAsYM(dateTime.year,dateTime.month,index?"sub":null);that.calendar(lay.extend({},dateTime,{year:EYM[0],month:EYM[1]}),1-index)}else{that.calendar(null,1-index)}}if(!options.range){var timeParams=["hours","minutes","seconds"];that.limit({elem:lay(that.footer).find(ELEM_NOW),date:that.systemDate(/^(datetime|time)$/.test(options.type)?new Date():null),index:0,time:timeParams});that.limit({elem:lay(that.footer).find(ELEM_CONFIRM),index:0,time:timeParams})}that.setBtnStatus();lay(that.shortcut).find("li."+THIS).removeClass(THIS);if(options.range&&!isAlone&&type!=="init"){that.stampRange()}return that};Class.prototype.list=function(type,index){var that=this,options=that.config,dateTime=that.rangeLinked?options.dateTime:[options.dateTime,that.endDate][index],lang=that.lang(),isAlone=options.range&&options.type!=="date"&&options.type!=="datetime",ul=lay.elem("ul",{"class":ELEM_LIST+" "+({year:"laydate-year-list",month:"laydate-month-list",time:"laydate-time-list"})[type]}),elemHeader=that.elemHeader[index],elemYM=lay(elemHeader[2]).find("span"),elemCont=that.elemCont[index||0],haveList=lay(elemCont).find("."+ELEM_LIST)[0],isCN=options.lang==="cn",text=isCN?"年":"",listYM=that.listYM[index]||{},hms=["hours","minutes","seconds"],startEnd=["startTime","endTime"][index];if(listYM[0]<1){listYM[0]=1}if(type==="year"){var yearNum,startY=yearNum=listYM[0]-7;if(startY<1){startY=yearNum=1}lay.each(new Array(15),function(i){var li=lay.elem("li",{"lay-ym":yearNum}),ymd={year:yearNum,month:0,date:1};yearNum==listYM[0]&&lay(li).addClass(THIS);li.innerHTML=yearNum+text;ul.appendChild(li);that.limit({elem:lay(li),date:ymd,index:index,type:type});yearNum++});lay(elemYM[isCN?0:1]).attr("lay-ym",(yearNum-8)+"-"+listYM[1]).html((startY+text)+" - "+(yearNum-1+text))}else{if(type==="month"){lay.each(new Array(12),function(i){var li=lay.elem("li",{"lay-ym":i}),ymd={year:listYM[0],month:i,date:1};i+1==listYM[1]&&lay(li).addClass(THIS);li.innerHTML=lang.month[i]+(isCN?"月":"");ul.appendChild(li);that.limit({elem:lay(li),date:ymd,index:index,type:type})});lay(elemYM[isCN?0:1]).attr("lay-ym",listYM[0]+"-"+listYM[1]).html(listYM[0]+text)}else{if(type==="time"){var setTimeStatus=function(){lay(ul).find("ol").each(function(i,ol){lay(ol).find("li").each(function(ii,li){that.limit({elem:lay(li),date:[{hours:ii},{hours:that[startEnd].hours,minutes:ii},{hours:that[startEnd].hours,minutes:that[startEnd].minutes,seconds:ii}][i],index:index,time:[["hours"],["hours","minutes"],["hours","minutes","seconds"]][i]})})});if(!options.range){that.limit({elem:lay(that.footer).find(ELEM_CONFIRM),date:that[startEnd],inedx:0,time:["hours","minutes","seconds"]})}};if(options.range){if(!that[startEnd]){that[startEnd]=startEnd==="startTime"?dateTime:that.endDate}}else{that[startEnd]=dateTime}lay.each([24,60,60],function(i,item){var li=lay.elem("li"),childUL=["<p>"+lang.time[i]+"</p><ol>"];lay.each(new Array(item),function(ii){childUL.push("<li"+(that[startEnd][hms[i]]===ii?' class="'+THIS+'"':"")+">"+lay.digit(ii,2)+"</li>")});li.innerHTML=childUL.join("")+"</ol>";ul.appendChild(li)});setTimeStatus()}}}if(haveList){elemCont.removeChild(haveList)}elemCont.appendChild(ul);if(type==="year"||type==="month"){lay(that.elemMain[index]).addClass("laydate-ym-show");lay(ul).find("li").on("click",function(){var ym=lay(this).attr("lay-ym")|0;if(lay(this).hasClass(DISABLED)){return}if(that.rangeLinked){lay.extend(dateTime,{year:type==="year"?ym:listYM[0],month:type==="year"?listYM[1]-1:ym})}else{dateTime[type]=ym}var isYearOrMonth=options.type==="year"||options.type==="month";if(isYearOrMonth){lay(ul).find("."+THIS).removeClass(THIS);lay(this).addClass(THIS);if(options.type==="month"&&type==="year"){that.listYM[index][0]=ym;isAlone&&((index?that.endDate:dateTime).year=ym);that.list("month",index)}}else{that.checkDate("limit").calendar(dateTime,index,"init");that.closeList()}that.setBtnStatus();if(!options.range&&options.autoConfirm){if((options.type==="month"&&type==="month")||(options.type==="year"&&type==="year")){that.setValue(that.parse()).done().remove()}}(that.autoCalendarModel.auto&&!that.rangeLinked)?that.choose(lay(elemCont).find("td.layui-this"),index):(that.endState&&that.done(null,"change"));lay(that.footer).find("."+ELEM_TIME_BTN).removeClass(DISABLED)})}else{var span=lay.elem("span",{"class":ELEM_TIME_TEXT}),scroll=function(){lay(ul).find("ol").each(function(i){var ol=this,li=lay(ol).find("li");ol.scrollTop=30*(that[startEnd][hms[i]]-2);if(ol.scrollTop<=0){li.each(function(ii,item){if(!lay(this).hasClass(DISABLED)){ol.scrollTop=30*(ii-2);return true}})}})},haveSpan=lay(elemHeader[2]).find("."+ELEM_TIME_TEXT);scroll();span.innerHTML=options.range?[lang.startTime,lang.endTime][index]:lang.timeTips;lay(that.elemMain[index]).addClass("laydate-time-show");if(haveSpan[0]){haveSpan.remove()}elemHeader[2].appendChild(span);lay(ul).find("ol").each(function(i){var ol=this;lay(ol).find("li").on("click",function(){var value=this.innerHTML|0;if(lay(this).hasClass(DISABLED)){return}if(options.range){that[startEnd][hms[i]]=value}else{dateTime[hms[i]]=value}lay(ol).find("."+THIS).removeClass(THIS);lay(this).addClass(THIS);setTimeStatus();scroll();(that.endDate||options.type==="time"||(options.type==="datetime"&&options.fullPanel))&&that.done(null,"change");that.setBtnStatus()})})}return that};Class.prototype.listYM=[];Class.prototype.closeList=function(){var that=this,options=that.config;lay.each(that.elemCont,function(index,item){lay(this).find("."+ELEM_LIST).remove();lay(that.elemMain[index]).removeClass("laydate-ym-show laydate-time-show")});lay(that.elem).find("."+ELEM_TIME_TEXT).remove()};Class.prototype.setBtnStatus=function(tips,start,end){var that=this,options=that.config,lang=that.lang(),isOut,elemBtn=lay(that.footer).find(ELEM_CONFIRM);if(options.range&&options.type!=="time"){start=start||(that.rangeLinked?that.startDate:options.dateTime);end=end||that.endDate;isOut=!that.endState||that.newDate(start).getTime()>that.newDate(end).getTime();(that.limit({date:start})||that.limit({date:end}))?elemBtn.addClass(DISABLED):elemBtn[isOut?"addClass":"removeClass"](DISABLED);if(tips&&isOut){that.hint(typeof tips==="string"?lang.timeout.replace(/日期/g,tips):lang.timeout)}}};Class.prototype.parse=function(state,date){var that=this;var options=that.config;var startDate=(that.rangeLinked?that.startDate:options.dateTime);var dateTime=date||(state=="end"?lay.extend({},that.endDate,that.endTime):(options.range?lay.extend({},startDate||options.dateTime,that.startTime):options.dateTime));var format=laydate.parse(dateTime,that.format,1);if(options.range&&state===undefined){return format+" "+that.rangeStr+" "+that.parse("end")}return format};Class.prototype.newDate=function(dateTime){dateTime=dateTime||{};return new Date(dateTime.year||1,dateTime.month||0,dateTime.date||1,dateTime.hours||0,dateTime.minutes||0,dateTime.seconds||0)};Class.prototype.getDateTime=function(obj){return this.newDate(obj).getTime()};Class.prototype.setValue=function(value){var that=this,options=that.config,elem=options.elem[0];if(options.position==="static"){return that}value=value||"";if(that.isInput(elem)){lay(elem).val(value)}else{var rangeElem=that.rangeElem;if(rangeElem){if(layui.type(value)!=="array"){value=value.split(" "+that.rangeStr+" ")}rangeElem[0].val(value[0]||"");rangeElem[1].val(value[1]||"")}else{if(lay(elem).find("*").length===0){lay(elem).html(value)}lay(elem).attr("lay-date",value)}}return that};Class.prototype.preview=function(){var that=this,options=that.config;if(!options.isPreview){return}var elemPreview=lay(that.elem).find("."+ELEM_PREVIEW),value=options.range?((that.rangeLinked?that.endState:that.endDate)?that.parse():""):that.parse();elemPreview.html(value);var oldValue=elemPreview.html();oldValue&&(elemPreview.css({"color":"#16b777"}),setTimeout(function(){elemPreview.css({"color":"#777"})},300))};Class.prototype.renderAdditional=function(){var that=this;var options=that.config;if(options.fullPanel){that.list("time",0)}};Class.prototype.stampRange=function(){var that=this,options=that.config,startTime=that.rangeLinked?that.startDate:options.dateTime,endTime,tds=lay(that.elem).find("td");if(options.range&&!that.endState){lay(that.footer).find(ELEM_CONFIRM).addClass(DISABLED)}startTime=startTime&&that.newDate({year:startTime.year,month:startTime.month,date:startTime.date}).getTime();endTime=that.endState&&that.endDate&&that.newDate({year:that.endDate.year,month:that.endDate.month,date:that.endDate.date}).getTime();lay.each(tds,function(i,item){var ymd=lay(item).attr("lay-ymd").split("-");var thisTime=that.newDate({year:ymd[0],month:ymd[1]-1,date:ymd[2]}).getTime();if(options.rangeLinked&&!that.startDate){if(thisTime===that.newDate(that.systemDate()).getTime()){lay(item).addClass(lay(item).hasClass(ELEM_PREV)||lay(item).hasClass(ELEM_NEXT)?"":ELEM_DAY_NOW)}}lay(item).removeClass(ELEM_SELECTED+" "+THIS);if(thisTime===startTime||thisTime===endTime){(that.rangeLinked||(!that.rangeLinked&&(i<42?thisTime===startTime:thisTime===endTime)))&&lay(item).addClass(lay(item).hasClass(ELEM_PREV)||lay(item).hasClass(ELEM_NEXT)?ELEM_SELECTED:THIS)}if(thisTime>startTime&&thisTime<endTime){lay(item).addClass(ELEM_SELECTED)}})};Class.prototype.done=function(param,type){var that=this;var options=that.config;var start=lay.extend({},lay.extend(that.rangeLinked?that.startDate:options.dateTime,that.startTime));var end=lay.extend({},lay.extend(that.endDate,that.endTime));lay.each([start,end],function(i,item){if(!("month" in item)){return}lay.extend(item,{month:item.month+1})});that.preview();param=param||[that.parse(),start,end];type==="change"&&that.renderAdditional();typeof options[type||"done"]==="function"&&options[type||"done"].apply(options,param);return that};Class.prototype.choose=function(td,index){if(td.hasClass(DISABLED)){return}var that=this,options=that.config,panelIndex=index;if(that.rangeLinked){if(that.endState||!that.startDate){index=0;that.endState=false;that.endDate={}}else{index=1;that.endState=true}}var dateTime=that.thisDateTime(index),tds=lay(that.elem).find("td"),YMD=td.attr("lay-ymd").split("-");YMD={year:YMD[0]|0,month:(YMD[1]|0)-1,date:YMD[2]|0};lay.extend(dateTime,YMD);if(options.range){lay.each(["startTime","endTime"],function(i,item){that[item]=that[item]||{hours:i?23:0,minutes:i?59:0,seconds:i?59:0};if(index===i){if(that.getDateTime(lay.extend({},dateTime,that[item]))<that.getDateTime(options.min)){that[item]={hours:options.min.hours,minutes:options.min.minutes,seconds:options.min.seconds};lay.extend(dateTime,that[item])}else{if(that.getDateTime(lay.extend({},dateTime,that[item]))>that.getDateTime(options.max)){that[item]={hours:options.max.hours,minutes:options.max.minutes,seconds:options.max.seconds};lay.extend(dateTime,that[item])}}}});if(!index){that.startDate=lay.extend({},dateTime)}if(that.endState&&!that.limit({date:that.thisDateTime(1-index)})){var isChange;if(that.endState&&that.autoCalendarModel.auto){isChange=that.autoCalendarModel()}if((isChange||that.rangeLinked&&that.endState)&&that.newDate(that.startDate)>that.newDate(that.endDate)){var isSameDate=that.startDate.year===that.endDate.year&&that.startDate.month===that.endDate.month&&that.startDate.date===that.endDate.date;var startDate=that.startDate;that.startDate=lay.extend({},that.endDate,isSameDate?{}:that.startTime);options.dateTime=lay.extend({},that.startDate);that.endDate=lay.extend({},startDate,isSameDate?{}:that.endTime);isSameDate&&(startDate=that.startTime,that.startTime=that.endTime,that.endTime=startDate)}isChange&&(options.dateTime=lay.extend({},that.startDate))}if(that.rangeLinked){var dateTimeTemp=lay.extend({},dateTime);if(panelIndex&&!index&&!isChange){var YM=that.getAsYM(dateTime.year,dateTime.month,"sub");lay.extend(options.dateTime,{year:YM[0],month:YM[1]})}that.calendar(dateTimeTemp,panelIndex,isChange?"init":null)}else{that.calendar(null,index,isChange?"init":null)}that.endState&&that.done(null,"change")}else{if(options.position==="static"){that.calendar().done().done(null,"change")}else{if(options.type==="date"){options.autoConfirm?that.setValue(that.parse()).done().remove():that.calendar().done(null,"change")}else{if(options.type==="datetime"){that.calendar().done(null,"change")}}}}};Class.prototype.tool=function(btn,type){var that=this,options=that.config,lang=that.lang(),dateTime=options.dateTime,isStatic=options.position==="static",active={datetime:function(){if(lay(btn).hasClass(DISABLED)){return}that.list("time",0);options.range&&that.list("time",1);lay(btn).attr("lay-type","date").html(that.lang().dateTips)},date:function(){that.closeList();lay(btn).attr("lay-type","datetime").html(that.lang().timeTips)},clear:function(){isStatic&&(lay.extend(dateTime,that.firstDate),that.calendar());options.range&&(delete options.dateTime,delete that.endDate,delete that.startTime,delete that.endTime);that.setValue("");that.done(null,"onClear").done(["",{},{}]).remove()},now:function(){var thisDate=new Date();if(lay(btn).hasClass(DISABLED)){return that.hint(lang.tools.now+", "+lang.invalidDate)}lay.extend(dateTime,that.systemDate(),{hours:thisDate.getHours(),minutes:thisDate.getMinutes(),seconds:thisDate.getSeconds()});that.setValue(that.parse());isStatic&&that.calendar();that.done(null,"onNow").done().remove()},confirm:function(){if(options.range){if(lay(btn).hasClass(DISABLED)){return that.hint(options.type==="time"?lang.timeout.replace(/日期/g,"时间"):lang.timeout)}}else{if(lay(btn).hasClass(DISABLED)){return that.hint(lang.invalidDate)}}that.setValue(that.parse());that.done(null,"onConfirm").done().remove()}};active[type]&&active[type]()};Class.prototype.change=function(index){var that=this,options=that.config,dateTime=that.thisDateTime(index),isAlone=options.range&&(options.type==="year"||options.type==="month"),elemCont=that.elemCont[index||0],listYM=that.listYM[index],addSubYear=function(type){var isYear=lay(elemCont).find(".laydate-year-list")[0],isMonth=lay(elemCont).find(".laydate-month-list")[0];if(isYear){listYM[0]=type?listYM[0]-15:listYM[0]+15;that.list("year",index)}if(isMonth){type?listYM[0]--:listYM[0]++;that.list("month",index)}if(isYear||isMonth){lay.extend(dateTime,{year:listYM[0]});if(isAlone){dateTime.year=listYM[0]}options.range||that.done(null,"change");options.range||that.limit({elem:lay(that.footer).find(ELEM_CONFIRM),date:{year:listYM[0]}})}that.setBtnStatus();return isYear||isMonth};return{prevYear:function(){if(addSubYear("sub")){return}if(that.rangeLinked){options.dateTime.year--;that.checkDate("limit").calendar(null,null,"init")}else{dateTime.year--;that.checkDate("limit").calendar(null,index);that.autoCalendarModel.auto?that.choose(lay(elemCont).find("td.layui-this"),index):that.done(null,"change")}},prevMonth:function(){if(that.rangeLinked){dateTime=options.dateTime}var YM=that.getAsYM(dateTime.year,dateTime.month,"sub");lay.extend(dateTime,{year:YM[0],month:YM[1]});that.checkDate("limit").calendar(null,null,"init");if(!that.rangeLinked){that.autoCalendarModel.auto?that.choose(lay(elemCont).find("td.layui-this"),index):that.done(null,"change")}},nextMonth:function(){if(that.rangeLinked){dateTime=options.dateTime}var YM=that.getAsYM(dateTime.year,dateTime.month);lay.extend(dateTime,{year:YM[0],month:YM[1]});that.checkDate("limit").calendar(null,null,"init");if(!that.rangeLinked){that.autoCalendarModel.auto?that.choose(lay(elemCont).find("td.layui-this"),index):that.done(null,"change")}},nextYear:function(){if(addSubYear()){return}if(that.rangeLinked){options.dateTime.year++;that.checkDate("limit").calendar(null,0,"init")}else{dateTime.year++;that.checkDate("limit").calendar(null,index);that.autoCalendarModel.auto?that.choose(lay(elemCont).find("td.layui-this"),index):that.done(null,"change")}}}};Class.prototype.changeEvent=function(){var that=this,options=that.config;lay(that.elem).on("click",function(e){lay.stope(e)}).on("mousedown",function(e){lay.stope(e)});lay.each(that.elemHeader,function(i,header){lay(header[0]).on("click",function(e){that.change(i).prevYear()});lay(header[1]).on("click",function(e){that.change(i).prevMonth()});lay(header[2]).find("span").on("click",function(e){var othis=lay(this),layYM=othis.attr("lay-ym"),layType=othis.attr("lay-type");if(!layYM){return}layYM=layYM.split("-");that.listYM[i]=[layYM[0]|0,layYM[1]|0];that.list(layType,i);lay(that.footer).find("."+ELEM_TIME_BTN).addClass(DISABLED)});lay(header[3]).on("click",function(e){that.change(i).nextMonth()});lay(header[4]).on("click",function(e){that.change(i).nextYear()})});lay.each(that.table,function(i,table){var tds=lay(table).find("td");tds.on("click",function(){that.choose(lay(this),i)})});lay(that.footer).find("span").on("click",function(){var type=lay(this).attr("lay-type");that.tool(this,type)})};Class.prototype.isInput=function(elem){return/input|textarea/.test(elem.tagName.toLocaleLowerCase())||/INPUT|TEXTAREA/.test(elem.tagName)};Class.prototype.events=function(){var that=this;var options=that.config;if(!options.elem[0]||options.elem[0].eventHandler){return}var showEvent=function(){if(laydate.thisId===options.id){return}that.render()};options.elem.on(options.trigger,showEvent);options.elem[0].eventHandler=true;options.eventElem.on(options.trigger,showEvent);that.unbind=function(){that.remove();options.elem.off(options.trigger,showEvent);options.elem.removeAttr("lay-key");options.elem.removeAttr(MOD_ID);options.elem[0].eventHandler=false;options.eventElem.off(options.trigger,showEvent);options.eventElem.removeAttr("lay-key");delete thisModule.that[options.id]}};thisModule.that={};thisModule.getThis=function(id){var that=thisModule.that[id];if(!that&&isLayui){layui.hint().error(id?(MOD_NAME+" instance with ID '"+id+"' not found"):"ID argument required")}return that};ready.run=function(lay){lay(document).on("mousedown",function(e){if(!laydate.thisId){return}var that=thisModule.getThis(laydate.thisId);if(!that){return}var options=that.config;if(e.target===options.elem[0]||e.target===options.eventElem[0]||e.target===lay(options.closeStop)[0]||(options.elem[0]&&options.elem[0].contains(e.target))){return}that.remove()}).on("keydown",function(e){if(!laydate.thisId){return}var that=thisModule.getThis(laydate.thisId);if(!that){return}if(that.config.position==="static"){return}if(e.keyCode===13){if(lay("#"+that.elemID)[0]&&that.elemID===Class.thisElemDate){e.preventDefault();lay(that.footer).find(ELEM_CONFIRM)[0].click()}}});lay(window).on("resize",function(){if(!laydate.thisId){return}var that=thisModule.getThis(laydate.thisId);if(!that){return}if(!that.elem||!lay(ELEM)[0]){return false}that.position()})};laydate.render=function(options){var inst=new Class(options);return thisModule.call(inst)};laydate.reload=function(id,options){var that=thisModule.getThis(id);if(!that){return}return that.reload(options)};laydate.getInst=function(id){var that=thisModule.getThis(id);if(that){return that.inst}};laydate.hint=function(id,opts){var that=thisModule.getThis(id);if(!that){return}return that.hint(opts)};laydate.unbind=function(id){var that=thisModule.getThis(id);if(!that){return}return that.unbind()};laydate.close=function(id){var that=thisModule.getThis(id||laydate.thisId);if(!that){return}return that.remove()};laydate.parse=function(dateTime,format,one){dateTime=dateTime||{};if(typeof format==="string"){format=thisModule.formatArr(format)}format=(format||[]).concat();lay.each(format,function(i,item){if(/yyyy|y/.test(item)){format[i]=lay.digit(dateTime.year,item.length)}else{if(/MM|M/.test(item)){format[i]=lay.digit(dateTime.month+(one||0),item.length)}else{if(/dd|d/.test(item)){format[i]=lay.digit(dateTime.date,item.length)}else{if(/HH|H/.test(item)){format[i]=lay.digit(dateTime.hours,item.length)}else{if(/mm|m/.test(item)){format[i]=lay.digit(dateTime.minutes,item.length)}else{if(/ss|s/.test(item)){format[i]=lay.digit(dateTime.seconds,item.length)}}}}}}});return format.join("")};laydate.getEndDate=function(month,year){var thisDate=new Date();thisDate.setFullYear(year||thisDate.getFullYear(),month||(thisDate.getMonth()+1),1);return new Date(thisDate.getTime()-1000*60*60*24).getDate()};isLayui?(laydate.ready(),layui.define("lay",function(exports){laydate.path=layui.cache.dir;ready.run(lay);exports(MOD_NAME,laydate)})):((typeof define==="function"&&define.amd)?define(function(){ready.run(lay);return laydate}):function(){laydate.ready();ready.run(window.lay);window.laydate=laydate}())}(window,window.document);