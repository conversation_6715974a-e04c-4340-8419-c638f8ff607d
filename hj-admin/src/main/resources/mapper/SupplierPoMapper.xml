<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierPoMapper">
    
    <resultMap type="SupplierPo" id="SupplierPoResult">
        <result property="id"    column="id"    />
        <result property="purchaseOrder"    column="purchase_order"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="plantId"    column="plant_id"    />
        <result property="plantName"    column="plant_name"    />
        <result property="voucherDate"    column="voucher_date"    />
        <result property="purchaser"    column="purchaser"    />
        <result property="supplier"    column="supplier"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="quantityDemand"    column="quantity_demand"    />
        <result property="materialUnit"    column="material_unit"    />
        <result property="deliveryDate"    column="delivery_date"    />
        <result property="deliveryPlace"    column="delivery_place"    />
        <result property="quantityDelivery"    column="quantity_delivery"    />
        <result property="note"    column="note"    />
        <result property="itemType"    column="item_type"    />
        <result property="tradeTerms"    column="trade_terms"    />
        <result property="country"    column="country"    />
        <result property="batch"    column="batch"    />
        <result property="createByUser"    column="create_by_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateByUser"    column="update_by_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="version"    column="version"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierPoVo">
        select id, purchase_order, serial_number, plant_id, plant_name, voucher_date, purchaser, supplier, material_code, material_description, quantity_demand, material_unit, delivery_date, delivery_place, quantity_delivery, note, item_type, trade_terms, country, batch, create_by_user, create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by from supplier_po
    </sql>

    <select id="selectSupplierPoList" parameterType="SupplierPo" resultMap="SupplierPoResult">
        <include refid="selectSupplierPoVo"/>
        <where>  
            <if test="purchaseOrder != null  and purchaseOrder != ''"> and purchase_order = #{purchaseOrder}</if>
            <if test="serialNumber != null  and serialNumber != ''"> and serial_number = #{serialNumber}</if>
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="plantName != null  and plantName != ''"> and plant_name like concat('%', #{plantName}, '%')</if>
            <if test="voucherDate != null  and voucherDate != ''"> and voucher_date = #{voucherDate}</if>
            <if test="purchaser != null  and purchaser != ''"> and purchaser = #{purchaser}</if>
            <if test="supplier != null  and supplier != ''"> and supplier = #{supplier}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="quantityDemand != null "> and quantity_demand = #{quantityDemand}</if>
            <if test="materialUnit != null  and materialUnit != ''"> and material_unit = #{materialUnit}</if>
            <if test="deliveryDate != null  and deliveryDate != ''"> and delivery_date = #{deliveryDate}</if>
            <if test="deliveryPlace != null  and deliveryPlace != ''"> and delivery_place = #{deliveryPlace}</if>
            <if test="quantityDelivery != null "> and quantity_delivery = #{quantityDelivery}</if>
            <if test="note != null  and note != ''"> and note = #{note}</if>
            <if test="itemType != null  and itemType != ''"> and item_type = #{itemType}</if>
            <if test="tradeTerms != null  and tradeTerms != ''"> and trade_terms = #{tradeTerms}</if>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="batch != null  and batch != ''"> and batch = #{batch}</if>
            <if test="createByUser != null  and createByUser != ''"> and create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''"> and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierPoById" parameterType="Long" resultMap="SupplierPoResult">
        <include refid="selectSupplierPoVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierPo" parameterType="SupplierPo">
        insert into supplier_po
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="purchaseOrder != null">purchase_order,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="plantId != null">plant_id,</if>
            <if test="plantName != null">plant_name,</if>
            <if test="voucherDate != null">voucher_date,</if>
            <if test="purchaser != null">purchaser,</if>
            <if test="supplier != null">supplier,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="quantityDemand != null">quantity_demand,</if>
            <if test="materialUnit != null">material_unit,</if>
            <if test="deliveryDate != null">delivery_date,</if>
            <if test="deliveryPlace != null">delivery_place,</if>
            <if test="quantityDelivery != null">quantity_delivery,</if>
            <if test="note != null">note,</if>
            <if test="itemType != null">item_type,</if>
            <if test="tradeTerms != null">trade_terms,</if>
            <if test="country != null">country,</if>
            <if test="batch != null">batch,</if>
            <if test="createByUser != null">create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="purchaseOrder != null">#{purchaseOrder},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="plantId != null">#{plantId},</if>
            <if test="plantName != null">#{plantName},</if>
            <if test="voucherDate != null">#{voucherDate},</if>
            <if test="purchaser != null">#{purchaser},</if>
            <if test="supplier != null">#{supplier},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="quantityDemand != null">#{quantityDemand},</if>
            <if test="materialUnit != null">#{materialUnit},</if>
            <if test="deliveryDate != null">#{deliveryDate},</if>
            <if test="deliveryPlace != null">#{deliveryPlace},</if>
            <if test="quantityDelivery != null">#{quantityDelivery},</if>
            <if test="note != null">#{note},</if>
            <if test="itemType != null">#{itemType},</if>
            <if test="tradeTerms != null">#{tradeTerms},</if>
            <if test="country != null">#{country},</if>
            <if test="batch != null">#{batch},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">#{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierPo" parameterType="SupplierPo">
        update supplier_po
        <trim prefix="SET" suffixOverrides=",">
            <if test="purchaseOrder != null">purchase_order = #{purchaseOrder},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="plantId != null">plant_id = #{plantId},</if>
            <if test="plantName != null">plant_name = #{plantName},</if>
            <if test="voucherDate != null">voucher_date = #{voucherDate},</if>
            <if test="purchaser != null">purchaser = #{purchaser},</if>
            <if test="supplier != null">supplier = #{supplier},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="quantityDemand != null">quantity_demand = #{quantityDemand},</if>
            <if test="materialUnit != null">material_unit = #{materialUnit},</if>
            <if test="deliveryDate != null">delivery_date = #{deliveryDate},</if>
            <if test="deliveryPlace != null">delivery_place = #{deliveryPlace},</if>
            <if test="quantityDelivery != null">quantity_delivery = #{quantityDelivery},</if>
            <if test="note != null">note = #{note},</if>
            <if test="itemType != null">item_type = #{itemType},</if>
            <if test="tradeTerms != null">trade_terms = #{tradeTerms},</if>
            <if test="country != null">country = #{country},</if>
            <if test="batch != null">batch = #{batch},</if>
            <if test="createByUser != null">create_by_user = #{createByUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierPoById" parameterType="Long">
        delete from supplier_po where id = #{id}
    </delete>

    <delete id="deleteSupplierPoByIds" parameterType="String">
        delete from supplier_po where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>