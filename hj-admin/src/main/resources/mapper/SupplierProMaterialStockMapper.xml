<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierProMaterialStockMapper">
    
    <resultMap type="SupplierProMaterialStock" id="SupplierProMaterialStockResult">
        <result property="id"    column="id"    />
        <result property="supplierCode"    column="supplier_code"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="supplierSubCode"    column="supplier_sub_code"    />
        <result property="supplierSubName"    column="supplier_sub_name"    />
        <result property="subSupplierCode"    column="sub_supplier_code"    />
        <result property="subSupplierName"    column="sub_supplier_name"    />
        <result property="subSupplierAddress"    column="sub_supplier_address"    />
        <result property="componentCode"    column="component_code"    />
        <result property="componentName"    column="component_name"    />
        <result property="subBatchNo"    column="sub_batch_no"    />
        <result property="subBatchNum"    column="sub_batch_num"    />
        <result property="subBatchSn"    column="sub_batch_sn"    />
        <result property="empCode"    column="emp_code"    />
        <result property="empName"    column="emp_name"    />
        <result property="deviceCode"    column="device_code"    />
        <result property="deviceName"    column="device_name"    />
        <result property="featureName"    column="feature_name"    />
        <result property="featureUnit"    column="feature_unit"    />
        <result property="standardValue"    column="standard_value"    />
        <result property="featureUpper"    column="feature_upper"    />
        <result property="featureLower"    column="feature_lower"    />
        <result property="featureValue"    column="feature_value"    />
        <result property="checkNo"    column="check_no"    />
        <result property="checkResult"    column="check_result"    />
        <result property="checkTime"    column="check_time"    />
        <result property="samplingRate"    column="sampling_rate"    />
        <result property="limitUpdateTime"    column="limit_update_time"    />
        <result property="vendorFieldDesc"    column="vendor_field_desc"    />
        <result property="vendorFieldCode"    column="vendor_field_code"    />
        <result property="deadLine"    column="dead_line"    />
        <result property="hasPush"    column="has_push"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierProMaterialStockVo">
        select id, supplier_code, supplier_name, supplier_sub_code, supplier_sub_name, sub_supplier_code, sub_supplier_name, sub_supplier_address, component_code, component_name, sub_batch_no, sub_batch_num, sub_batch_sn, emp_code, emp_name, device_code, device_name, feature_name, feature_unit, standard_value, feature_upper, feature_lower, feature_value, check_no, check_result, check_time, sampling_rate, limit_update_time, vendor_field_desc, vendor_field_code, dead_line, has_push, insert_time, modify_time, insert_by, modify_by from supplier_pro_material_stock
    </sql>

    <select id="selectSupplierProMaterialStockList" parameterType="SupplierProMaterialStock" resultMap="SupplierProMaterialStockResult">
        <include refid="selectSupplierProMaterialStockVo"/>
        <where>  
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code = #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="supplierSubCode != null  and supplierSubCode != ''"> and supplier_sub_code = #{supplierSubCode}</if>
            <if test="supplierSubName != null  and supplierSubName != ''"> and supplier_sub_name like concat('%', #{supplierSubName}, '%')</if>
            <if test="subSupplierCode != null  and subSupplierCode != ''"> and sub_supplier_code = #{subSupplierCode}</if>
            <if test="subSupplierName != null  and subSupplierName != ''"> and sub_supplier_name like concat('%', #{subSupplierName}, '%')</if>
            <if test="subSupplierAddress != null  and subSupplierAddress != ''"> and sub_supplier_address = #{subSupplierAddress}</if>
            <if test="componentCode != null  and componentCode != ''"> and component_code = #{componentCode}</if>
            <if test="componentName != null  and componentName != ''"> and component_name like concat('%', #{componentName}, '%')</if>
            <if test="subBatchNo != null  and subBatchNo != ''"> and sub_batch_no = #{subBatchNo}</if>
            <if test="subBatchNum != null "> and sub_batch_num = #{subBatchNum}</if>
            <if test="subBatchSn != null  and subBatchSn != ''"> and sub_batch_sn = #{subBatchSn}</if>
            <if test="empCode != null  and empCode != ''"> and emp_code = #{empCode}</if>
            <if test="empName != null  and empName != ''"> and emp_name like concat('%', #{empName}, '%')</if>
            <if test="deviceCode != null  and deviceCode != ''"> and device_code = #{deviceCode}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="featureName != null  and featureName != ''"> and feature_name like concat('%', #{featureName}, '%')</if>
            <if test="featureUnit != null  and featureUnit != ''"> and feature_unit = #{featureUnit}</if>
            <if test="standardValue != null  and standardValue != ''"> and standard_value = #{standardValue}</if>
            <if test="featureUpper != null  and featureUpper != ''"> and feature_upper = #{featureUpper}</if>
            <if test="featureLower != null  and featureLower != ''"> and feature_lower = #{featureLower}</if>
            <if test="featureValue != null  and featureValue != ''"> and feature_value = #{featureValue}</if>
            <if test="checkNo != null  and checkNo != ''"> and check_no = #{checkNo}</if>
            <if test="checkResult != null  and checkResult != ''"> and check_result = #{checkResult}</if>
            <if test="checkTime != null  and checkTime != ''"> and check_time = #{checkTime}</if>
            <if test="samplingRate != null "> and sampling_rate = #{samplingRate}</if>
            <if test="limitUpdateTime != null  and limitUpdateTime != ''"> and limit_update_time = #{limitUpdateTime}</if>
            <if test="vendorFieldDesc != null  and vendorFieldDesc != ''"> and vendor_field_desc = #{vendorFieldDesc}</if>
            <if test="vendorFieldCode != null  and vendorFieldCode != ''"> and vendor_field_code = #{vendorFieldCode}</if>
            <if test="deadLine != null  and deadLine != ''"> and dead_line = #{deadLine}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierProMaterialStockById" parameterType="Long" resultMap="SupplierProMaterialStockResult">
        <include refid="selectSupplierProMaterialStockVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierProMaterialStock" parameterType="SupplierProMaterialStock" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_pro_material_stock
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if test="supplierSubCode != null and supplierSubCode != ''">supplier_sub_code,</if>
            <if test="supplierSubName != null and supplierSubName != ''">supplier_sub_name,</if>
            <if test="subSupplierCode != null and subSupplierCode != ''">sub_supplier_code,</if>
            <if test="subSupplierName != null and subSupplierName != ''">sub_supplier_name,</if>
            <if test="subSupplierAddress != null and subSupplierAddress != ''">sub_supplier_address,</if>
            <if test="componentCode != null and componentCode != ''">component_code,</if>
            <if test="componentName != null and componentName != ''">component_name,</if>
            <if test="subBatchNo != null and subBatchNo != ''">sub_batch_no,</if>
            <if test="subBatchNum != null">sub_batch_num,</if>
            <if test="subBatchSn != null and subBatchSn != ''">sub_batch_sn,</if>
            <if test="empCode != null and empCode != ''">emp_code,</if>
            <if test="empName != null and empName != ''">emp_name,</if>
            <if test="deviceCode != null and deviceCode != ''">device_code,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="featureName != null and featureName != ''">feature_name,</if>
            <if test="featureUnit != null and featureUnit != ''">feature_unit,</if>
            <if test="standardValue != null and standardValue != ''">standard_value,</if>
            <if test="featureUpper != null and featureUpper != ''">feature_upper,</if>
            <if test="featureLower != null and featureLower != ''">feature_lower,</if>
            <if test="featureValue != null and featureValue != ''">feature_value,</if>
            <if test="checkNo != null and checkNo != ''">check_no,</if>
            <if test="checkResult != null and checkResult != ''">check_result,</if>
            <if test="checkTime != null and checkTime != ''">check_time,</if>
            <if test="samplingRate != null">sampling_rate,</if>
            <if test="limitUpdateTime != null and limitUpdateTime != ''">limit_update_time,</if>
            <if test="vendorFieldDesc != null and vendorFieldDesc != ''">vendor_field_desc,</if>
            <if test="vendorFieldCode != null and vendorFieldCode != ''">vendor_field_code,</if>
            <if test="deadLine != null and deadLine != ''">dead_line,</if>
            <if test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if test="supplierSubCode != null and supplierSubCode != ''">#{supplierSubCode},</if>
            <if test="supplierSubName != null and supplierSubName != ''">#{supplierSubName},</if>
            <if test="subSupplierCode != null and subSupplierCode != ''">#{subSupplierCode},</if>
            <if test="subSupplierName != null and subSupplierName != ''">#{subSupplierName},</if>
            <if test="subSupplierAddress != null and subSupplierAddress != ''">#{subSupplierAddress},</if>
            <if test="componentCode != null and componentCode != ''">#{componentCode},</if>
            <if test="componentName != null and componentName != ''">#{componentName},</if>
            <if test="subBatchNo != null and subBatchNo != ''">#{subBatchNo},</if>
            <if test="subBatchNum != null">#{subBatchNum},</if>
            <if test="subBatchSn != null and subBatchSn != ''">#{subBatchSn},</if>
            <if test="empCode != null and empCode != ''">#{empCode},</if>
            <if test="empName != null and empName != ''">#{empName},</if>
            <if test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="featureName != null and featureName != ''">#{featureName},</if>
            <if test="featureUnit != null and featureUnit != ''">#{featureUnit},</if>
            <if test="standardValue != null and standardValue != ''">#{standardValue},</if>
            <if test="featureUpper != null and featureUpper != ''">#{featureUpper},</if>
            <if test="featureLower != null and featureLower != ''">#{featureLower},</if>
            <if test="featureValue != null and featureValue != ''">#{featureValue},</if>
            <if test="checkNo != null and checkNo != ''">#{checkNo},</if>
            <if test="checkResult != null and checkResult != ''">#{checkResult},</if>
            <if test="checkTime != null and checkTime != ''">#{checkTime},</if>
            <if test="samplingRate != null">#{samplingRate},</if>
            <if test="limitUpdateTime != null and limitUpdateTime != ''">#{limitUpdateTime},</if>
            <if test="vendorFieldDesc != null and vendorFieldDesc != ''">#{vendorFieldDesc},</if>
            <if test="vendorFieldCode != null and vendorFieldCode != ''">#{vendorFieldCode},</if>
            <if test="deadLine != null and deadLine != ''">#{deadLine},</if>
            <if test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierProMaterialStock" parameterType="SupplierProMaterialStock">
        update supplier_pro_material_stock
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if test="supplierSubCode != null and supplierSubCode != ''">supplier_sub_code = #{supplierSubCode},</if>
            <if test="supplierSubName != null and supplierSubName != ''">supplier_sub_name = #{supplierSubName},</if>
            <if test="subSupplierCode != null and subSupplierCode != ''">sub_supplier_code = #{subSupplierCode},</if>
            <if test="subSupplierName != null and subSupplierName != ''">sub_supplier_name = #{subSupplierName},</if>
            <if test="subSupplierAddress != null and subSupplierAddress != ''">sub_supplier_address = #{subSupplierAddress},</if>
            <if test="componentCode != null and componentCode != ''">component_code = #{componentCode},</if>
            <if test="componentName != null and componentName != ''">component_name = #{componentName},</if>
            <if test="subBatchNo != null and subBatchNo != ''">sub_batch_no = #{subBatchNo},</if>
            <if test="subBatchNum != null">sub_batch_num = #{subBatchNum},</if>
            <if test="subBatchSn != null and subBatchSn != ''">sub_batch_sn = #{subBatchSn},</if>
            <if test="empCode != null and empCode != ''">emp_code = #{empCode},</if>
            <if test="empName != null and empName != ''">emp_name = #{empName},</if>
            <if test="deviceCode != null and deviceCode != ''">device_code = #{deviceCode},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="featureName != null and featureName != ''">feature_name = #{featureName},</if>
            <if test="featureUnit != null and featureUnit != ''">feature_unit = #{featureUnit},</if>
            <if test="standardValue != null and standardValue != ''">standard_value = #{standardValue},</if>
            <if test="featureUpper != null and featureUpper != ''">feature_upper = #{featureUpper},</if>
            <if test="featureLower != null and featureLower != ''">feature_lower = #{featureLower},</if>
            <if test="featureValue != null and featureValue != ''">feature_value = #{featureValue},</if>
            <if test="checkNo != null and checkNo != ''">check_no = #{checkNo},</if>
            <if test="checkResult != null and checkResult != ''">check_result = #{checkResult},</if>
            <if test="checkTime != null and checkTime != ''">check_time = #{checkTime},</if>
            <if test="samplingRate != null">sampling_rate = #{samplingRate},</if>
            <if test="limitUpdateTime != null and limitUpdateTime != ''">limit_update_time = #{limitUpdateTime},</if>
            <if test="vendorFieldDesc != null and vendorFieldDesc != ''">vendor_field_desc = #{vendorFieldDesc},</if>
            <if test="vendorFieldCode != null and vendorFieldCode != ''">vendor_field_code = #{vendorFieldCode},</if>
            <if test="deadLine != null and deadLine != ''">dead_line = #{deadLine},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierProMaterialStockById" parameterType="Long">
        delete from supplier_pro_material_stock where id = #{id}
    </delete>

    <delete id="deleteSupplierProMaterialStockByIds" parameterType="String">
        delete from supplier_pro_material_stock where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>