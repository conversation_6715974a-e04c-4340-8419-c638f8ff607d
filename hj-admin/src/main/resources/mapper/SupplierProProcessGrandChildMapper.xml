<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierProProcessGrandChildMapper">
    
    <resultMap type="SupplierProProcessGrandChild" id="SupplierProProcessGrandChildResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierProProcessGrandChildVo">
        select id, parent_id, device_type, device_id, device_name, insert_time, modify_time, insert_by, modify_by from supplier_pro_process_grand_child
    </sql>

    <select id="selectSupplierProProcessGrandChildList" parameterType="SupplierProProcessGrandChild" resultMap="SupplierProProcessGrandChildResult">
        <include refid="selectSupplierProProcessGrandChildVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierProProcessGrandChildById" parameterType="Long" resultMap="SupplierProProcessGrandChildResult">
        <include refid="selectSupplierProProcessGrandChildVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierProProcessGrandChild" parameterType="SupplierProProcessGrandChild" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_pro_process_grand_child
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="deviceType != null and deviceType != ''">device_type,</if>
            <if test="deviceId != null and deviceId != ''">device_id,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="deviceType != null and deviceType != ''">#{deviceType},</if>
            <if test="deviceId != null and deviceId != ''">#{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierProProcessGrandChild" parameterType="SupplierProProcessGrandChild">
        update supplier_pro_process_grand_child
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="deviceType != null and deviceType != ''">device_type = #{deviceType},</if>
            <if test="deviceId != null and deviceId != ''">device_id = #{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierProProcessGrandChildById" parameterType="Long">
        delete from supplier_pro_process_grand_child where id = #{id}
    </delete>

    <delete id="deleteSupplierProProcessGrandChildByIds" parameterType="String">
        delete from supplier_pro_process_grand_child where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>