<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierProCschedulMapper">
    
    <resultMap type="SupplierProCschedul" id="SupplierProCschedulResult">
        <result property="id"    column="id"    />
        <result property="models"    column="models"    />
        <result property="vin"    column="vin"    />
        <result property="productionLineId"    column="production_line_id"    />
        <result property="productionLineName"    column="production_line_name"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="sortDate"    column="sort_date"    />
        <result property="sortTime"    column="sort_time"    />
        <result property="onLineDate"    column="on_line_date"    />
        <result property="onLineTime"    column="on_line_time"    />
        <result property="modelCategory"    column="model_category"    />
        <result property="assemblyMaterialCode"    column="assembly_material_code"    />
        <result property="motorMaterialCode"    column="motor_material_code"    />
        <result property="plant"    column="plant"    />
        <result property="createByUser"    column="create_by_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateByUser"    column="update_by_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="version"    column="version"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierProCschedulVo">
        select id, models, vin, production_line_id, production_line_name, material_code, material_description, sort_date, sort_time, on_line_date, on_line_time, model_category, assembly_material_code, motor_material_code, plant, create_by_user, create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by from supplier_pro_cschedul
    </sql>

    <select id="selectSupplierProCschedulList" parameterType="SupplierProCschedul" resultMap="SupplierProCschedulResult">
        <include refid="selectSupplierProCschedulVo"/>
        <where>  
            <if test="models != null  and models != ''"> and models = #{models}</if>
            <if test="vin != null  and vin != ''"> and vin = #{vin}</if>
            <if test="productionLineId != null  and productionLineId != ''"> and production_line_id = #{productionLineId}</if>
            <if test="productionLineName != null  and productionLineName != ''"> and production_line_name like concat('%', #{productionLineName}, '%')</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="sortDate != null  and sortDate != ''"> and sort_date = #{sortDate}</if>
            <if test="sortTime != null  and sortTime != ''"> and sort_time = #{sortTime}</if>
            <if test="onLineDate != null  and onLineDate != ''"> and on_line_date = #{onLineDate}</if>
            <if test="onLineTime != null  and onLineTime != ''"> and on_line_time = #{onLineTime}</if>
            <if test="modelCategory != null  and modelCategory != ''"> and model_category = #{modelCategory}</if>
            <if test="assemblyMaterialCode != null  and assemblyMaterialCode != ''"> and assembly_material_code = #{assemblyMaterialCode}</if>
            <if test="motorMaterialCode != null  and motorMaterialCode != ''"> and motor_material_code = #{motorMaterialCode}</if>
            <if test="plant != null  and plant != ''"> and plant = #{plant}</if>
            <if test="createByUser != null  and createByUser != ''"> and create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''"> and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierProCschedulById" parameterType="Long" resultMap="SupplierProCschedulResult">
        <include refid="selectSupplierProCschedulVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierProCschedul" parameterType="SupplierProCschedul">
        insert into supplier_pro_cschedul
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="models != null">models,</if>
            <if test="vin != null">vin,</if>
            <if test="productionLineId != null">production_line_id,</if>
            <if test="productionLineName != null">production_line_name,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="sortDate != null">sort_date,</if>
            <if test="sortTime != null">sort_time,</if>
            <if test="onLineDate != null">on_line_date,</if>
            <if test="onLineTime != null">on_line_time,</if>
            <if test="modelCategory != null">model_category,</if>
            <if test="assemblyMaterialCode != null">assembly_material_code,</if>
            <if test="motorMaterialCode != null">motor_material_code,</if>
            <if test="plant != null">plant,</if>
            <if test="createByUser != null">create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="models != null">#{models},</if>
            <if test="vin != null">#{vin},</if>
            <if test="productionLineId != null">#{productionLineId},</if>
            <if test="productionLineName != null">#{productionLineName},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="sortDate != null">#{sortDate},</if>
            <if test="sortTime != null">#{sortTime},</if>
            <if test="onLineDate != null">#{onLineDate},</if>
            <if test="onLineTime != null">#{onLineTime},</if>
            <if test="modelCategory != null">#{modelCategory},</if>
            <if test="assemblyMaterialCode != null">#{assemblyMaterialCode},</if>
            <if test="motorMaterialCode != null">#{motorMaterialCode},</if>
            <if test="plant != null">#{plant},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">#{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierProCschedul" parameterType="SupplierProCschedul">
        update supplier_pro_cschedul
        <trim prefix="SET" suffixOverrides=",">
            <if test="models != null">models = #{models},</if>
            <if test="vin != null">vin = #{vin},</if>
            <if test="productionLineId != null">production_line_id = #{productionLineId},</if>
            <if test="productionLineName != null">production_line_name = #{productionLineName},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="sortDate != null">sort_date = #{sortDate},</if>
            <if test="sortTime != null">sort_time = #{sortTime},</if>
            <if test="onLineDate != null">on_line_date = #{onLineDate},</if>
            <if test="onLineTime != null">on_line_time = #{onLineTime},</if>
            <if test="modelCategory != null">model_category = #{modelCategory},</if>
            <if test="assemblyMaterialCode != null">assembly_material_code = #{assemblyMaterialCode},</if>
            <if test="motorMaterialCode != null">motor_material_code = #{motorMaterialCode},</if>
            <if test="plant != null">plant = #{plant},</if>
            <if test="createByUser != null">create_by_user = #{createByUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierProCschedulById" parameterType="Long">
        delete from supplier_pro_cschedul where id = #{id}
    </delete>

    <delete id="deleteSupplierProCschedulByIds" parameterType="String">
        delete from supplier_pro_cschedul where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>