<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierInvDataMapper">
    
    <resultMap type="SupplierInvData" id="SupplierInvDataResult">
        <result property="id"    column="id"    />
        <result property="plantId"    column="plant_id"    />
        <result property="plantName"    column="plant_name"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="quantityCurrent"    column="quantity_current"    />
        <result property="stockState"    column="stock_state"    />
        <result property="dataUpdateTime"    column="data_update_time"    />
        <result property="createByUser"    column="create_by_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateByUser"    column="update_by_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="version"    column="version"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierInvDataVo">
        select id, plant_id, plant_name, material_code, material_description, quantity_current, stock_state, data_update_time, create_by_user, create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by from supplier_inv_data
    </sql>

    <select id="selectSupplierInvDataList" parameterType="SupplierInvData" resultMap="SupplierInvDataResult">
        <include refid="selectSupplierInvDataVo"/>
        <where>  
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="plantName != null  and plantName != ''"> and plant_name like concat('%', #{plantName}, '%')</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="quantityCurrent != null "> and quantity_current = #{quantityCurrent}</if>
            <if test="stockState != null  and stockState != ''"> and stock_state = #{stockState}</if>
            <if test="dataUpdateTime != null  and dataUpdateTime != ''"> and data_update_time = #{dataUpdateTime}</if>
            <if test="createByUser != null  and createByUser != ''"> and create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''"> and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierInvDataById" parameterType="Long" resultMap="SupplierInvDataResult">
        <include refid="selectSupplierInvDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierInvData" parameterType="SupplierInvData">
        insert into supplier_inv_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="plantId != null">plant_id,</if>
            <if test="plantName != null">plant_name,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="quantityCurrent != null">quantity_current,</if>
            <if test="stockState != null">stock_state,</if>
            <if test="dataUpdateTime != null">data_update_time,</if>
            <if test="createByUser != null">create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="plantId != null">#{plantId},</if>
            <if test="plantName != null">#{plantName},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="quantityCurrent != null">#{quantityCurrent},</if>
            <if test="stockState != null">#{stockState},</if>
            <if test="dataUpdateTime != null">#{dataUpdateTime},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">#{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierInvData" parameterType="SupplierInvData">
        update supplier_inv_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="plantId != null">plant_id = #{plantId},</if>
            <if test="plantName != null">plant_name = #{plantName},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="quantityCurrent != null">quantity_current = #{quantityCurrent},</if>
            <if test="stockState != null">stock_state = #{stockState},</if>
            <if test="dataUpdateTime != null">data_update_time = #{dataUpdateTime},</if>
            <if test="createByUser != null">create_by_user = #{createByUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierInvDataById" parameterType="Long">
        delete from supplier_inv_data where id = #{id}
    </delete>

    <delete id="deleteSupplierInvDataByIds" parameterType="String">
        delete from supplier_inv_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>