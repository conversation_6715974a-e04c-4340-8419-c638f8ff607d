<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierProOeeTimeDetailsMapper">
    
    <resultMap type="SupplierProOeeTimeDetails" id="SupplierProOeeTimeDetailsResult">
        <result property="id"    column="id"    />
        <result property="recId"    column="rec_id"    />
        <result property="supplierCode"    column="supplier_code"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="plantId"    column="plant_id"    />
        <result property="plantName"    column="plant_name"    />
        <result property="workshopId"    column="workshop_id"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="productionLineId"    column="production_line_id"    />
        <result property="productionLineName"    column="production_line_name"    />
        <result property="stationId"    column="station_id"    />
        <result property="stationName"    column="station_name"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="type"    column="type"    />
        <result property="subType"    column="sub_type"    />
        <result property="subTypeDescription"    column="sub_type_description"    />
        <result property="planBeginTime"    column="plan_begin_time"    />
        <result property="planEndTime"    column="plan_end_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="hasPush"    column="has_push"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierProOeeTimeDetailsVo">
        select id, rec_id, supplier_code, supplier_name, plant_id, plant_name, workshop_id, workshop_name, production_line_id, production_line_name, station_id, station_name, device_type, device_id, device_name, type, sub_type, sub_type_description, plan_begin_time, plan_end_time, start_time, end_time, has_push, insert_time, modify_time, insert_by, modify_by from supplier_pro_oee_time_details
    </sql>

    <select id="selectSupplierProOeeTimeDetailsList" parameterType="SupplierProOeeTimeDetails" resultMap="SupplierProOeeTimeDetailsResult">
        <include refid="selectSupplierProOeeTimeDetailsVo"/>
        <where>  
            <if test="recId != null  and recId != ''"> and rec_id = #{recId}</if>
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code = #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="plantName != null  and plantName != ''"> and plant_name like concat('%', #{plantName}, '%')</if>
            <if test="workshopId != null  and workshopId != ''"> and workshop_id = #{workshopId}</if>
            <if test="workshopName != null  and workshopName != ''"> and workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="productionLineId != null  and productionLineId != ''"> and production_line_id = #{productionLineId}</if>
            <if test="productionLineName != null  and productionLineName != ''"> and production_line_name like concat('%', #{productionLineName}, '%')</if>
            <if test="stationId != null  and stationId != ''"> and station_id = #{stationId}</if>
            <if test="stationName != null  and stationName != ''"> and station_name like concat('%', #{stationName}, '%')</if>
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="subType != null  and subType != ''"> and sub_type = #{subType}</if>
            <if test="subTypeDescription != null  and subTypeDescription != ''"> and sub_type_description = #{subTypeDescription}</if>
            <if test="planBeginTime != null  and planBeginTime != ''"> and plan_begin_time = #{planBeginTime}</if>
            <if test="planEndTime != null  and planEndTime != ''"> and plan_end_time = #{planEndTime}</if>
            <if test="startTime != null  and startTime != ''"> and start_time = #{startTime}</if>
            <if test="endTime != null  and endTime != ''"> and end_time = #{endTime}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierProOeeTimeDetailsById" parameterType="Long" resultMap="SupplierProOeeTimeDetailsResult">
        <include refid="selectSupplierProOeeTimeDetailsVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierProOeeTimeDetails" parameterType="SupplierProOeeTimeDetails" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_pro_oee_time_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recId != null and recId != ''">rec_id,</if>
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if test="plantId != null and plantId != ''">plant_id,</if>
            <if test="plantName != null and plantName != ''">plant_name,</if>
            <if test="workshopId != null and workshopId != ''">workshop_id,</if>
            <if test="workshopName != null and workshopName != ''">workshop_name,</if>
            <if test="productionLineId != null and productionLineId != ''">production_line_id,</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name,</if>
            <if test="stationId != null and stationId != ''">station_id,</if>
            <if test="stationName != null and stationName != ''">station_name,</if>
            <if test="deviceType != null and deviceType != ''">device_type,</if>
            <if test="deviceId != null and deviceId != ''">device_id,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="subType != null and subType != ''">sub_type,</if>
            <if test="subTypeDescription != null and subTypeDescription != ''">sub_type_description,</if>
            <if test="planBeginTime != null and planBeginTime != ''">plan_begin_time,</if>
            <if test="planEndTime != null and planEndTime != ''">plan_end_time,</if>
            <if test="startTime != null and startTime != ''">start_time,</if>
            <if test="endTime != null and endTime != ''">end_time,</if>
            <if test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recId != null and recId != ''">#{recId},</if>
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if test="plantId != null and plantId != ''">#{plantId},</if>
            <if test="plantName != null and plantName != ''">#{plantName},</if>
            <if test="workshopId != null and workshopId != ''">#{workshopId},</if>
            <if test="workshopName != null and workshopName != ''">#{workshopName},</if>
            <if test="productionLineId != null and productionLineId != ''">#{productionLineId},</if>
            <if test="productionLineName != null and productionLineName != ''">#{productionLineName},</if>
            <if test="stationId != null and stationId != ''">#{stationId},</if>
            <if test="stationName != null and stationName != ''">#{stationName},</if>
            <if test="deviceType != null and deviceType != ''">#{deviceType},</if>
            <if test="deviceId != null and deviceId != ''">#{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="subType != null and subType != ''">#{subType},</if>
            <if test="subTypeDescription != null and subTypeDescription != ''">#{subTypeDescription},</if>
            <if test="planBeginTime != null and planBeginTime != ''">#{planBeginTime},</if>
            <if test="planEndTime != null and planEndTime != ''">#{planEndTime},</if>
            <if test="startTime != null and startTime != ''">#{startTime},</if>
            <if test="endTime != null and endTime != ''">#{endTime},</if>
            <if test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierProOeeTimeDetails" parameterType="SupplierProOeeTimeDetails">
        update supplier_pro_oee_time_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="recId != null and recId != ''">rec_id = #{recId},</if>
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if test="plantId != null and plantId != ''">plant_id = #{plantId},</if>
            <if test="plantName != null and plantName != ''">plant_name = #{plantName},</if>
            <if test="workshopId != null and workshopId != ''">workshop_id = #{workshopId},</if>
            <if test="workshopName != null and workshopName != ''">workshop_name = #{workshopName},</if>
            <if test="productionLineId != null and productionLineId != ''">production_line_id = #{productionLineId},</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name = #{productionLineName},</if>
            <if test="stationId != null and stationId != ''">station_id = #{stationId},</if>
            <if test="stationName != null and stationName != ''">station_name = #{stationName},</if>
            <if test="deviceType != null and deviceType != ''">device_type = #{deviceType},</if>
            <if test="deviceId != null and deviceId != ''">device_id = #{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="subType != null and subType != ''">sub_type = #{subType},</if>
            <if test="subTypeDescription != null and subTypeDescription != ''">sub_type_description = #{subTypeDescription},</if>
            <if test="planBeginTime != null and planBeginTime != ''">plan_begin_time = #{planBeginTime},</if>
            <if test="planEndTime != null and planEndTime != ''">plan_end_time = #{planEndTime},</if>
            <if test="startTime != null and startTime != ''">start_time = #{startTime},</if>
            <if test="endTime != null and endTime != ''">end_time = #{endTime},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierProOeeTimeDetailsById" parameterType="Long">
        delete from supplier_pro_oee_time_details where id = #{id}
    </delete>

    <delete id="deleteSupplierProOeeTimeDetailsByIds" parameterType="String">
        delete from supplier_pro_oee_time_details where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>