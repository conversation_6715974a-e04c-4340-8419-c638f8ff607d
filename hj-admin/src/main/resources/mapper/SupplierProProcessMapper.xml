<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierProProcessMapper">
    
    <resultMap type="SupplierProProcess" id="SupplierProProcessResult">
        <result property="id"    column="id"    />
        <result property="supplierCode"    column="supplier_code"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="cheryProductNo"    column="chery_product_no"    />
        <result property="cheryProductName"    column="chery_product_name"    />
        <result property="vendorProductNo"    column="vendor_product_no"    />
        <result property="vendorProductName"    column="vendor_product_name"    />
        <result property="techCode"    column="tech_code"    />
        <result property="techName"    column="tech_name"    />
        <result property="techVersion"    column="tech_version"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="maxProcessingCapacity"    column="max_processing_capacity"    />
        <result property="promiseRatio"    column="promise_ratio"    />
        <result property="makeSamePeriod"    column="make_same_period"    />
        <result property="hasPush"    column="has_push"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierProProcessVo">
        select id, supplier_code, supplier_name, chery_product_no, chery_product_name, vendor_product_no, vendor_product_name, tech_code, tech_name, tech_version, is_enabled, max_processing_capacity, promise_ratio, make_same_period, has_push, insert_time, modify_time, insert_by, modify_by from supplier_pro_process
    </sql>

    <select id="selectSupplierProProcessList" parameterType="SupplierProProcess" resultMap="SupplierProProcessResult">
        <include refid="selectSupplierProProcessVo"/>
        <where>  
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code = #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="cheryProductNo != null  and cheryProductNo != ''"> and chery_product_no = #{cheryProductNo}</if>
            <if test="cheryProductName != null  and cheryProductName != ''"> and chery_product_name like concat('%', #{cheryProductName}, '%')</if>
            <if test="vendorProductNo != null  and vendorProductNo != ''"> and vendor_product_no = #{vendorProductNo}</if>
            <if test="vendorProductName != null  and vendorProductName != ''"> and vendor_product_name like concat('%', #{vendorProductName}, '%')</if>
            <if test="techCode != null  and techCode != ''"> and tech_code = #{techCode}</if>
            <if test="techName != null  and techName != ''"> and tech_name like concat('%', #{techName}, '%')</if>
            <if test="techVersion != null  and techVersion != ''"> and tech_version = #{techVersion}</if>
            <if test="isEnabled != null  and isEnabled != ''"> and is_enabled = #{isEnabled}</if>
            <if test="maxProcessingCapacity != null  and maxProcessingCapacity != ''"> and max_processing_capacity = #{maxProcessingCapacity}</if>
            <if test="promiseRatio != null "> and promise_ratio = #{promiseRatio}</if>
            <if test="makeSamePeriod != null "> and make_same_period = #{makeSamePeriod}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierProProcessById" parameterType="Long" resultMap="SupplierProProcessResult">
        <include refid="selectSupplierProProcessVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierProProcess" parameterType="SupplierProProcess" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_pro_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no,</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name,</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no,</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name,</if>
            <if test="techCode != null and techCode != ''">tech_code,</if>
            <if test="techName != null and techName != ''">tech_name,</if>
            <if test="techVersion != null and techVersion != ''">tech_version,</if>
            <if test="isEnabled != null and isEnabled != ''">is_enabled,</if>
            <if test="maxProcessingCapacity != null and maxProcessingCapacity != ''">max_processing_capacity,</if>
            <if test="promiseRatio != null">promise_ratio,</if>
            <if test="makeSamePeriod != null">make_same_period,</if>
            <if test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">#{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">#{cheryProductName},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">#{vendorProductNo},</if>
            <if test="vendorProductName != null and vendorProductName != ''">#{vendorProductName},</if>
            <if test="techCode != null and techCode != ''">#{techCode},</if>
            <if test="techName != null and techName != ''">#{techName},</if>
            <if test="techVersion != null and techVersion != ''">#{techVersion},</if>
            <if test="isEnabled != null and isEnabled != ''">#{isEnabled},</if>
            <if test="maxProcessingCapacity != null and maxProcessingCapacity != ''">#{maxProcessingCapacity},</if>
            <if test="promiseRatio != null">#{promiseRatio},</if>
            <if test="makeSamePeriod != null">#{makeSamePeriod},</if>
            <if test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierProProcess" parameterType="SupplierProProcess">
        update supplier_pro_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no = #{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name = #{cheryProductName},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no = #{vendorProductNo},</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name = #{vendorProductName},</if>
            <if test="techCode != null and techCode != ''">tech_code = #{techCode},</if>
            <if test="techName != null and techName != ''">tech_name = #{techName},</if>
            <if test="techVersion != null and techVersion != ''">tech_version = #{techVersion},</if>
            <if test="isEnabled != null and isEnabled != ''">is_enabled = #{isEnabled},</if>
            <if test="maxProcessingCapacity != null and maxProcessingCapacity != ''">max_processing_capacity = #{maxProcessingCapacity},</if>
            <if test="promiseRatio != null">promise_ratio = #{promiseRatio},</if>
            <if test="makeSamePeriod != null">make_same_period = #{makeSamePeriod},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierProProcessById" parameterType="Long">
        delete from supplier_pro_process where id = #{id}
    </delete>

    <delete id="deleteSupplierProProcessByIds" parameterType="String">
        delete from supplier_pro_process where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>