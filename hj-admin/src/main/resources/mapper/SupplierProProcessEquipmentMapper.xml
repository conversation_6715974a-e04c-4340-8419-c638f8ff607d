<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierProProcessEquipmentMapper">
    
    <resultMap type="SupplierProProcessEquipment" id="SupplierProProcessEquipmentResult">
        <result property="id"    column="id"    />
        <result property="supplierCode"    column="supplier_code"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="manufacturer"    column="manufacturer"    />
        <result property="modelNumber"    column="model_number"    />
        <result property="productionDate"    column="production_date"    />
        <result property="material"    column="material"    />
        <result property="currentLocation"    column="current_location"    />
        <result property="deviceStatus"    column="device_status"    />
        <result property="designLifeUnits"    column="design_life_units"    />
        <result property="designLifeValue"    column="design_life_value"    />
        <result property="currentUsageCount"    column="current_usage_count"    />
        <result property="effectiveDays"    column="effective_days"    />
        <result property="maxProcessHours"    column="max_process_hours"    />
        <result property="regularProcessHours"    column="regular_process_hours"    />
        <result property="deviceStartDate"    column="device_start_date"    />
        <result property="deviceEndDate"    column="device_end_date"    />
        <result property="machineCosts"    column="machine_costs"    />
        <result property="machinePurchasePeriod"    column="machine_purchase_period"    />
        <result property="machineType"    column="machine_type"    />
        <result property="unitperHour"    column="unitper_hour"    />
        <result property="cavityCount"    column="cavity_count"    />
        <result property="moldSize"    column="mold_size"    />
        <result property="copyMoldCosts"    column="copy_mold_costs"    />
        <result property="overhaulCount"    column="overhaul_count"    />
        <result property="calibrationDate"    column="calibration_date"    />
        <result property="calibrationDueDays"    column="calibration_due_days"    />
        <result property="unitType"    column="unit_type"    />
        <result property="hasPush"    column="has_push"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierProProcessEquipmentVo">
        select id, supplier_code, supplier_name, device_type, device_id, device_name, manufacturer, model_number, production_date, material, current_location, device_status, design_life_units, design_life_value, current_usage_count, effective_days, max_process_hours, regular_process_hours, device_start_date, device_end_date, machine_costs, machine_purchase_period, machine_type, unitper_hour, cavity_count, mold_size, copy_mold_costs, overhaul_count, calibration_date, calibration_due_days, unit_type, has_push, insert_time, modify_time, insert_by, modify_by from supplier_pro_process_equipment
    </sql>

    <select id="selectSupplierProProcessEquipmentList" parameterType="SupplierProProcessEquipment" resultMap="SupplierProProcessEquipmentResult">
        <include refid="selectSupplierProProcessEquipmentVo"/>
        <where>  
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code = #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="manufacturer != null  and manufacturer != ''"> and manufacturer = #{manufacturer}</if>
            <if test="modelNumber != null  and modelNumber != ''"> and model_number = #{modelNumber}</if>
            <if test="productionDate != null  and productionDate != ''"> and production_date = #{productionDate}</if>
            <if test="material != null  and material != ''"> and material = #{material}</if>
            <if test="currentLocation != null  and currentLocation != ''"> and current_location = #{currentLocation}</if>
            <if test="deviceStatus != null  and deviceStatus != ''"> and device_status = #{deviceStatus}</if>
            <if test="designLifeUnits != null  and designLifeUnits != ''"> and design_life_units = #{designLifeUnits}</if>
            <if test="designLifeValue != null "> and design_life_value = #{designLifeValue}</if>
            <if test="currentUsageCount != null "> and current_usage_count = #{currentUsageCount}</if>
            <if test="effectiveDays != null "> and effective_days = #{effectiveDays}</if>
            <if test="maxProcessHours != null "> and max_process_hours = #{maxProcessHours}</if>
            <if test="regularProcessHours != null "> and regular_process_hours = #{regularProcessHours}</if>
            <if test="deviceStartDate != null  and deviceStartDate != ''"> and device_start_date = #{deviceStartDate}</if>
            <if test="deviceEndDate != null  and deviceEndDate != ''"> and device_end_date = #{deviceEndDate}</if>
            <if test="machineCosts != null "> and machine_costs = #{machineCosts}</if>
            <if test="machinePurchasePeriod != null "> and machine_purchase_period = #{machinePurchasePeriod}</if>
            <if test="machineType != null  and machineType != ''"> and machine_type = #{machineType}</if>
            <if test="unitperHour != null "> and unitper_hour = #{unitperHour}</if>
            <if test="cavityCount != null "> and cavity_count = #{cavityCount}</if>
            <if test="moldSize != null  and moldSize != ''"> and mold_size = #{moldSize}</if>
            <if test="copyMoldCosts != null "> and copy_mold_costs = #{copyMoldCosts}</if>
            <if test="overhaulCount != null "> and overhaul_count = #{overhaulCount}</if>
            <if test="calibrationDate != null  and calibrationDate != ''"> and calibration_date = #{calibrationDate}</if>
            <if test="calibrationDueDays != null  and calibrationDueDays != ''"> and calibration_due_days = #{calibrationDueDays}</if>
            <if test="unitType != null  and unitType != ''"> and unit_type = #{unitType}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierProProcessEquipmentById" parameterType="Long" resultMap="SupplierProProcessEquipmentResult">
        <include refid="selectSupplierProProcessEquipmentVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierProProcessEquipment" parameterType="SupplierProProcessEquipment" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_pro_process_equipment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if test="deviceType != null and deviceType != ''">device_type,</if>
            <if test="deviceId != null and deviceId != ''">device_id,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="manufacturer != null and manufacturer != ''">manufacturer,</if>
            <if test="modelNumber != null and modelNumber != ''">model_number,</if>
            <if test="productionDate != null and productionDate != ''">production_date,</if>
            <if test="material != null and material != ''">material,</if>
            <if test="currentLocation != null and currentLocation != ''">current_location,</if>
            <if test="deviceStatus != null and deviceStatus != ''">device_status,</if>
            <if test="designLifeUnits != null and designLifeUnits != ''">design_life_units,</if>
            <if test="designLifeValue != null">design_life_value,</if>
            <if test="currentUsageCount != null">current_usage_count,</if>
            <if test="effectiveDays != null">effective_days,</if>
            <if test="maxProcessHours != null">max_process_hours,</if>
            <if test="regularProcessHours != null">regular_process_hours,</if>
            <if test="deviceStartDate != null and deviceStartDate != ''">device_start_date,</if>
            <if test="deviceEndDate != null and deviceEndDate != ''">device_end_date,</if>
            <if test="machineCosts != null">machine_costs,</if>
            <if test="machinePurchasePeriod != null">machine_purchase_period,</if>
            <if test="machineType != null and machineType != ''">machine_type,</if>
            <if test="unitperHour != null">unitper_hour,</if>
            <if test="cavityCount != null">cavity_count,</if>
            <if test="moldSize != null and moldSize != ''">mold_size,</if>
            <if test="copyMoldCosts != null">copy_mold_costs,</if>
            <if test="overhaulCount != null">overhaul_count,</if>
            <if test="calibrationDate != null and calibrationDate != ''">calibration_date,</if>
            <if test="calibrationDueDays != null and calibrationDueDays != ''">calibration_due_days,</if>
            <if test="unitType != null and unitType != ''">unit_type,</if>
            <if test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if test="deviceType != null and deviceType != ''">#{deviceType},</if>
            <if test="deviceId != null and deviceId != ''">#{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="manufacturer != null and manufacturer != ''">#{manufacturer},</if>
            <if test="modelNumber != null and modelNumber != ''">#{modelNumber},</if>
            <if test="productionDate != null and productionDate != ''">#{productionDate},</if>
            <if test="material != null and material != ''">#{material},</if>
            <if test="currentLocation != null and currentLocation != ''">#{currentLocation},</if>
            <if test="deviceStatus != null and deviceStatus != ''">#{deviceStatus},</if>
            <if test="designLifeUnits != null and designLifeUnits != ''">#{designLifeUnits},</if>
            <if test="designLifeValue != null">#{designLifeValue},</if>
            <if test="currentUsageCount != null">#{currentUsageCount},</if>
            <if test="effectiveDays != null">#{effectiveDays},</if>
            <if test="maxProcessHours != null">#{maxProcessHours},</if>
            <if test="regularProcessHours != null">#{regularProcessHours},</if>
            <if test="deviceStartDate != null and deviceStartDate != ''">#{deviceStartDate},</if>
            <if test="deviceEndDate != null and deviceEndDate != ''">#{deviceEndDate},</if>
            <if test="machineCosts != null">#{machineCosts},</if>
            <if test="machinePurchasePeriod != null">#{machinePurchasePeriod},</if>
            <if test="machineType != null and machineType != ''">#{machineType},</if>
            <if test="unitperHour != null">#{unitperHour},</if>
            <if test="cavityCount != null">#{cavityCount},</if>
            <if test="moldSize != null and moldSize != ''">#{moldSize},</if>
            <if test="copyMoldCosts != null">#{copyMoldCosts},</if>
            <if test="overhaulCount != null">#{overhaulCount},</if>
            <if test="calibrationDate != null and calibrationDate != ''">#{calibrationDate},</if>
            <if test="calibrationDueDays != null and calibrationDueDays != ''">#{calibrationDueDays},</if>
            <if test="unitType != null and unitType != ''">#{unitType},</if>
            <if test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierProProcessEquipment" parameterType="SupplierProProcessEquipment">
        update supplier_pro_process_equipment
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if test="deviceType != null and deviceType != ''">device_type = #{deviceType},</if>
            <if test="deviceId != null and deviceId != ''">device_id = #{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="manufacturer != null and manufacturer != ''">manufacturer = #{manufacturer},</if>
            <if test="modelNumber != null and modelNumber != ''">model_number = #{modelNumber},</if>
            <if test="productionDate != null and productionDate != ''">production_date = #{productionDate},</if>
            <if test="material != null and material != ''">material = #{material},</if>
            <if test="currentLocation != null and currentLocation != ''">current_location = #{currentLocation},</if>
            <if test="deviceStatus != null and deviceStatus != ''">device_status = #{deviceStatus},</if>
            <if test="designLifeUnits != null and designLifeUnits != ''">design_life_units = #{designLifeUnits},</if>
            <if test="designLifeValue != null">design_life_value = #{designLifeValue},</if>
            <if test="currentUsageCount != null">current_usage_count = #{currentUsageCount},</if>
            <if test="effectiveDays != null">effective_days = #{effectiveDays},</if>
            <if test="maxProcessHours != null">max_process_hours = #{maxProcessHours},</if>
            <if test="regularProcessHours != null">regular_process_hours = #{regularProcessHours},</if>
            <if test="deviceStartDate != null and deviceStartDate != ''">device_start_date = #{deviceStartDate},</if>
            <if test="deviceEndDate != null and deviceEndDate != ''">device_end_date = #{deviceEndDate},</if>
            <if test="machineCosts != null">machine_costs = #{machineCosts},</if>
            <if test="machinePurchasePeriod != null">machine_purchase_period = #{machinePurchasePeriod},</if>
            <if test="machineType != null and machineType != ''">machine_type = #{machineType},</if>
            <if test="unitperHour != null">unitper_hour = #{unitperHour},</if>
            <if test="cavityCount != null">cavity_count = #{cavityCount},</if>
            <if test="moldSize != null and moldSize != ''">mold_size = #{moldSize},</if>
            <if test="copyMoldCosts != null">copy_mold_costs = #{copyMoldCosts},</if>
            <if test="overhaulCount != null">overhaul_count = #{overhaulCount},</if>
            <if test="calibrationDate != null and calibrationDate != ''">calibration_date = #{calibrationDate},</if>
            <if test="calibrationDueDays != null and calibrationDueDays != ''">calibration_due_days = #{calibrationDueDays},</if>
            <if test="unitType != null and unitType != ''">unit_type = #{unitType},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierProProcessEquipmentById" parameterType="Long">
        delete from supplier_pro_process_equipment where id = #{id}
    </delete>

    <delete id="deleteSupplierProProcessEquipmentByIds" parameterType="String">
        delete from supplier_pro_process_equipment where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>