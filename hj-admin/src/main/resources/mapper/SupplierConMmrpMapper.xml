<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierConMmrpMapper">
    
    <resultMap type="SupplierConMmrp" id="SupplierConMmrpResult">
        <result property="id"    column="id"    />
        <result property="supplierCode"    column="supplier_code"    />
        <result property="releaseEdition"    column="release_edition"    />
        <result property="materialCode"    column="material_code"    />
        <result property="plantId"    column="plant_id"    />
        <result property="feedbackResults"    column="feedback_results"    />
        <result property="ventureType"    column="venture_type"    />
        <result property="ventureSpecific"    column="venture_specific"    />
        <result property="measures"    column="measures"    />
        <result property="startMonth"    column="start_month"    />
        <result property="quantityMeet1"    column="quantity_meet1"    />
        <result property="quantityMeet2"    column="quantity_meet2"    />
        <result property="quantityMeet3"    column="quantity_meet3"    />
        <result property="quantityMeet4"    column="quantity_meet4"    />
        <result property="quantityMeet5"    column="quantity_meet5"    />
        <result property="quantityMeet6"    column="quantity_meet6"    />
        <result property="quantityMeet7"    column="quantity_meet7"    />
        <result property="quantityMeet8"    column="quantity_meet8"    />
        <result property="quantityMeet9"    column="quantity_meet9"    />
        <result property="quantityMeet10"    column="quantity_meet10"    />
        <result property="quantityMeet11"    column="quantity_meet11"    />
        <result property="quantityMeet12"    column="quantity_meet12"    />
        <result property="hasPush"    column="has_push"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierConMmrpVo">
        select id, supplier_code, release_edition, material_code, plant_id, feedback_results, venture_type, venture_specific, measures, start_month, quantity_meet1, quantity_meet2, quantity_meet3, quantity_meet4, quantity_meet5, quantity_meet6, quantity_meet7, quantity_meet8, quantity_meet9, quantity_meet10, quantity_meet11, quantity_meet12, has_push, insert_time, modify_time, insert_by, modify_by from supplier_con_mmrp
    </sql>

    <select id="selectSupplierConMmrpList" parameterType="SupplierConMmrp" resultMap="SupplierConMmrpResult">
        <include refid="selectSupplierConMmrpVo"/>
        <where>  
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code = #{supplierCode}</if>
            <if test="releaseEdition != null  and releaseEdition != ''"> and release_edition = #{releaseEdition}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="feedbackResults != null  and feedbackResults != ''"> and feedback_results = #{feedbackResults}</if>
            <if test="ventureType != null  and ventureType != ''"> and venture_type = #{ventureType}</if>
            <if test="ventureSpecific != null  and ventureSpecific != ''"> and venture_specific = #{ventureSpecific}</if>
            <if test="measures != null  and measures != ''"> and measures = #{measures}</if>
            <if test="startMonth != null  and startMonth != ''"> and start_month = #{startMonth}</if>
            <if test="quantityMeet1 != null "> and quantity_meet1 = #{quantityMeet1}</if>
            <if test="quantityMeet2 != null "> and quantity_meet2 = #{quantityMeet2}</if>
            <if test="quantityMeet3 != null "> and quantity_meet3 = #{quantityMeet3}</if>
            <if test="quantityMeet4 != null "> and quantity_meet4 = #{quantityMeet4}</if>
            <if test="quantityMeet5 != null "> and quantity_meet5 = #{quantityMeet5}</if>
            <if test="quantityMeet6 != null "> and quantity_meet6 = #{quantityMeet6}</if>
            <if test="quantityMeet7 != null "> and quantity_meet7 = #{quantityMeet7}</if>
            <if test="quantityMeet8 != null "> and quantity_meet8 = #{quantityMeet8}</if>
            <if test="quantityMeet9 != null "> and quantity_meet9 = #{quantityMeet9}</if>
            <if test="quantityMeet10 != null "> and quantity_meet10 = #{quantityMeet10}</if>
            <if test="quantityMeet11 != null "> and quantity_meet11 = #{quantityMeet11}</if>
            <if test="quantityMeet12 != null "> and quantity_meet12 = #{quantityMeet12}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierConMmrpById" parameterType="Long" resultMap="SupplierConMmrpResult">
        <include refid="selectSupplierConMmrpVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierConMmrp" parameterType="SupplierConMmrp" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_con_mmrp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if test="releaseEdition != null and releaseEdition != ''">release_edition,</if>
            <if test="materialCode != null and materialCode != ''">material_code,</if>
            <if test="plantId != null and plantId != ''">plant_id,</if>
            <if test="feedbackResults != null and feedbackResults != ''">feedback_results,</if>
            <if test="ventureType != null and ventureType != ''">venture_type,</if>
            <if test="ventureSpecific != null and ventureSpecific != ''">venture_specific,</if>
            <if test="measures != null and measures != ''">measures,</if>
            <if test="startMonth != null and startMonth != ''">start_month,</if>
            <if test="quantityMeet1 != null">quantity_meet1,</if>
            <if test="quantityMeet2 != null">quantity_meet2,</if>
            <if test="quantityMeet3 != null">quantity_meet3,</if>
            <if test="quantityMeet4 != null">quantity_meet4,</if>
            <if test="quantityMeet5 != null">quantity_meet5,</if>
            <if test="quantityMeet6 != null">quantity_meet6,</if>
            <if test="quantityMeet7 != null">quantity_meet7,</if>
            <if test="quantityMeet8 != null">quantity_meet8,</if>
            <if test="quantityMeet9 != null">quantity_meet9,</if>
            <if test="quantityMeet10 != null">quantity_meet10,</if>
            <if test="quantityMeet11 != null">quantity_meet11,</if>
            <if test="quantityMeet12 != null">quantity_meet12,</if>
            <if test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if test="releaseEdition != null and releaseEdition != ''">#{releaseEdition},</if>
            <if test="materialCode != null and materialCode != ''">#{materialCode},</if>
            <if test="plantId != null and plantId != ''">#{plantId},</if>
            <if test="feedbackResults != null and feedbackResults != ''">#{feedbackResults},</if>
            <if test="ventureType != null and ventureType != ''">#{ventureType},</if>
            <if test="ventureSpecific != null and ventureSpecific != ''">#{ventureSpecific},</if>
            <if test="measures != null and measures != ''">#{measures},</if>
            <if test="startMonth != null and startMonth != ''">#{startMonth},</if>
            <if test="quantityMeet1 != null">#{quantityMeet1},</if>
            <if test="quantityMeet2 != null">#{quantityMeet2},</if>
            <if test="quantityMeet3 != null">#{quantityMeet3},</if>
            <if test="quantityMeet4 != null">#{quantityMeet4},</if>
            <if test="quantityMeet5 != null">#{quantityMeet5},</if>
            <if test="quantityMeet6 != null">#{quantityMeet6},</if>
            <if test="quantityMeet7 != null">#{quantityMeet7},</if>
            <if test="quantityMeet8 != null">#{quantityMeet8},</if>
            <if test="quantityMeet9 != null">#{quantityMeet9},</if>
            <if test="quantityMeet10 != null">#{quantityMeet10},</if>
            <if test="quantityMeet11 != null">#{quantityMeet11},</if>
            <if test="quantityMeet12 != null">#{quantityMeet12},</if>
            <if test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierConMmrp" parameterType="SupplierConMmrp">
        update supplier_con_mmrp
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="releaseEdition != null and releaseEdition != ''">release_edition = #{releaseEdition},</if>
            <if test="materialCode != null and materialCode != ''">material_code = #{materialCode},</if>
            <if test="plantId != null and plantId != ''">plant_id = #{plantId},</if>
            <if test="feedbackResults != null and feedbackResults != ''">feedback_results = #{feedbackResults},</if>
            <if test="ventureType != null and ventureType != ''">venture_type = #{ventureType},</if>
            <if test="ventureSpecific != null and ventureSpecific != ''">venture_specific = #{ventureSpecific},</if>
            <if test="measures != null and measures != ''">measures = #{measures},</if>
            <if test="startMonth != null and startMonth != ''">start_month = #{startMonth},</if>
            <if test="quantityMeet1 != null">quantity_meet1 = #{quantityMeet1},</if>
            <if test="quantityMeet2 != null">quantity_meet2 = #{quantityMeet2},</if>
            <if test="quantityMeet3 != null">quantity_meet3 = #{quantityMeet3},</if>
            <if test="quantityMeet4 != null">quantity_meet4 = #{quantityMeet4},</if>
            <if test="quantityMeet5 != null">quantity_meet5 = #{quantityMeet5},</if>
            <if test="quantityMeet6 != null">quantity_meet6 = #{quantityMeet6},</if>
            <if test="quantityMeet7 != null">quantity_meet7 = #{quantityMeet7},</if>
            <if test="quantityMeet8 != null">quantity_meet8 = #{quantityMeet8},</if>
            <if test="quantityMeet9 != null">quantity_meet9 = #{quantityMeet9},</if>
            <if test="quantityMeet10 != null">quantity_meet10 = #{quantityMeet10},</if>
            <if test="quantityMeet11 != null">quantity_meet11 = #{quantityMeet11},</if>
            <if test="quantityMeet12 != null">quantity_meet12 = #{quantityMeet12},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierConMmrpById" parameterType="Long">
        delete from supplier_con_mmrp where id = #{id}
    </delete>

    <delete id="deleteSupplierConMmrpByIds" parameterType="String">
        delete from supplier_con_mmrp where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>