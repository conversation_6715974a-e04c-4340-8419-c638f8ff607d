<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierProTschedulMapper">
    
    <resultMap type="SupplierProTschedul" id="SupplierProTschedulResult">
        <result property="id"    column="id"    />
        <result property="models"    column="models"    />
        <result property="vin"    column="vin"    />
        <result property="productionLineId"    column="production_line_id"    />
        <result property="productionLineName"    column="production_line_name"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="onLineTime"    column="on_line_time"    />
        <result property="finalWorkshop"    column="final_workshop"    />
        <result property="finalOnlineTime"    column="final_onLine_time"    />
        <result property="createByUser"    column="create_by_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateByUser"    column="update_by_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="version"    column="version"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierProTschedulVo">
        select id, models, vin, production_line_id, production_line_name, material_code, material_description, on_line_time, final_workshop, final_onLine_time, create_by_user, create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by from supplier_pro_tschedul
    </sql>

    <select id="selectSupplierProTschedulList" parameterType="SupplierProTschedul" resultMap="SupplierProTschedulResult">
        <include refid="selectSupplierProTschedulVo"/>
        <where>  
            <if test="models != null  and models != ''"> and models = #{models}</if>
            <if test="vin != null  and vin != ''"> and vin = #{vin}</if>
            <if test="productionLineId != null  and productionLineId != ''"> and production_line_id = #{productionLineId}</if>
            <if test="productionLineName != null  and productionLineName != ''"> and production_line_name like concat('%', #{productionLineName}, '%')</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="onLineTime != null  and onLineTime != ''"> and on_line_time = #{onLineTime}</if>
            <if test="finalWorkshop != null  and finalWorkshop != ''"> and final_workshop = #{finalWorkshop}</if>
            <if test="finalOnlineTime != null  and finalOnlineTime != ''"> and final_onLine_time = #{finalOnlineTime}</if>
            <if test="createByUser != null  and createByUser != ''"> and create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''"> and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierProTschedulById" parameterType="Long" resultMap="SupplierProTschedulResult">
        <include refid="selectSupplierProTschedulVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierProTschedul" parameterType="SupplierProTschedul">
        insert into supplier_pro_tschedul
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="models != null">models,</if>
            <if test="vin != null">vin,</if>
            <if test="productionLineId != null">production_line_id,</if>
            <if test="productionLineName != null">production_line_name,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="onLineTime != null">on_line_time,</if>
            <if test="finalWorkshop != null">final_workshop,</if>
            <if test="finalOnlineTime != null">final_onLine_time,</if>
            <if test="createByUser != null">create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="models != null">#{models},</if>
            <if test="vin != null">#{vin},</if>
            <if test="productionLineId != null">#{productionLineId},</if>
            <if test="productionLineName != null">#{productionLineName},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="onLineTime != null">#{onLineTime},</if>
            <if test="finalWorkshop != null">#{finalWorkshop},</if>
            <if test="finalOnlineTime != null">#{finalOnlineTime},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">#{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierProTschedul" parameterType="SupplierProTschedul">
        update supplier_pro_tschedul
        <trim prefix="SET" suffixOverrides=",">
            <if test="models != null">models = #{models},</if>
            <if test="vin != null">vin = #{vin},</if>
            <if test="productionLineId != null">production_line_id = #{productionLineId},</if>
            <if test="productionLineName != null">production_line_name = #{productionLineName},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="onLineTime != null">on_line_time = #{onLineTime},</if>
            <if test="finalWorkshop != null">final_workshop = #{finalWorkshop},</if>
            <if test="finalOnlineTime != null">final_onLine_time = #{finalOnlineTime},</if>
            <if test="createByUser != null">create_by_user = #{createByUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierProTschedulById" parameterType="Long">
        delete from supplier_pro_tschedul where id = #{id}
    </delete>

    <delete id="deleteSupplierProTschedulByIds" parameterType="String">
        delete from supplier_pro_tschedul where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>