<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierProDataMapper">
    
    <resultMap type="SupplierProData" id="SupplierProDataResult">
        <result property="id"    column="id"    />
        <result property="supplierCode"    column="supplier_code"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="plantId"    column="plant_id"    />
        <result property="plantName"    column="plant_name"    />
        <result property="workshopId"    column="workshop_id"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="productionLineId"    column="production_line_id"    />
        <result property="productionLineName"    column="production_line_name"    />
        <result property="stationId"    column="station_id"    />
        <result property="stationName"    column="station_name"    />
        <result property="empCode"    column="emp_code"    />
        <result property="empName"    column="emp_name"    />
        <result property="vendorProductName"    column="vendor_product_name"    />
        <result property="vendorProductNo"    column="vendor_product_no"    />
        <result property="vendorProductBatch"    column="vendor_product_batch"    />
        <result property="vendorProductSn"    column="vendor_product_sn"    />
        <result property="subProdNo"    column="sub_prod_no"    />
        <result property="subProdName"    column="sub_prod_name"    />
        <result property="subBatchNo"    column="sub_batch_no"    />
        <result property="childPackageInfo"    column="child_package_info"    />
        <result property="subProdNum"    column="sub_prod_num"    />
        <result property="subProdSn"    column="sub_prod_sn"    />
        <result property="childSource"    column="child_source"    />
        <result property="subSupplierCode"    column="sub_supplier_code"    />
        <result property="subSupplierName"    column="sub_supplier_name"    />
        <result property="cheryProductNo"    column="chery_product_no"    />
        <result property="cheryProductName"    column="chery_product_name"    />
        <result property="cheryProductSn"    column="chery_product_sn"    />
        <result property="manufactureNo"    column="manufacture_no"    />
        <result property="productBatchNo"    column="product_batch_no"    />
        <result property="workShift"    column="work_shift"    />
        <result property="materialInputTime"    column="material_input_time"    />
        <result property="materialOutputTime"    column="material_output_time"    />
        <result property="vendorFieldNum"    column="vendor_field_num"    />
        <result property="vendorFieldName"    column="vendor_field_name"    />
        <result property="instrumentQualityStatus"    column="instrument_quality_status"    />
        <result property="manualQualityStatus"    column="manual_quality_status"    />
        <result property="finalQualityStatus"    column="final_quality_status"    />
        <result property="collectTime"    column="collect_time"    />
        <result property="dateTime"    column="date_time"    />
        <result property="parentHardwareRevision"    column="parent_hardware_revision"    />
        <result property="parentSoftwareRevision"    column="parent_software_revision"    />
        <result property="childHardwareRevision"    column="child_hardware_revision"    />
        <result property="childSoftwareRevision"    column="child_software_revision"    />
        <result property="hasPush"    column="has_push"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierProDataVo">
        select id, supplier_code, supplier_name, plant_id, plant_name, workshop_id, workshop_name, production_line_id, production_line_name, station_id, station_name, emp_code, emp_name, vendor_product_name, vendor_product_no, vendor_product_batch, vendor_product_sn, sub_prod_no, sub_prod_name, sub_batch_no, child_package_info, sub_prod_num, sub_prod_sn, child_source, sub_supplier_code, sub_supplier_name, chery_product_no, chery_product_name, chery_product_sn, manufacture_no, product_batch_no, work_shift, material_input_time, material_output_time, vendor_field_num, vendor_field_name, instrument_quality_status, manual_quality_status, final_quality_status, collect_time, date_time, parent_hardware_revision, parent_software_revision, child_hardware_revision, child_software_revision, has_push, insert_time, modify_time, insert_by, modify_by from supplier_pro_data
    </sql>

    <select id="selectSupplierProDataList" parameterType="SupplierProData" resultMap="SupplierProDataResult">
        <include refid="selectSupplierProDataVo"/>
        <where>  
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code = #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="plantName != null  and plantName != ''"> and plant_name like concat('%', #{plantName}, '%')</if>
            <if test="workshopId != null  and workshopId != ''"> and workshop_id = #{workshopId}</if>
            <if test="workshopName != null  and workshopName != ''"> and workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="productionLineId != null  and productionLineId != ''"> and production_line_id = #{productionLineId}</if>
            <if test="productionLineName != null  and productionLineName != ''"> and production_line_name like concat('%', #{productionLineName}, '%')</if>
            <if test="stationId != null  and stationId != ''"> and station_id = #{stationId}</if>
            <if test="stationName != null  and stationName != ''"> and station_name like concat('%', #{stationName}, '%')</if>
            <if test="empCode != null  and empCode != ''"> and emp_code = #{empCode}</if>
            <if test="empName != null  and empName != ''"> and emp_name like concat('%', #{empName}, '%')</if>
            <if test="vendorProductName != null  and vendorProductName != ''"> and vendor_product_name like concat('%', #{vendorProductName}, '%')</if>
            <if test="vendorProductNo != null  and vendorProductNo != ''"> and vendor_product_no = #{vendorProductNo}</if>
            <if test="vendorProductBatch != null  and vendorProductBatch != ''"> and vendor_product_batch = #{vendorProductBatch}</if>
            <if test="vendorProductSn != null  and vendorProductSn != ''"> and vendor_product_sn = #{vendorProductSn}</if>
            <if test="subProdNo != null  and subProdNo != ''"> and sub_prod_no = #{subProdNo}</if>
            <if test="subProdName != null  and subProdName != ''"> and sub_prod_name like concat('%', #{subProdName}, '%')</if>
            <if test="subBatchNo != null  and subBatchNo != ''"> and sub_batch_no = #{subBatchNo}</if>
            <if test="childPackageInfo != null  and childPackageInfo != ''"> and child_package_info = #{childPackageInfo}</if>
            <if test="subProdNum != null "> and sub_prod_num = #{subProdNum}</if>
            <if test="subProdSn != null  and subProdSn != ''"> and sub_prod_sn = #{subProdSn}</if>
            <if test="childSource != null  and childSource != ''"> and child_source = #{childSource}</if>
            <if test="subSupplierCode != null  and subSupplierCode != ''"> and sub_supplier_code = #{subSupplierCode}</if>
            <if test="subSupplierName != null  and subSupplierName != ''"> and sub_supplier_name like concat('%', #{subSupplierName}, '%')</if>
            <if test="cheryProductNo != null  and cheryProductNo != ''"> and chery_product_no = #{cheryProductNo}</if>
            <if test="cheryProductName != null  and cheryProductName != ''"> and chery_product_name like concat('%', #{cheryProductName}, '%')</if>
            <if test="cheryProductSn != null  and cheryProductSn != ''"> and chery_product_sn = #{cheryProductSn}</if>
            <if test="manufactureNo != null  and manufactureNo != ''"> and manufacture_no = #{manufactureNo}</if>
            <if test="productBatchNo != null  and productBatchNo != ''"> and product_batch_no = #{productBatchNo}</if>
            <if test="workShift != null  and workShift != ''"> and work_shift = #{workShift}</if>
            <if test="materialInputTime != null  and materialInputTime != ''"> and material_input_time = #{materialInputTime}</if>
            <if test="materialOutputTime != null  and materialOutputTime != ''"> and material_output_time = #{materialOutputTime}</if>
            <if test="vendorFieldNum != null  and vendorFieldNum != ''"> and vendor_field_num = #{vendorFieldNum}</if>
            <if test="vendorFieldName != null  and vendorFieldName != ''"> and vendor_field_name like concat('%', #{vendorFieldName}, '%')</if>
            <if test="instrumentQualityStatus != null  and instrumentQualityStatus != ''"> and instrument_quality_status = #{instrumentQualityStatus}</if>
            <if test="manualQualityStatus != null  and manualQualityStatus != ''"> and manual_quality_status = #{manualQualityStatus}</if>
            <if test="finalQualityStatus != null  and finalQualityStatus != ''"> and final_quality_status = #{finalQualityStatus}</if>
            <if test="collectTime != null  and collectTime != ''"> and collect_time = #{collectTime}</if>
            <if test="dateTime != null  and dateTime != ''"> and date_time = #{dateTime}</if>
            <if test="parentHardwareRevision != null  and parentHardwareRevision != ''"> and parent_hardware_revision = #{parentHardwareRevision}</if>
            <if test="parentSoftwareRevision != null  and parentSoftwareRevision != ''"> and parent_software_revision = #{parentSoftwareRevision}</if>
            <if test="childHardwareRevision != null  and childHardwareRevision != ''"> and child_hardware_revision = #{childHardwareRevision}</if>
            <if test="childSoftwareRevision != null  and childSoftwareRevision != ''"> and child_software_revision = #{childSoftwareRevision}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierProDataById" parameterType="Long" resultMap="SupplierProDataResult">
        <include refid="selectSupplierProDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierProData" parameterType="SupplierProData" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_pro_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if test="plantId != null and plantId != ''">plant_id,</if>
            <if test="plantName != null and plantName != ''">plant_name,</if>
            <if test="workshopId != null and workshopId != ''">workshop_id,</if>
            <if test="workshopName != null and workshopName != ''">workshop_name,</if>
            <if test="productionLineId != null and productionLineId != ''">production_line_id,</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name,</if>
            <if test="stationId != null and stationId != ''">station_id,</if>
            <if test="stationName != null and stationName != ''">station_name,</if>
            <if test="empCode != null and empCode != ''">emp_code,</if>
            <if test="empName != null and empName != ''">emp_name,</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name,</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no,</if>
            <if test="vendorProductBatch != null and vendorProductBatch != ''">vendor_product_batch,</if>
            <if test="vendorProductSn != null and vendorProductSn != ''">vendor_product_sn,</if>
            <if test="subProdNo != null and subProdNo != ''">sub_prod_no,</if>
            <if test="subProdName != null and subProdName != ''">sub_prod_name,</if>
            <if test="subBatchNo != null and subBatchNo != ''">sub_batch_no,</if>
            <if test="childPackageInfo != null and childPackageInfo != ''">child_package_info,</if>
            <if test="subProdNum != null">sub_prod_num,</if>
            <if test="subProdSn != null and subProdSn != ''">sub_prod_sn,</if>
            <if test="childSource != null and childSource != ''">child_source,</if>
            <if test="subSupplierCode != null and subSupplierCode != ''">sub_supplier_code,</if>
            <if test="subSupplierName != null and subSupplierName != ''">sub_supplier_name,</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no,</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name,</if>
            <if test="cheryProductSn != null and cheryProductSn != ''">chery_product_sn,</if>
            <if test="manufactureNo != null and manufactureNo != ''">manufacture_no,</if>
            <if test="productBatchNo != null and productBatchNo != ''">product_batch_no,</if>
            <if test="workShift != null and workShift != ''">work_shift,</if>
            <if test="materialInputTime != null and materialInputTime != ''">material_input_time,</if>
            <if test="materialOutputTime != null and materialOutputTime != ''">material_output_time,</if>
            <if test="vendorFieldNum != null and vendorFieldNum != ''">vendor_field_num,</if>
            <if test="vendorFieldName != null and vendorFieldName != ''">vendor_field_name,</if>
            <if test="instrumentQualityStatus != null and instrumentQualityStatus != ''">instrument_quality_status,</if>
            <if test="manualQualityStatus != null and manualQualityStatus != ''">manual_quality_status,</if>
            <if test="finalQualityStatus != null and finalQualityStatus != ''">final_quality_status,</if>
            <if test="collectTime != null and collectTime != ''">collect_time,</if>
            <if test="dateTime != null and dateTime != ''">date_time,</if>
            <if test="parentHardwareRevision != null and parentHardwareRevision != ''">parent_hardware_revision,</if>
            <if test="parentSoftwareRevision != null and parentSoftwareRevision != ''">parent_software_revision,</if>
            <if test="childHardwareRevision != null and childHardwareRevision != ''">child_hardware_revision,</if>
            <if test="childSoftwareRevision != null and childSoftwareRevision != ''">child_software_revision,</if>
            <if test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if test="plantId != null and plantId != ''">#{plantId},</if>
            <if test="plantName != null and plantName != ''">#{plantName},</if>
            <if test="workshopId != null and workshopId != ''">#{workshopId},</if>
            <if test="workshopName != null and workshopName != ''">#{workshopName},</if>
            <if test="productionLineId != null and productionLineId != ''">#{productionLineId},</if>
            <if test="productionLineName != null and productionLineName != ''">#{productionLineName},</if>
            <if test="stationId != null and stationId != ''">#{stationId},</if>
            <if test="stationName != null and stationName != ''">#{stationName},</if>
            <if test="empCode != null and empCode != ''">#{empCode},</if>
            <if test="empName != null and empName != ''">#{empName},</if>
            <if test="vendorProductName != null and vendorProductName != ''">#{vendorProductName},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">#{vendorProductNo},</if>
            <if test="vendorProductBatch != null and vendorProductBatch != ''">#{vendorProductBatch},</if>
            <if test="vendorProductSn != null and vendorProductSn != ''">#{vendorProductSn},</if>
            <if test="subProdNo != null and subProdNo != ''">#{subProdNo},</if>
            <if test="subProdName != null and subProdName != ''">#{subProdName},</if>
            <if test="subBatchNo != null and subBatchNo != ''">#{subBatchNo},</if>
            <if test="childPackageInfo != null and childPackageInfo != ''">#{childPackageInfo},</if>
            <if test="subProdNum != null">#{subProdNum},</if>
            <if test="subProdSn != null and subProdSn != ''">#{subProdSn},</if>
            <if test="childSource != null and childSource != ''">#{childSource},</if>
            <if test="subSupplierCode != null and subSupplierCode != ''">#{subSupplierCode},</if>
            <if test="subSupplierName != null and subSupplierName != ''">#{subSupplierName},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">#{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">#{cheryProductName},</if>
            <if test="cheryProductSn != null and cheryProductSn != ''">#{cheryProductSn},</if>
            <if test="manufactureNo != null and manufactureNo != ''">#{manufactureNo},</if>
            <if test="productBatchNo != null and productBatchNo != ''">#{productBatchNo},</if>
            <if test="workShift != null and workShift != ''">#{workShift},</if>
            <if test="materialInputTime != null and materialInputTime != ''">#{materialInputTime},</if>
            <if test="materialOutputTime != null and materialOutputTime != ''">#{materialOutputTime},</if>
            <if test="vendorFieldNum != null and vendorFieldNum != ''">#{vendorFieldNum},</if>
            <if test="vendorFieldName != null and vendorFieldName != ''">#{vendorFieldName},</if>
            <if test="instrumentQualityStatus != null and instrumentQualityStatus != ''">#{instrumentQualityStatus},</if>
            <if test="manualQualityStatus != null and manualQualityStatus != ''">#{manualQualityStatus},</if>
            <if test="finalQualityStatus != null and finalQualityStatus != ''">#{finalQualityStatus},</if>
            <if test="collectTime != null and collectTime != ''">#{collectTime},</if>
            <if test="dateTime != null and dateTime != ''">#{dateTime},</if>
            <if test="parentHardwareRevision != null and parentHardwareRevision != ''">#{parentHardwareRevision},</if>
            <if test="parentSoftwareRevision != null and parentSoftwareRevision != ''">#{parentSoftwareRevision},</if>
            <if test="childHardwareRevision != null and childHardwareRevision != ''">#{childHardwareRevision},</if>
            <if test="childSoftwareRevision != null and childSoftwareRevision != ''">#{childSoftwareRevision},</if>
            <if test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierProData" parameterType="SupplierProData">
        update supplier_pro_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if test="plantId != null and plantId != ''">plant_id = #{plantId},</if>
            <if test="plantName != null and plantName != ''">plant_name = #{plantName},</if>
            <if test="workshopId != null and workshopId != ''">workshop_id = #{workshopId},</if>
            <if test="workshopName != null and workshopName != ''">workshop_name = #{workshopName},</if>
            <if test="productionLineId != null and productionLineId != ''">production_line_id = #{productionLineId},</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name = #{productionLineName},</if>
            <if test="stationId != null and stationId != ''">station_id = #{stationId},</if>
            <if test="stationName != null and stationName != ''">station_name = #{stationName},</if>
            <if test="empCode != null and empCode != ''">emp_code = #{empCode},</if>
            <if test="empName != null and empName != ''">emp_name = #{empName},</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name = #{vendorProductName},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no = #{vendorProductNo},</if>
            <if test="vendorProductBatch != null and vendorProductBatch != ''">vendor_product_batch = #{vendorProductBatch},</if>
            <if test="vendorProductSn != null and vendorProductSn != ''">vendor_product_sn = #{vendorProductSn},</if>
            <if test="subProdNo != null and subProdNo != ''">sub_prod_no = #{subProdNo},</if>
            <if test="subProdName != null and subProdName != ''">sub_prod_name = #{subProdName},</if>
            <if test="subBatchNo != null and subBatchNo != ''">sub_batch_no = #{subBatchNo},</if>
            <if test="childPackageInfo != null and childPackageInfo != ''">child_package_info = #{childPackageInfo},</if>
            <if test="subProdNum != null">sub_prod_num = #{subProdNum},</if>
            <if test="subProdSn != null and subProdSn != ''">sub_prod_sn = #{subProdSn},</if>
            <if test="childSource != null and childSource != ''">child_source = #{childSource},</if>
            <if test="subSupplierCode != null and subSupplierCode != ''">sub_supplier_code = #{subSupplierCode},</if>
            <if test="subSupplierName != null and subSupplierName != ''">sub_supplier_name = #{subSupplierName},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no = #{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name = #{cheryProductName},</if>
            <if test="cheryProductSn != null and cheryProductSn != ''">chery_product_sn = #{cheryProductSn},</if>
            <if test="manufactureNo != null and manufactureNo != ''">manufacture_no = #{manufactureNo},</if>
            <if test="productBatchNo != null and productBatchNo != ''">product_batch_no = #{productBatchNo},</if>
            <if test="workShift != null and workShift != ''">work_shift = #{workShift},</if>
            <if test="materialInputTime != null and materialInputTime != ''">material_input_time = #{materialInputTime},</if>
            <if test="materialOutputTime != null and materialOutputTime != ''">material_output_time = #{materialOutputTime},</if>
            <if test="vendorFieldNum != null and vendorFieldNum != ''">vendor_field_num = #{vendorFieldNum},</if>
            <if test="vendorFieldName != null and vendorFieldName != ''">vendor_field_name = #{vendorFieldName},</if>
            <if test="instrumentQualityStatus != null and instrumentQualityStatus != ''">instrument_quality_status = #{instrumentQualityStatus},</if>
            <if test="manualQualityStatus != null and manualQualityStatus != ''">manual_quality_status = #{manualQualityStatus},</if>
            <if test="finalQualityStatus != null and finalQualityStatus != ''">final_quality_status = #{finalQualityStatus},</if>
            <if test="collectTime != null and collectTime != ''">collect_time = #{collectTime},</if>
            <if test="dateTime != null and dateTime != ''">date_time = #{dateTime},</if>
            <if test="parentHardwareRevision != null and parentHardwareRevision != ''">parent_hardware_revision = #{parentHardwareRevision},</if>
            <if test="parentSoftwareRevision != null and parentSoftwareRevision != ''">parent_software_revision = #{parentSoftwareRevision},</if>
            <if test="childHardwareRevision != null and childHardwareRevision != ''">child_hardware_revision = #{childHardwareRevision},</if>
            <if test="childSoftwareRevision != null and childSoftwareRevision != ''">child_software_revision = #{childSoftwareRevision},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierProDataById" parameterType="Long">
        delete from supplier_pro_data where id = #{id}
    </delete>

    <delete id="deleteSupplierProDataByIds" parameterType="String">
        delete from supplier_pro_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>