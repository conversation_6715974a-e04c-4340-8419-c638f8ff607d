<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierSinvDataMapper">
    
    <resultMap type="SupplierSinvData" id="SupplierSinvDataResult">
        <result property="id"    column="id"    />
        <result property="supplierCode"    column="supplier_code"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="materialType"    column="material_type"    />
        <result property="quantityCurrent"    column="quantity_current"    />
        <result property="quantityPlan"    column="quantity_plan"    />
        <result property="inventoryStatus"    column="inventory_status"    />
        <result property="safetyStock"    column="safety_stock"    />
        <result property="productionCycle"    column="production_cycle"    />
        <result property="dataUpdateTime"    column="data_update_time"    />
        <result property="supplierBatch"    column="supplier_batch"    />
        <result property="supplieryxqDate"    column="supplieryxq_date"    />
        <result property="hasPush"    column="has_push"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierSinvDataVo">
        select id, supplier_code, supplier_name, material_code, material_description, material_type, quantity_current, quantity_plan, inventory_status, safety_stock, production_cycle, data_update_time, supplier_batch, supplieryxq_date, has_push, insert_time, modify_time, insert_by, modify_by from supplier_sinv_data
    </sql>

    <select id="selectSupplierSinvDataList" parameterType="SupplierSinvData" resultMap="SupplierSinvDataResult">
        <include refid="selectSupplierSinvDataVo"/>
        <where>  
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code = #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="materialType != null  and materialType != ''"> and material_type = #{materialType}</if>
            <if test="quantityCurrent != null "> and quantity_current = #{quantityCurrent}</if>
            <if test="quantityPlan != null "> and quantity_plan = #{quantityPlan}</if>
            <if test="inventoryStatus != null  and inventoryStatus != ''"> and inventory_status = #{inventoryStatus}</if>
            <if test="safetyStock != null "> and safety_stock = #{safetyStock}</if>
            <if test="productionCycle != null  and productionCycle != ''"> and production_cycle = #{productionCycle}</if>
            <if test="dataUpdateTime != null  and dataUpdateTime != ''"> and data_update_time = #{dataUpdateTime}</if>
            <if test="supplierBatch != null  and supplierBatch != ''"> and supplier_batch = #{supplierBatch}</if>
            <if test="supplieryxqDate != null  and supplieryxqDate != ''"> and supplieryxq_date = #{supplieryxqDate}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierSinvDataById" parameterType="Long" resultMap="SupplierSinvDataResult">
        <include refid="selectSupplierSinvDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierSinvData" parameterType="SupplierSinvData" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_sinv_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if test="materialCode != null and materialCode != ''">material_code,</if>
            <if test="materialDescription != null and materialDescription != ''">material_description,</if>
            <if test="materialType != null and materialType != ''">material_type,</if>
            <if test="quantityCurrent != null">quantity_current,</if>
            <if test="quantityPlan != null">quantity_plan,</if>
            <if test="inventoryStatus != null and inventoryStatus != ''">inventory_status,</if>
            <if test="safetyStock != null">safety_stock,</if>
            <if test="productionCycle != null and productionCycle != ''">production_cycle,</if>
            <if test="dataUpdateTime != null and dataUpdateTime != ''">data_update_time,</if>
            <if test="supplierBatch != null and supplierBatch != ''">supplier_batch,</if>
            <if test="supplieryxqDate != null and supplieryxqDate != ''">supplieryxq_date,</if>
            <if test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if test="materialCode != null and materialCode != ''">#{materialCode},</if>
            <if test="materialDescription != null and materialDescription != ''">#{materialDescription},</if>
            <if test="materialType != null and materialType != ''">#{materialType},</if>
            <if test="quantityCurrent != null">#{quantityCurrent},</if>
            <if test="quantityPlan != null">#{quantityPlan},</if>
            <if test="inventoryStatus != null and inventoryStatus != ''">#{inventoryStatus},</if>
            <if test="safetyStock != null">#{safetyStock},</if>
            <if test="productionCycle != null and productionCycle != ''">#{productionCycle},</if>
            <if test="dataUpdateTime != null and dataUpdateTime != ''">#{dataUpdateTime},</if>
            <if test="supplierBatch != null and supplierBatch != ''">#{supplierBatch},</if>
            <if test="supplieryxqDate != null and supplieryxqDate != ''">#{supplieryxqDate},</if>
            <if test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierSinvData" parameterType="SupplierSinvData">
        update supplier_sinv_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if test="materialCode != null and materialCode != ''">material_code = #{materialCode},</if>
            <if test="materialDescription != null and materialDescription != ''">material_description = #{materialDescription},</if>
            <if test="materialType != null and materialType != ''">material_type = #{materialType},</if>
            <if test="quantityCurrent != null">quantity_current = #{quantityCurrent},</if>
            <if test="quantityPlan != null">quantity_plan = #{quantityPlan},</if>
            <if test="inventoryStatus != null and inventoryStatus != ''">inventory_status = #{inventoryStatus},</if>
            <if test="safetyStock != null">safety_stock = #{safetyStock},</if>
            <if test="productionCycle != null and productionCycle != ''">production_cycle = #{productionCycle},</if>
            <if test="dataUpdateTime != null and dataUpdateTime != ''">data_update_time = #{dataUpdateTime},</if>
            <if test="supplierBatch != null and supplierBatch != ''">supplier_batch = #{supplierBatch},</if>
            <if test="supplieryxqDate != null and supplieryxqDate != ''">supplieryxq_date = #{supplieryxqDate},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierSinvDataById" parameterType="Long">
        delete from supplier_sinv_data where id = #{id}
    </delete>

    <delete id="deleteSupplierSinvDataByIds" parameterType="String">
        delete from supplier_sinv_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>