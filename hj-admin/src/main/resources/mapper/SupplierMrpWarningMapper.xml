<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierMrpWarningMapper">
    
    <resultMap type="SupplierMrpWarning" id="SupplierMrpWarningResult">
        <result property="id"    column="id"    />
        <result property="plantId"    column="plant_id"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="quantityCurrent"    column="quantity_current"    />
        <result property="reckonDate"    column="reckon_date"    />
        <result property="quantityPlanned"    column="quantity_planned"    />
        <result property="quantityPlannedDelivery"    column="quantity_planned_delivery"    />
        <result property="quantityInTransit"    column="quantity_in_transit"    />
        <result property="dateGap"    column="date_gap"    />
        <result property="inventoryGap"    column="inventory_gap"    />
        <result property="createByUser"    column="create_by_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateByUser"    column="update_by_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="version"    column="version"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierMrpWarningVo">
        select id, plant_id, material_code, material_description, quantity_current, reckon_date, quantity_planned, quantity_planned_delivery, quantity_in_transit, date_gap, inventory_gap, create_by_user, create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by from supplier_mrp_warning
    </sql>

    <select id="selectSupplierMrpWarningList" parameterType="SupplierMrpWarning" resultMap="SupplierMrpWarningResult">
        <include refid="selectSupplierMrpWarningVo"/>
        <where>  
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="quantityCurrent != null "> and quantity_current = #{quantityCurrent}</if>
            <if test="reckonDate != null  and reckonDate != ''"> and reckon_date = #{reckonDate}</if>
            <if test="quantityPlanned != null "> and quantity_planned = #{quantityPlanned}</if>
            <if test="quantityPlannedDelivery != null "> and quantity_planned_delivery = #{quantityPlannedDelivery}</if>
            <if test="quantityInTransit != null "> and quantity_in_transit = #{quantityInTransit}</if>
            <if test="dateGap != null "> and date_gap = #{dateGap}</if>
            <if test="inventoryGap != null "> and inventory_gap = #{inventoryGap}</if>
            <if test="createByUser != null  and createByUser != ''"> and create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''"> and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierMrpWarningById" parameterType="Long" resultMap="SupplierMrpWarningResult">
        <include refid="selectSupplierMrpWarningVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierMrpWarning" parameterType="SupplierMrpWarning">
        insert into supplier_mrp_warning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="plantId != null">plant_id,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="quantityCurrent != null">quantity_current,</if>
            <if test="reckonDate != null">reckon_date,</if>
            <if test="quantityPlanned != null">quantity_planned,</if>
            <if test="quantityPlannedDelivery != null">quantity_planned_delivery,</if>
            <if test="quantityInTransit != null">quantity_in_transit,</if>
            <if test="dateGap != null">date_gap,</if>
            <if test="inventoryGap != null">inventory_gap,</if>
            <if test="createByUser != null">create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="plantId != null">#{plantId},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="quantityCurrent != null">#{quantityCurrent},</if>
            <if test="reckonDate != null">#{reckonDate},</if>
            <if test="quantityPlanned != null">#{quantityPlanned},</if>
            <if test="quantityPlannedDelivery != null">#{quantityPlannedDelivery},</if>
            <if test="quantityInTransit != null">#{quantityInTransit},</if>
            <if test="dateGap != null">#{dateGap},</if>
            <if test="inventoryGap != null">#{inventoryGap},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">#{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierMrpWarning" parameterType="SupplierMrpWarning">
        update supplier_mrp_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="plantId != null">plant_id = #{plantId},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="quantityCurrent != null">quantity_current = #{quantityCurrent},</if>
            <if test="reckonDate != null">reckon_date = #{reckonDate},</if>
            <if test="quantityPlanned != null">quantity_planned = #{quantityPlanned},</if>
            <if test="quantityPlannedDelivery != null">quantity_planned_delivery = #{quantityPlannedDelivery},</if>
            <if test="quantityInTransit != null">quantity_in_transit = #{quantityInTransit},</if>
            <if test="dateGap != null">date_gap = #{dateGap},</if>
            <if test="inventoryGap != null">inventory_gap = #{inventoryGap},</if>
            <if test="createByUser != null">create_by_user = #{createByUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierMrpWarningById" parameterType="Long">
        delete from supplier_mrp_warning where id = #{id}
    </delete>

    <delete id="deleteSupplierMrpWarningByIds" parameterType="String">
        delete from supplier_mrp_warning where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>