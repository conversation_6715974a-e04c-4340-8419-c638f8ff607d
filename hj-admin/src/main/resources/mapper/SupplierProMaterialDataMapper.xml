<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierProMaterialDataMapper">
    
    <resultMap type="SupplierProMaterialData" id="SupplierProMaterialDataResult">
        <result property="id"    column="id"    />
        <result property="supplierCode"    column="supplier_code"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="vendorProductNo"    column="vendor_product_no"    />
        <result property="vendorProductName"    column="vendor_product_name"    />
        <result property="type"    column="type"    />
        <result property="vendorHardwareRevision"    column="vendor_hardware_revision"    />
        <result property="cheryProductNo"    column="chery_product_no"    />
        <result property="cheryProductName"    column="chery_product_name"    />
        <result property="oemHardwareRevision"    column="oem_hardware_revision"    />
        <result property="oemSoftwareRevision"    column="oem_software_revision"    />
        <result property="oemModel"    column="oem_model"    />
        <result property="oemProjectName"    column="oem_project_name"    />
        <result property="launched"    column="launched"    />
        <result property="dateTime"    column="date_time"    />
        <result property="plantId"    column="plant_id"    />
        <result property="plantName"    column="plant_name"    />
        <result property="procurementType"    column="procurement_type"    />
        <result property="mpnCode"    column="mpn_code"    />
        <result property="mpnName"    column="mpn_name"    />
        <result property="validDays"    column="valid_days"    />
        <result property="hasPush"    column="has_push"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierProMaterialDataVo">
        select id, supplier_code, supplier_name, vendor_product_no, vendor_product_name, type, vendor_hardware_revision, chery_product_no, chery_product_name, oem_hardware_revision, oem_software_revision, oem_model, oem_project_name, launched, date_time, plant_id, plant_name, procurement_type, mpn_code, mpn_name, valid_days, has_push, insert_time, modify_time, insert_by, modify_by from supplier_pro_material_data
    </sql>

    <select id="selectSupplierProMaterialDataList" parameterType="SupplierProMaterialData" resultMap="SupplierProMaterialDataResult">
        <include refid="selectSupplierProMaterialDataVo"/>
        <where>  
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code = #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="vendorProductNo != null  and vendorProductNo != ''"> and vendor_product_no = #{vendorProductNo}</if>
            <if test="vendorProductName != null  and vendorProductName != ''"> and vendor_product_name like concat('%', #{vendorProductName}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="vendorHardwareRevision != null  and vendorHardwareRevision != ''"> and vendor_hardware_revision = #{vendorHardwareRevision}</if>
            <if test="cheryProductNo != null  and cheryProductNo != ''"> and chery_product_no = #{cheryProductNo}</if>
            <if test="cheryProductName != null  and cheryProductName != ''"> and chery_product_name like concat('%', #{cheryProductName}, '%')</if>
            <if test="oemHardwareRevision != null  and oemHardwareRevision != ''"> and oem_hardware_revision = #{oemHardwareRevision}</if>
            <if test="oemSoftwareRevision != null  and oemSoftwareRevision != ''"> and oem_software_revision = #{oemSoftwareRevision}</if>
            <if test="oemModel != null  and oemModel != ''"> and oem_model = #{oemModel}</if>
            <if test="oemProjectName != null  and oemProjectName != ''"> and oem_project_name like concat('%', #{oemProjectName}, '%')</if>
            <if test="launched != null  and launched != ''"> and launched = #{launched}</if>
            <if test="dateTime != null  and dateTime != ''"> and date_time = #{dateTime}</if>
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="plantName != null  and plantName != ''"> and plant_name like concat('%', #{plantName}, '%')</if>
            <if test="procurementType != null  and procurementType != ''"> and procurement_type = #{procurementType}</if>
            <if test="mpnCode != null  and mpnCode != ''"> and mpn_code = #{mpnCode}</if>
            <if test="mpnName != null  and mpnName != ''"> and mpn_name like concat('%', #{mpnName}, '%')</if>
            <if test="validDays != null  and validDays != ''"> and valid_days = #{validDays}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierProMaterialDataById" parameterType="Long" resultMap="SupplierProMaterialDataResult">
        <include refid="selectSupplierProMaterialDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierProMaterialData" parameterType="SupplierProMaterialData" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_pro_material_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no,</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="vendorHardwareRevision != null and vendorHardwareRevision != ''">vendor_hardware_revision,</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no,</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name,</if>
            <if test="oemHardwareRevision != null and oemHardwareRevision != ''">oem_hardware_revision,</if>
            <if test="oemSoftwareRevision != null and oemSoftwareRevision != ''">oem_software_revision,</if>
            <if test="oemModel != null and oemModel != ''">oem_model,</if>
            <if test="oemProjectName != null and oemProjectName != ''">oem_project_name,</if>
            <if test="launched != null and launched != ''">launched,</if>
            <if test="dateTime != null and dateTime != ''">date_time,</if>
            <if test="plantId != null and plantId != ''">plant_id,</if>
            <if test="plantName != null and plantName != ''">plant_name,</if>
            <if test="procurementType != null and procurementType != ''">procurement_type,</if>
            <if test="mpnCode != null and mpnCode != ''">mpn_code,</if>
            <if test="mpnName != null and mpnName != ''">mpn_name,</if>
            <if test="validDays != null and validDays != ''">valid_days,</if>
            <if test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">#{vendorProductNo},</if>
            <if test="vendorProductName != null and vendorProductName != ''">#{vendorProductName},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="vendorHardwareRevision != null and vendorHardwareRevision != ''">#{vendorHardwareRevision},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">#{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">#{cheryProductName},</if>
            <if test="oemHardwareRevision != null and oemHardwareRevision != ''">#{oemHardwareRevision},</if>
            <if test="oemSoftwareRevision != null and oemSoftwareRevision != ''">#{oemSoftwareRevision},</if>
            <if test="oemModel != null and oemModel != ''">#{oemModel},</if>
            <if test="oemProjectName != null and oemProjectName != ''">#{oemProjectName},</if>
            <if test="launched != null and launched != ''">#{launched},</if>
            <if test="dateTime != null and dateTime != ''">#{dateTime},</if>
            <if test="plantId != null and plantId != ''">#{plantId},</if>
            <if test="plantName != null and plantName != ''">#{plantName},</if>
            <if test="procurementType != null and procurementType != ''">#{procurementType},</if>
            <if test="mpnCode != null and mpnCode != ''">#{mpnCode},</if>
            <if test="mpnName != null and mpnName != ''">#{mpnName},</if>
            <if test="validDays != null and validDays != ''">#{validDays},</if>
            <if test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierProMaterialData" parameterType="SupplierProMaterialData">
        update supplier_pro_material_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no = #{vendorProductNo},</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name = #{vendorProductName},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="vendorHardwareRevision != null and vendorHardwareRevision != ''">vendor_hardware_revision = #{vendorHardwareRevision},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no = #{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name = #{cheryProductName},</if>
            <if test="oemHardwareRevision != null and oemHardwareRevision != ''">oem_hardware_revision = #{oemHardwareRevision},</if>
            <if test="oemSoftwareRevision != null and oemSoftwareRevision != ''">oem_software_revision = #{oemSoftwareRevision},</if>
            <if test="oemModel != null and oemModel != ''">oem_model = #{oemModel},</if>
            <if test="oemProjectName != null and oemProjectName != ''">oem_project_name = #{oemProjectName},</if>
            <if test="launched != null and launched != ''">launched = #{launched},</if>
            <if test="dateTime != null and dateTime != ''">date_time = #{dateTime},</if>
            <if test="plantId != null and plantId != ''">plant_id = #{plantId},</if>
            <if test="plantName != null and plantName != ''">plant_name = #{plantName},</if>
            <if test="procurementType != null and procurementType != ''">procurement_type = #{procurementType},</if>
            <if test="mpnCode != null and mpnCode != ''">mpn_code = #{mpnCode},</if>
            <if test="mpnName != null and mpnName != ''">mpn_name = #{mpnName},</if>
            <if test="validDays != null and validDays != ''">valid_days = #{validDays},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierProMaterialDataById" parameterType="Long">
        delete from supplier_pro_material_data where id = #{id}
    </delete>

    <delete id="deleteSupplierProMaterialDataByIds" parameterType="String">
        delete from supplier_pro_material_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>