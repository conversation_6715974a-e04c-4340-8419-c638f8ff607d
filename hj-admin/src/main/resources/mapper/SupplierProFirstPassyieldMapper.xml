<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierProFirstPassyieldMapper">
    
    <resultMap type="SupplierProFirstPassyield" id="SupplierProFirstPassyieldResult">
        <result property="id"    column="id"    />
        <result property="supplierCode"    column="supplier_code"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="vendorProductNo"    column="vendor_product_no"    />
        <result property="vendorProductName"    column="vendor_product_name"    />
        <result property="plantId"    column="plant_id"    />
        <result property="plantName"    column="plant_name"    />
        <result property="workshopId"    column="workshop_id"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="productionLineId"    column="production_line_id"    />
        <result property="productionLineName"    column="production_line_name"    />
        <result property="cheryProductNo"    column="chery_product_no"    />
        <result property="cheryProductName"    column="chery_product_name"    />
        <result property="manufactureNo"    column="manufacture_no"    />
        <result property="productBatchNo"    column="product_batch_no"    />
        <result property="workOrderNumber"    column="work_order_number"    />
        <result property="defectiveNumber"    column="defective_number"    />
        <result property="acceptableNumber"    column="acceptable_number"    />
        <result property="oncePassRateRealValue"    column="once_pass_rate_real_value"    />
        <result property="oncePassRateTagValue"    column="once_pass_rate_tag_value"    />
        <result property="workShift"    column="work_shift"    />
        <result property="statisticalTime"    column="statistical_time"    />
        <result property="dateTime"    column="date_time"    />
        <result property="hasPush"    column="has_push"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierProFirstPassyieldVo">
        select id, supplier_code, supplier_name, vendor_product_no, vendor_product_name, plant_id, plant_name, workshop_id, workshop_name, production_line_id, production_line_name, chery_product_no, chery_product_name, manufacture_no, product_batch_no, work_order_number, defective_number, acceptable_number, once_pass_rate_real_value, once_pass_rate_tag_value, work_shift, statistical_time, date_time, has_push, insert_time, modify_time, insert_by, modify_by from supplier_pro_first_passyield
    </sql>

    <select id="selectSupplierProFirstPassyieldList" parameterType="SupplierProFirstPassyield" resultMap="SupplierProFirstPassyieldResult">
        <include refid="selectSupplierProFirstPassyieldVo"/>
        <where>  
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code = #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="vendorProductNo != null  and vendorProductNo != ''"> and vendor_product_no = #{vendorProductNo}</if>
            <if test="vendorProductName != null  and vendorProductName != ''"> and vendor_product_name like concat('%', #{vendorProductName}, '%')</if>
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="plantName != null  and plantName != ''"> and plant_name like concat('%', #{plantName}, '%')</if>
            <if test="workshopId != null  and workshopId != ''"> and workshop_id = #{workshopId}</if>
            <if test="workshopName != null  and workshopName != ''"> and workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="productionLineId != null  and productionLineId != ''"> and production_line_id = #{productionLineId}</if>
            <if test="productionLineName != null  and productionLineName != ''"> and production_line_name like concat('%', #{productionLineName}, '%')</if>
            <if test="cheryProductNo != null  and cheryProductNo != ''"> and chery_product_no = #{cheryProductNo}</if>
            <if test="cheryProductName != null  and cheryProductName != ''"> and chery_product_name like concat('%', #{cheryProductName}, '%')</if>
            <if test="manufactureNo != null  and manufactureNo != ''"> and manufacture_no = #{manufactureNo}</if>
            <if test="productBatchNo != null  and productBatchNo != ''"> and product_batch_no = #{productBatchNo}</if>
            <if test="workOrderNumber != null "> and work_order_number = #{workOrderNumber}</if>
            <if test="defectiveNumber != null "> and defective_number = #{defectiveNumber}</if>
            <if test="acceptableNumber != null "> and acceptable_number = #{acceptableNumber}</if>
            <if test="oncePassRateRealValue != null "> and once_pass_rate_real_value = #{oncePassRateRealValue}</if>
            <if test="oncePassRateTagValue != null "> and once_pass_rate_tag_value = #{oncePassRateTagValue}</if>
            <if test="workShift != null  and workShift != ''"> and work_shift = #{workShift}</if>
            <if test="statisticalTime != null  and statisticalTime != ''"> and statistical_time = #{statisticalTime}</if>
            <if test="dateTime != null  and dateTime != ''"> and date_time = #{dateTime}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierProFirstPassyieldById" parameterType="Long" resultMap="SupplierProFirstPassyieldResult">
        <include refid="selectSupplierProFirstPassyieldVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierProFirstPassyield" parameterType="SupplierProFirstPassyield" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_pro_first_passyield
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no,</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name,</if>
            <if test="plantId != null and plantId != ''">plant_id,</if>
            <if test="plantName != null and plantName != ''">plant_name,</if>
            <if test="workshopId != null and workshopId != ''">workshop_id,</if>
            <if test="workshopName != null and workshopName != ''">workshop_name,</if>
            <if test="productionLineId != null and productionLineId != ''">production_line_id,</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name,</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no,</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name,</if>
            <if test="manufactureNo != null and manufactureNo != ''">manufacture_no,</if>
            <if test="productBatchNo != null and productBatchNo != ''">product_batch_no,</if>
            <if test="workOrderNumber != null">work_order_number,</if>
            <if test="defectiveNumber != null">defective_number,</if>
            <if test="acceptableNumber != null">acceptable_number,</if>
            <if test="oncePassRateRealValue != null">once_pass_rate_real_value,</if>
            <if test="oncePassRateTagValue != null">once_pass_rate_tag_value,</if>
            <if test="workShift != null and workShift != ''">work_shift,</if>
            <if test="statisticalTime != null and statisticalTime != ''">statistical_time,</if>
            <if test="dateTime != null and dateTime != ''">date_time,</if>
            <if test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">#{vendorProductNo},</if>
            <if test="vendorProductName != null and vendorProductName != ''">#{vendorProductName},</if>
            <if test="plantId != null and plantId != ''">#{plantId},</if>
            <if test="plantName != null and plantName != ''">#{plantName},</if>
            <if test="workshopId != null and workshopId != ''">#{workshopId},</if>
            <if test="workshopName != null and workshopName != ''">#{workshopName},</if>
            <if test="productionLineId != null and productionLineId != ''">#{productionLineId},</if>
            <if test="productionLineName != null and productionLineName != ''">#{productionLineName},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">#{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">#{cheryProductName},</if>
            <if test="manufactureNo != null and manufactureNo != ''">#{manufactureNo},</if>
            <if test="productBatchNo != null and productBatchNo != ''">#{productBatchNo},</if>
            <if test="workOrderNumber != null">#{workOrderNumber},</if>
            <if test="defectiveNumber != null">#{defectiveNumber},</if>
            <if test="acceptableNumber != null">#{acceptableNumber},</if>
            <if test="oncePassRateRealValue != null">#{oncePassRateRealValue},</if>
            <if test="oncePassRateTagValue != null">#{oncePassRateTagValue},</if>
            <if test="workShift != null and workShift != ''">#{workShift},</if>
            <if test="statisticalTime != null and statisticalTime != ''">#{statisticalTime},</if>
            <if test="dateTime != null and dateTime != ''">#{dateTime},</if>
            <if test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierProFirstPassyield" parameterType="SupplierProFirstPassyield">
        update supplier_pro_first_passyield
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no = #{vendorProductNo},</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name = #{vendorProductName},</if>
            <if test="plantId != null and plantId != ''">plant_id = #{plantId},</if>
            <if test="plantName != null and plantName != ''">plant_name = #{plantName},</if>
            <if test="workshopId != null and workshopId != ''">workshop_id = #{workshopId},</if>
            <if test="workshopName != null and workshopName != ''">workshop_name = #{workshopName},</if>
            <if test="productionLineId != null and productionLineId != ''">production_line_id = #{productionLineId},</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name = #{productionLineName},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no = #{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name = #{cheryProductName},</if>
            <if test="manufactureNo != null and manufactureNo != ''">manufacture_no = #{manufactureNo},</if>
            <if test="productBatchNo != null and productBatchNo != ''">product_batch_no = #{productBatchNo},</if>
            <if test="workOrderNumber != null">work_order_number = #{workOrderNumber},</if>
            <if test="defectiveNumber != null">defective_number = #{defectiveNumber},</if>
            <if test="acceptableNumber != null">acceptable_number = #{acceptableNumber},</if>
            <if test="oncePassRateRealValue != null">once_pass_rate_real_value = #{oncePassRateRealValue},</if>
            <if test="oncePassRateTagValue != null">once_pass_rate_tag_value = #{oncePassRateTagValue},</if>
            <if test="workShift != null and workShift != ''">work_shift = #{workShift},</if>
            <if test="statisticalTime != null and statisticalTime != ''">statistical_time = #{statisticalTime},</if>
            <if test="dateTime != null and dateTime != ''">date_time = #{dateTime},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierProFirstPassyieldById" parameterType="Long">
        delete from supplier_pro_first_passyield where id = #{id}
    </delete>

    <delete id="deleteSupplierProFirstPassyieldByIds" parameterType="String">
        delete from supplier_pro_first_passyield where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>