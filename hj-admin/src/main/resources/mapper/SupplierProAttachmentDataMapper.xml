<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierProAttachmentDataMapper">
    
    <resultMap type="SupplierProAttachmentData" id="SupplierProAttachmentDataResult">
        <result property="id"    column="id"    />
        <result property="supplierCode"    column="supplier_code"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="type"    column="type"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="dateTime"    column="date_time"    />
        <result property="productionLineName"    column="production_line_name"    />
        <result property="productionLineId"    column="production_line_id"    />
        <result property="stationName"    column="station_name"    />
        <result property="stationId"    column="station_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceId"    column="device_id"    />
        <result property="vendorProductNo"    column="vendor_product_no"    />
        <result property="vendorProductName"    column="vendor_product_name"    />
        <result property="cheryProductNo"    column="chery_product_no"    />
        <result property="cheryProductName"    column="chery_product_name"    />
        <result property="vendorProductSn"    column="vendor_product_sn"    />
        <result property="hasPush"    column="has_push"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierProAttachmentDataVo">
        select id, supplier_code, supplier_name, type, file_name, file_url, date_time, production_line_name, production_line_id, station_name, station_id, device_name, device_id, vendor_product_no, vendor_product_name, chery_product_no, chery_product_name, vendor_product_sn, has_push, insert_time, modify_time, insert_by, modify_by from supplier_pro_attachment_data
    </sql>

    <select id="selectSupplierProAttachmentDataList" parameterType="SupplierProAttachmentData" resultMap="SupplierProAttachmentDataResult">
        <include refid="selectSupplierProAttachmentDataVo"/>
        <where>  
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code = #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="dateTime != null  and dateTime != ''"> and date_time = #{dateTime}</if>
            <if test="productionLineName != null  and productionLineName != ''"> and production_line_name like concat('%', #{productionLineName}, '%')</if>
            <if test="productionLineId != null  and productionLineId != ''"> and production_line_id = #{productionLineId}</if>
            <if test="stationName != null  and stationName != ''"> and station_name like concat('%', #{stationName}, '%')</if>
            <if test="stationId != null  and stationId != ''"> and station_id = #{stationId}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="vendorProductNo != null  and vendorProductNo != ''"> and vendor_product_no = #{vendorProductNo}</if>
            <if test="vendorProductName != null  and vendorProductName != ''"> and vendor_product_name like concat('%', #{vendorProductName}, '%')</if>
            <if test="cheryProductNo != null  and cheryProductNo != ''"> and chery_product_no = #{cheryProductNo}</if>
            <if test="cheryProductName != null  and cheryProductName != ''"> and chery_product_name like concat('%', #{cheryProductName}, '%')</if>
            <if test="vendorProductSn != null  and vendorProductSn != ''"> and vendor_product_sn = #{vendorProductSn}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierProAttachmentDataById" parameterType="Long" resultMap="SupplierProAttachmentDataResult">
        <include refid="selectSupplierProAttachmentDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierProAttachmentData" parameterType="SupplierProAttachmentData" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_pro_attachment_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="dateTime != null and dateTime != ''">date_time,</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name,</if>
            <if test="productionLineId != null and productionLineId != ''">production_line_id,</if>
            <if test="stationName != null and stationName != ''">station_name,</if>
            <if test="stationId != null and stationId != ''">station_id,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="deviceId != null and deviceId != ''">device_id,</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no,</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name,</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no,</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name,</if>
            <if test="vendorProductSn != null and vendorProductSn != ''">vendor_product_sn,</if>
            <if test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="dateTime != null and dateTime != ''">#{dateTime},</if>
            <if test="productionLineName != null and productionLineName != ''">#{productionLineName},</if>
            <if test="productionLineId != null and productionLineId != ''">#{productionLineId},</if>
            <if test="stationName != null and stationName != ''">#{stationName},</if>
            <if test="stationId != null and stationId != ''">#{stationId},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="deviceId != null and deviceId != ''">#{deviceId},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">#{vendorProductNo},</if>
            <if test="vendorProductName != null and vendorProductName != ''">#{vendorProductName},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">#{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">#{cheryProductName},</if>
            <if test="vendorProductSn != null and vendorProductSn != ''">#{vendorProductSn},</if>
            <if test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierProAttachmentData" parameterType="SupplierProAttachmentData">
        update supplier_pro_attachment_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="dateTime != null and dateTime != ''">date_time = #{dateTime},</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name = #{productionLineName},</if>
            <if test="productionLineId != null and productionLineId != ''">production_line_id = #{productionLineId},</if>
            <if test="stationName != null and stationName != ''">station_name = #{stationName},</if>
            <if test="stationId != null and stationId != ''">station_id = #{stationId},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="deviceId != null and deviceId != ''">device_id = #{deviceId},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no = #{vendorProductNo},</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name = #{vendorProductName},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no = #{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name = #{cheryProductName},</if>
            <if test="vendorProductSn != null and vendorProductSn != ''">vendor_product_sn = #{vendorProductSn},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierProAttachmentDataById" parameterType="Long">
        delete from supplier_pro_attachment_data where id = #{id}
    </delete>

    <delete id="deleteSupplierProAttachmentDataByIds" parameterType="String">
        delete from supplier_pro_attachment_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>