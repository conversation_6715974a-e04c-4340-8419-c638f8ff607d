<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.SupplierSaWeekMapper">
    
    <resultMap type="SupplierSaWeek" id="SupplierSaWeekResult">
        <result property="id"    column="id"    />
        <result property="scheduleAgreement"    column="schedule_agreement"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="purchasingGroup"    column="purchasing_group"    />
        <result property="plantId"    column="plant_id"    />
        <result property="quantityDemand"    column="quantity_demand"    />
        <result property="dateReceived"    column="date_received"    />
        <result property="createByUser"    column="create_by_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateByUser"    column="update_by_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="version"    column="version"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierSaWeekVo">
        select id, schedule_agreement, serial_number, material_code, material_description, purchasing_group, plant_id, quantity_demand, date_received, create_by_user, create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by from supplier_sa_week
    </sql>

    <select id="selectSupplierSaWeekList" parameterType="SupplierSaWeek" resultMap="SupplierSaWeekResult">
        <include refid="selectSupplierSaWeekVo"/>
        <where>  
            <if test="scheduleAgreement != null  and scheduleAgreement != ''"> and schedule_agreement = #{scheduleAgreement}</if>
            <if test="serialNumber != null  and serialNumber != ''"> and serial_number = #{serialNumber}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="purchasingGroup != null  and purchasingGroup != ''"> and purchasing_group = #{purchasingGroup}</if>
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="quantityDemand != null "> and quantity_demand = #{quantityDemand}</if>
            <if test="dateReceived != null  and dateReceived != ''"> and date_received = #{dateReceived}</if>
            <if test="createByUser != null  and createByUser != ''"> and create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''"> and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierSaWeekById" parameterType="Long" resultMap="SupplierSaWeekResult">
        <include refid="selectSupplierSaWeekVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierSaWeek" parameterType="SupplierSaWeek">
        insert into supplier_sa_week
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="scheduleAgreement != null">schedule_agreement,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="purchasingGroup != null">purchasing_group,</if>
            <if test="plantId != null">plant_id,</if>
            <if test="quantityDemand != null">quantity_demand,</if>
            <if test="dateReceived != null">date_received,</if>
            <if test="createByUser != null">create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="scheduleAgreement != null">#{scheduleAgreement},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="purchasingGroup != null">#{purchasingGroup},</if>
            <if test="plantId != null">#{plantId},</if>
            <if test="quantityDemand != null">#{quantityDemand},</if>
            <if test="dateReceived != null">#{dateReceived},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">#{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierSaWeek" parameterType="SupplierSaWeek">
        update supplier_sa_week
        <trim prefix="SET" suffixOverrides=",">
            <if test="scheduleAgreement != null">schedule_agreement = #{scheduleAgreement},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="purchasingGroup != null">purchasing_group = #{purchasingGroup},</if>
            <if test="plantId != null">plant_id = #{plantId},</if>
            <if test="quantityDemand != null">quantity_demand = #{quantityDemand},</if>
            <if test="dateReceived != null">date_received = #{dateReceived},</if>
            <if test="createByUser != null">create_by_user = #{createByUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierSaWeekById" parameterType="Long">
        delete from supplier_sa_week where id = #{id}
    </delete>

    <delete id="deleteSupplierSaWeekByIds" parameterType="String">
        delete from supplier_sa_week where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>