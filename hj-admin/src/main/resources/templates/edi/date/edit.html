<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改日物料需求计划')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-date-edit" th:object="${supplierMrpDate}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求发布版次:用于日物料需求计划风险确认接口反馈数据的ID：</label>
                    <div class="col-sm-8">
                        <input name="releaseEdition" th:field="*{releaseEdition}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">零件号:奇瑞零件号：</label>
                    <div class="col-sm-8">
                        <input name="materialCode" th:field="*{materialCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">零件名称 ：</label>
                    <div class="col-sm-8">
                        <input name="materialDescription" th:field="*{materialDescription}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">工厂代码：</label>
                    <div class="col-sm-8">
                        <input name="plantId" th:field="*{plantId}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">工厂名称：</label>
                    <div class="col-sm-8">
                        <input name="plantName" th:field="*{plantName}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">起始日期-格式：yyyy-MM-dd：</label>
                    <div class="col-sm-8">
                        <input name="startDate" th:field="*{startDate}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量1：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand1" th:field="*{quantityDemand1}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量2：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand2" th:field="*{quantityDemand2}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量3：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand3" th:field="*{quantityDemand3}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量4：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand4" th:field="*{quantityDemand4}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量5：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand5" th:field="*{quantityDemand5}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量6：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand6" th:field="*{quantityDemand6}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量7：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand7" th:field="*{quantityDemand7}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量8：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand8" th:field="*{quantityDemand8}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量9：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand9" th:field="*{quantityDemand9}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量10：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand10" th:field="*{quantityDemand10}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量11：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand11" th:field="*{quantityDemand11}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量12：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand12" th:field="*{quantityDemand12}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量13：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand13" th:field="*{quantityDemand13}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量14：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand14" th:field="*{quantityDemand14}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量15：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand15" th:field="*{quantityDemand15}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量16：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand16" th:field="*{quantityDemand16}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量17：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand17" th:field="*{quantityDemand17}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量18：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand18" th:field="*{quantityDemand18}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量19：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand19" th:field="*{quantityDemand19}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量20：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand20" th:field="*{quantityDemand20}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量21：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand21" th:field="*{quantityDemand21}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量22：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand22" th:field="*{quantityDemand22}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量23：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand23" th:field="*{quantityDemand23}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量24：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand24" th:field="*{quantityDemand24}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量25：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand25" th:field="*{quantityDemand25}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量26：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand26" th:field="*{quantityDemand26}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量27：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand27" th:field="*{quantityDemand27}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量28：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand28" th:field="*{quantityDemand28}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量29：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand29" th:field="*{quantityDemand29}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量30：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand30" th:field="*{quantityDemand30}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量31：</label>
                    <div class="col-sm-8">
                        <input name="quantityDemand31" th:field="*{quantityDemand31}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">当文件夹数据发生变更时(更新需求=1/否则=0)：</label>
                    <div class="col-sm-8">
                        <input name="isUpdate" th:field="*{isUpdate}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">创建人：</label>
                    <div class="col-sm-8">
                        <input name="createByUser" th:field="*{createByUser}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">修改人：</label>
                    <div class="col-sm-8">
                        <input name="updateByUser" th:field="*{updateByUser}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">是否删除(0:否,1是)(系统默认字段与业务数据无关)：</label>
                    <div class="col-sm-8">
                        <input name="isDelete" th:field="*{isDelete}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">版本号(系统默认字段与业务数据无关)：</label>
                    <div class="col-sm-8">
                        <input name="version" th:field="*{version}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">插入时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="insertTime" th:value="${#dates.format(supplierMrpDate.insertTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">调整时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="modifyTime" th:value="${#dates.format(supplierMrpDate.modifyTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">插入人：</label>
                    <div class="col-sm-8">
                        <input name="insertBy" th:field="*{insertBy}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">调整人：</label>
                    <div class="col-sm-8">
                        <input name="modifyBy" th:field="*{modifyBy}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "edi/date";
        $("#form-date-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-date-edit').serialize());
            }
        }

        $("input[name='insertTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='modifyTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>