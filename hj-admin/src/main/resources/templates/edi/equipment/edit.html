<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改工艺装备')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-equipment-edit" th:object="${supplierProProcessEquipment}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商代码：</label>
                    <div class="col-sm-8">
                        <input name="supplierCode" th:field="*{supplierCode}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商名称：</label>
                    <div class="col-sm-8">
                        <input name="supplierName" th:field="*{supplierName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工艺装备代码：</label>
                    <div class="col-sm-8">
                        <input name="deviceId" th:field="*{deviceId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工艺装备名称：</label>
                    <div class="col-sm-8">
                        <input name="deviceName" th:field="*{deviceName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">生产厂家：</label>
                    <div class="col-sm-8">
                        <input name="manufacturer" th:field="*{manufacturer}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工艺装备型号：</label>
                    <div class="col-sm-8">
                        <input name="modelNumber" th:field="*{modelNumber}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工艺装备制造日期：</label>
                    <div class="col-sm-8">
                        <input name="productionDate" th:field="*{productionDate}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">主要材质：</label>
                    <div class="col-sm-8">
                        <input name="material" th:field="*{material}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">当前存放地点：</label>
                    <div class="col-sm-8">
                        <input name="currentLocation" th:field="*{currentLocation}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">设计寿命单位：</label>
                    <div class="col-sm-8">
                        <input name="designLifeUnits" th:field="*{designLifeUnits}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">设计寿命：</label>
                    <div class="col-sm-8">
                        <input name="designLifeValue" th:field="*{designLifeValue}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">当前剩余寿命：</label>
                    <div class="col-sm-8">
                        <input name="currentUsageCount" th:field="*{currentUsageCount}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">每月有效工作天数：</label>
                    <div class="col-sm-8">
                        <input name="effectiveDays" th:field="*{effectiveDays}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">每天最大加工工时：</label>
                    <div class="col-sm-8">
                        <input name="maxProcessHours" th:field="*{maxProcessHours}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">每天常规加工工时：</label>
                    <div class="col-sm-8">
                        <input name="regularProcessHours" th:field="*{regularProcessHours}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">投产日期：</label>
                    <div class="col-sm-8">
                        <input name="deviceStartDate" th:field="*{deviceStartDate}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">报废停产日期：</label>
                    <div class="col-sm-8">
                        <input name="deviceEndDate" th:field="*{deviceEndDate}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">单台设备投资金额：</label>
                    <div class="col-sm-8">
                        <input name="machineCosts" th:field="*{machineCosts}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">设备购买周期：</label>
                    <div class="col-sm-8">
                        <input name="machinePurchasePeriod" th:field="*{machinePurchasePeriod}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">单位小时产出：</label>
                    <div class="col-sm-8">
                        <input name="unitperHour" th:field="*{unitperHour}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">穴腔数量：</label>
                    <div class="col-sm-8">
                        <input name="cavityCount" th:field="*{cavityCount}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">模具尺寸规格：</label>
                    <div class="col-sm-8">
                        <input name="moldSize" th:field="*{moldSize}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">模具复制模费用：</label>
                    <div class="col-sm-8">
                        <input name="copyMoldCosts" th:field="*{copyMoldCosts}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">模具大修次数：</label>
                    <div class="col-sm-8">
                        <input name="overhaulCount" th:field="*{overhaulCount}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">检具最近校准日期：</label>
                    <div class="col-sm-8">
                        <input name="calibrationDate" th:field="*{calibrationDate}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">检具校准到期天数：</label>
                    <div class="col-sm-8">
                        <input name="calibrationDueDays" th:field="*{calibrationDueDays}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">是否推送过数据 0未推送 1已推送：</label>
                    <div class="col-sm-8">
                        <input name="hasPush" th:field="*{hasPush}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">插入时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="insertTime" th:value="${#dates.format(supplierProProcessEquipment.insertTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">调整时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="modifyTime" th:value="${#dates.format(supplierProProcessEquipment.modifyTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">插入人：</label>
                    <div class="col-sm-8">
                        <input name="insertBy" th:field="*{insertBy}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">调整人：</label>
                    <div class="col-sm-8">
                        <input name="modifyBy" th:field="*{modifyBy}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "edi/equipment";
        $("#form-equipment-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-equipment-edit').serialize());
            }
        }

        $("input[name='insertTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='modifyTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>