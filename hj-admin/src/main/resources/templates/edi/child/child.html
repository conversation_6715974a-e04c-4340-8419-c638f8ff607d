<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('工艺子集（关联supplier_pro_process）列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>父级id：</label>
                                <input type="text" name="parentId"/>
                            </li>
                            <li>
                                <label>工序编码：</label>
                                <input type="text" name="processCode"/>
                            </li>
                            <li>
                                <label>工序名称：</label>
                                <input type="text" name="processName"/>
                            </li>
                            <li>
                                <label>工序顺序号：</label>
                                <input type="text" name="processOrder"/>
                            </li>
                            <li>
                                <label>工序节拍：</label>
                                <input type="text" name="rhythm"/>
                            </li>
                            <li>
                                <label>委外工序供应商编码：</label>
                                <input type="text" name="processSupplierCode"/>
                            </li>
                            <li>
                                <label>委外工序供应商名称：</label>
                                <input type="text" name="processSupplierName"/>
                            </li>
                            <li>
                                <label>工序所在地：</label>
                                <input type="text" name="processLocation"/>
                            </li>
                            <li>
                                <label>工序间流转时间：</label>
                                <input type="text" name="processCycleTime"/>
                            </li>
                            <li>
                                <label>工序良率目标：</label>
                                <input type="text" name="processYieldTarget"/>
                            </li>
                            <li>
                                <label>插入时间：</label>
                                <input type="text" class="time-input" placeholder="请选择插入时间" name="insertTime"/>
                            </li>
                            <li>
                                <label>调整时间：</label>
                                <input type="text" class="time-input" placeholder="请选择调整时间" name="modifyTime"/>
                            </li>
                            <li>
                                <label>插入人：</label>
                                <input type="text" name="insertBy"/>
                            </li>
                            <li>
                                <label>调整人：</label>
                                <input type="text" name="modifyBy"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:child:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:child:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:child:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:child:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:child:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:child:remove')}]];
        var prefix = ctx + "edi/child";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "工艺子集（关联supplier_pro_process）",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'parentId',
                    title: '父级id'
                },
                {
                    field: 'processCode',
                    title: '工序编码'
                },
                {
                    field: 'processName',
                    title: '工序名称'
                },
                {
                    field: 'processOrder',
                    title: '工序顺序号'
                },
                {
                    field: 'rhythm',
                    title: '工序节拍'
                },
                {
                    field: 'processSupplierCode',
                    title: '委外工序供应商编码'
                },
                {
                    field: 'processSupplierName',
                    title: '委外工序供应商名称'
                },
                {
                    field: 'processLocation',
                    title: '工序所在地'
                },
                {
                    field: 'processCycleTime',
                    title: '工序间流转时间'
                },
                {
                    field: 'processYieldTarget',
                    title: '工序良率目标'
                },
                {
                    field: 'insertTime',
                    title: '插入时间'
                },
                {
                    field: 'modifyTime',
                    title: '调整时间'
                },
                {
                    field: 'insertBy',
                    title: '插入人'
                },
                {
                    field: 'modifyBy',
                    title: '调整人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>