<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改日MRP预警推移')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-warning-edit" th:object="${supplierMrpWarning}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">工厂代码：</label>
                    <div class="col-sm-8">
                        <input name="plantId" th:field="*{plantId}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">零件号:奇瑞零件号：</label>
                    <div class="col-sm-8">
                        <input name="materialCode" th:field="*{materialCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">零件名称：</label>
                    <div class="col-sm-8">
                        <input name="materialDescription" th:field="*{materialDescription}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">当前库存：</label>
                    <div class="col-sm-8">
                        <input name="quantityCurrent" th:field="*{quantityCurrent}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求日期-格式:yyyy-MM-dd：</label>
                    <div class="col-sm-8">
                        <input name="reckonDate" th:field="*{reckonDate}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">需求数量：</label>
                    <div class="col-sm-8">
                        <input name="quantityPlanned" th:field="*{quantityPlanned}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">满足数量：</label>
                    <div class="col-sm-8">
                        <input name="quantityPlannedDelivery" th:field="*{quantityPlannedDelivery}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">在途数量：</label>
                    <div class="col-sm-8">
                        <input name="quantityInTransit" th:field="*{quantityInTransit}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">日GAP:日需求数量与满足数量差异：</label>
                    <div class="col-sm-8">
                        <input name="dateGap" th:field="*{dateGap}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">库存GAP:库存推移差异：</label>
                    <div class="col-sm-8">
                        <input name="inventoryGap" th:field="*{inventoryGap}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">创建人：</label>
                    <div class="col-sm-8">
                        <input name="createByUser" th:field="*{createByUser}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">修改人：</label>
                    <div class="col-sm-8">
                        <input name="updateByUser" th:field="*{updateByUser}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">是否删除(0:否,1是)(系统默认字段与业务数据无关)：</label>
                    <div class="col-sm-8">
                        <input name="isDelete" th:field="*{isDelete}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">版本号(系统默认字段与业务数据无关)：</label>
                    <div class="col-sm-8">
                        <input name="version" th:field="*{version}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">插入时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="insertTime" th:value="${#dates.format(supplierMrpWarning.insertTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">调整时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="modifyTime" th:value="${#dates.format(supplierMrpWarning.modifyTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">插入人：</label>
                    <div class="col-sm-8">
                        <input name="insertBy" th:field="*{insertBy}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">调整人：</label>
                    <div class="col-sm-8">
                        <input name="modifyBy" th:field="*{modifyBy}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "edi/warning";
        $("#form-warning-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-warning-edit').serialize());
            }
        }

        $("input[name='insertTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='modifyTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>