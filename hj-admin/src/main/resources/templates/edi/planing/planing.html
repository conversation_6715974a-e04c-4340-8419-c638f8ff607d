<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('整车月度生产计划列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>需求发布版次:唯一版次ID ：</label>
                                <input type="text" name="releaseEdition"/>
                            </li>
                            <li>
                                <label>车型：</label>
                                <input type="text" name="models"/>
                            </li>
                            <li>
                                <label>销售单位：</label>
                                <input type="text" name="salseDepartment"/>
                            </li>
                            <li>
                                <label>动力总成：</label>
                                <input type="text" name="assembly"/>
                            </li>
                            <li>
                                <label>版型：</label>
                                <input type="text" name="pattern"/>
                            </li>
                            <li>
                                <label>内饰：</label>
                                <input type="text" name="omterior"/>
                            </li>
                            <li>
                                <label>物料号：</label>
                                <input type="text" name="materialCode"/>
                            </li>
                            <li>
                                <label>起始月份-格式：yyyy-MM：</label>
                                <input type="text" name="startMonth"/>
                            </li>
                            <li>
                                <label>数量1：</label>
                                <input type="text" name="quantity1"/>
                            </li>
                            <li>
                                <label>数量2：</label>
                                <input type="text" name="quantity2"/>
                            </li>
                            <li>
                                <label>数量3：</label>
                                <input type="text" name="quantity3"/>
                            </li>
                            <li>
                                <label>数量4：</label>
                                <input type="text" name="quantity4"/>
                            </li>
                            <li>
                                <label>数量5：</label>
                                <input type="text" name="quantity5"/>
                            </li>
                            <li>
                                <label>数量6：</label>
                                <input type="text" name="quantity6"/>
                            </li>
                            <li>
                                <label>工厂：</label>
                                <input type="text" name="plant"/>
                            </li>
                            <li>
                                <label>二级车型代码-细：</label>
                                <input type="text" name="models2"/>
                            </li>
                            <li>
                                <label>排量：</label>
                                <input type="text" name="displacement"/>
                            </li>
                            <li>
                                <label>创建人：</label>
                                <input type="text" name="createByUser"/>
                            </li>
                            <li>
                                <label>修改人：</label>
                                <input type="text" name="updateByUser"/>
                            </li>
                            <li>
                                <label>是否删除(0:否,1是)(系统默认字段与业务数据无关)：</label>
                                <input type="text" name="isDelete"/>
                            </li>
                            <li>
                                <label>版本号(系统默认字段与业务数据无关)：</label>
                                <input type="text" name="version"/>
                            </li>
                            <li>
                                <label>插入时间：</label>
                                <input type="text" class="time-input" placeholder="请选择插入时间" name="insertTime"/>
                            </li>
                            <li>
                                <label>调整时间：</label>
                                <input type="text" class="time-input" placeholder="请选择调整时间" name="modifyTime"/>
                            </li>
                            <li>
                                <label>插入人：</label>
                                <input type="text" name="insertBy"/>
                            </li>
                            <li>
                                <label>调整人：</label>
                                <input type="text" name="modifyBy"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:planing:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:planing:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:planing:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:planing:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:planing:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:planing:remove')}]];
        var prefix = ctx + "edi/planing";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "整车月度生产计划",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
                    visible: false
                },
                {
                    field: 'releaseEdition',
                    title: '需求发布版次:唯一版次ID '
                },
                {
                    field: 'models',
                    title: '车型'
                },
                {
                    field: 'salseDepartment',
                    title: '销售单位'
                },
                {
                    field: 'type',
                    title: '类型'
                },
                {
                    field: 'assembly',
                    title: '动力总成'
                },
                {
                    field: 'pattern',
                    title: '版型'
                },
                {
                    field: 'omterior',
                    title: '内饰'
                },
                {
                    field: 'materialCode',
                    title: '物料号'
                },
                {
                    field: 'startMonth',
                    title: '起始月份-格式：yyyy-MM'
                },
                {
                    field: 'quantity1',
                    title: '数量1'
                },
                {
                    field: 'quantity2',
                    title: '数量2'
                },
                {
                    field: 'quantity3',
                    title: '数量3'
                },
                {
                    field: 'quantity4',
                    title: '数量4'
                },
                {
                    field: 'quantity5',
                    title: '数量5'
                },
                {
                    field: 'quantity6',
                    title: '数量6'
                },
                {
                    field: 'plant',
                    title: '工厂'
                },
                {
                    field: 'models2',
                    title: '二级车型代码-细'
                },
                {
                    field: 'displacement',
                    title: '排量'
                },
                {
                    field: 'createByUser',
                    title: '创建人'
                },
                {
                    field: 'updateByUser',
                    title: '修改人'
                },
                {
                    field: 'isDelete',
                    title: '是否删除(0:否,1是)(系统默认字段与业务数据无关)'
                },
                {
                    field: 'version',
                    title: '版本号(系统默认字段与业务数据无关)'
                },
                {
                    field: 'insertTime',
                    title: '插入时间'
                },
                {
                    field: 'modifyTime',
                    title: '调整时间'
                },
                {
                    field: 'insertBy',
                    title: '插入人'
                },
                {
                    field: 'modifyBy',
                    title: '调整人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>