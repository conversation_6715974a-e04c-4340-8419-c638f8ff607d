<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('M+6月物料需求计划列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>需求发布版次:M+6月物料需求计划风险确认接口对应需求版次，需求ID：</label>
                                <input type="text" name="releaseEdition"/>
                            </li>
                            <li>
                                <label>零件号:奇瑞零件号：</label>
                                <input type="text" name="materialCode"/>
                            </li>
                            <li>
                                <label>零件名称：</label>
                                <input type="text" name="materialDescription"/>
                            </li>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>工厂名称：</label>
                                <input type="text" name="plantName"/>
                            </li>
                            <li>
                                <label>起始月份-格式：yyyy-MM ：</label>
                                <input type="text" name="startMonth"/>
                            </li>
                            <li>
                                <label>需求数量1：</label>
                                <input type="text" name="quantityDemand1"/>
                            </li>
                            <li>
                                <label>需求数量2：</label>
                                <input type="text" name="quantityDemand2"/>
                            </li>
                            <li>
                                <label>需求数量3：</label>
                                <input type="text" name="quantityDemand3"/>
                            </li>
                            <li>
                                <label>需求数量4：</label>
                                <input type="text" name="quantityDemand4"/>
                            </li>
                            <li>
                                <label>需求数量5：</label>
                                <input type="text" name="quantityDemand5"/>
                            </li>
                            <li>
                                <label>需求数量6：</label>
                                <input type="text" name="quantityDemand6"/>
                            </li>
                            <li>
                                <label>需求数量7：</label>
                                <input type="text" name="quantityDemand7"/>
                            </li>
                            <li>
                                <label>需求数量8：</label>
                                <input type="text" name="quantityDemand8"/>
                            </li>
                            <li>
                                <label>需求数量9：</label>
                                <input type="text" name="quantityDemand9"/>
                            </li>
                            <li>
                                <label>需求数量10：</label>
                                <input type="text" name="quantityDemand10"/>
                            </li>
                            <li>
                                <label>需求数量11：</label>
                                <input type="text" name="quantityDemand11"/>
                            </li>
                            <li>
                                <label>需求数量12：</label>
                                <input type="text" name="quantityDemand12"/>
                            </li>
                            <li>
                                <label>当文件夹数据发生变更时(更新需求=1/否则=0)：</label>
                                <input type="text" name="isUpdate"/>
                            </li>
                            <li>
                                <label>创建人：</label>
                                <input type="text" name="createByUser"/>
                            </li>
                            <li>
                                <label>修改人：</label>
                                <input type="text" name="updateByUser"/>
                            </li>
                            <li>
                                <label>是否删除(0:否,1是)(系统默认字段与业务数据无关)：</label>
                                <input type="text" name="isDelete"/>
                            </li>
                            <li>
                                <label>版本号(系统默认字段与业务数据无关)：</label>
                                <input type="text" name="version"/>
                            </li>
                            <li>
                                <label>插入时间：</label>
                                <input type="text" class="time-input" placeholder="请选择插入时间" name="insertTime"/>
                            </li>
                            <li>
                                <label>调整时间：</label>
                                <input type="text" class="time-input" placeholder="请选择调整时间" name="modifyTime"/>
                            </li>
                            <li>
                                <label>插入人：</label>
                                <input type="text" name="insertBy"/>
                            </li>
                            <li>
                                <label>调整人：</label>
                                <input type="text" name="modifyBy"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:month:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:month:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:month:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:month:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:month:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:month:remove')}]];
        var prefix = ctx + "edi/month";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "M+6月物料需求计划",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
                    visible: false
                },
                {
                    field: 'releaseEdition',
                    title: '需求发布版次:M+6月物料需求计划风险确认接口对应需求版次，需求ID'
                },
                {
                    field: 'materialCode',
                    title: '零件号:奇瑞零件号'
                },
                {
                    field: 'materialDescription',
                    title: '零件名称'
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'plantName',
                    title: '工厂名称'
                },
                {
                    field: 'startMonth',
                    title: '起始月份-格式：yyyy-MM '
                },
                {
                    field: 'quantityDemand1',
                    title: '需求数量1'
                },
                {
                    field: 'quantityDemand2',
                    title: '需求数量2'
                },
                {
                    field: 'quantityDemand3',
                    title: '需求数量3'
                },
                {
                    field: 'quantityDemand4',
                    title: '需求数量4'
                },
                {
                    field: 'quantityDemand5',
                    title: '需求数量5'
                },
                {
                    field: 'quantityDemand6',
                    title: '需求数量6'
                },
                {
                    field: 'quantityDemand7',
                    title: '需求数量7'
                },
                {
                    field: 'quantityDemand8',
                    title: '需求数量8'
                },
                {
                    field: 'quantityDemand9',
                    title: '需求数量9'
                },
                {
                    field: 'quantityDemand10',
                    title: '需求数量10'
                },
                {
                    field: 'quantityDemand11',
                    title: '需求数量11'
                },
                {
                    field: 'quantityDemand12',
                    title: '需求数量12'
                },
                {
                    field: 'isUpdate',
                    title: '当文件夹数据发生变更时(更新需求=1/否则=0)'
                },
                {
                    field: 'createByUser',
                    title: '创建人'
                },
                {
                    field: 'updateByUser',
                    title: '修改人'
                },
                {
                    field: 'isDelete',
                    title: '是否删除(0:否,1是)(系统默认字段与业务数据无关)'
                },
                {
                    field: 'version',
                    title: '版本号(系统默认字段与业务数据无关)'
                },
                {
                    field: 'insertTime',
                    title: '插入时间'
                },
                {
                    field: 'modifyTime',
                    title: '调整时间'
                },
                {
                    field: 'insertBy',
                    title: '插入人'
                },
                {
                    field: 'modifyBy',
                    title: '调整人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>