<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('人员资质信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>供应商代码：</label>
                                <input type="text" name="supplierCode"/>
                            </li>
                            <li>
                                <label>供应商名称：</label>
                                <input type="text" name="supplierName"/>
                            </li>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>工厂名称：</label>
                                <input type="text" name="plantName"/>
                            </li>
                            <li>
                                <label>车间代码：</label>
                                <input type="text" name="workshopId"/>
                            </li>
                            <li>
                                <label>车间名称：</label>
                                <input type="text" name="workshopName"/>
                            </li>
                            <li>
                                <label>产线代码：</label>
                                <input type="text" name="productionLineId"/>
                            </li>
                            <li>
                                <label>产线名称：</label>
                                <input type="text" name="productionLineName"/>
                            </li>
                            <li>
                                <label>工位代码：</label>
                                <input type="text" name="stationId"/>
                            </li>
                            <li>
                                <label>工位名称：</label>
                                <input type="text" name="stationName"/>
                            </li>
                            <li>
                                <label>工位人员账号：</label>
                                <input type="text" name="operatorId"/>
                            </li>
                            <li>
                                <label>工位人员姓名：</label>
                                <input type="text" name="operatorName"/>
                            </li>
                            <li>
                                <label>是否有资质(Y,N)：</label>
                                <input type="text" name="haveQuantity"/>
                            </li>
                            <li>
                                <label>供应商修改时间,格式(yyyy-MM-dd HH:mm:ss)：</label>
                                <input type="text" name="dataUpdateTime"/>
                            </li>
                            <li>
                                <label>岗位代码：</label>
                                <input type="text" name="positionId"/>
                            </li>
                            <li>
                                <label>岗位名称：</label>
                                <input type="text" name="positionName"/>
                            </li>
                            <li>
                                <label>资质等级(Level_4，Level_3，Level_2, Level_1)：</label>
                                <input type="text" name="qualificationLevel"/>
                            </li>
                            <li>
                                <label>资质获取时间：</label>
                                <input type="text" name="checkInTime"/>
                            </li>
                            <li>
                                <label>资质失去时间：</label>
                                <input type="text" name="checkOutTime"/>
                            </li>
                            <li>
                                <label>是否推送过数据 0未推送 1已推送：</label>
                                <input type="text" name="hasPush"/>
                            </li>
                            <li>
                                <label>插入时间：</label>
                                <input type="text" class="time-input" placeholder="请选择插入时间" name="insertTime"/>
                            </li>
                            <li>
                                <label>调整时间：</label>
                                <input type="text" class="time-input" placeholder="请选择调整时间" name="modifyTime"/>
                            </li>
                            <li>
                                <label>插入人：</label>
                                <input type="text" name="insertBy"/>
                            </li>
                            <li>
                                <label>调整人：</label>
                                <input type="text" name="modifyBy"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:employee:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:employee:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:employee:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:employee:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:employee:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:employee:remove')}]];
        var prefix = ctx + "edi/employee";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "人员资质信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'supplierCode',
                    title: '供应商代码'
                },
                {
                    field: 'supplierName',
                    title: '供应商名称'
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'plantName',
                    title: '工厂名称'
                },
                {
                    field: 'workshopId',
                    title: '车间代码'
                },
                {
                    field: 'workshopName',
                    title: '车间名称'
                },
                {
                    field: 'productionLineId',
                    title: '产线代码'
                },
                {
                    field: 'productionLineName',
                    title: '产线名称'
                },
                {
                    field: 'stationId',
                    title: '工位代码'
                },
                {
                    field: 'stationName',
                    title: '工位名称'
                },
                {
                    field: 'operatorId',
                    title: '工位人员账号'
                },
                {
                    field: 'operatorName',
                    title: '工位人员姓名'
                },
                {
                    field: 'haveQuantity',
                    title: '是否有资质(Y,N)'
                },
                {
                    field: 'dataUpdateTime',
                    title: '供应商修改时间,格式(yyyy-MM-dd HH:mm:ss)'
                },
                {
                    field: 'positionId',
                    title: '岗位代码'
                },
                {
                    field: 'positionName',
                    title: '岗位名称'
                },
                {
                    field: 'qualificationLevel',
                    title: '资质等级(Level_4，Level_3，Level_2, Level_1)'
                },
                {
                    field: 'checkInTime',
                    title: '资质获取时间'
                },
                {
                    field: 'checkOutTime',
                    title: '资质失去时间'
                },
                {
                    field: 'hasPush',
                    title: '是否推送过数据 0未推送 1已推送'
                },
                {
                    field: 'insertTime',
                    title: '插入时间'
                },
                {
                    field: 'modifyTime',
                    title: '调整时间'
                },
                {
                    field: 'insertBy',
                    title: '插入人'
                },
                {
                    field: 'modifyBy',
                    title: '调整人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>