<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增M+6月物料需求计划风险确认')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-mmrp-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商代码：</label>
                    <div class="col-sm-8">
                        <input name="supplierCode" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">需求发布版次 , 取自M+6月物料需求计划接口中的需求发布版次，针对与这个版次的需求进行风险反馈：</label>
                    <div class="col-sm-8">
                        <input name="releaseEdition" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">零件号 , 奇瑞零件号：</label>
                    <div class="col-sm-8">
                        <input name="materialCode" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工厂代码：</label>
                    <div class="col-sm-8">
                        <input name="plantId" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">反馈结果 , 1-异常；0-无异常：</label>
                    <div class="col-sm-8">
                        <input name="feedbackResults" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">具体风险 , 当反馈结果=1时，此字段必输 描述具体风险：</label>
                    <div class="col-sm-8">
                        <input name="ventureSpecific" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">应对措施 , 当反馈结果=1时，此字段必输 描述具体应对措施：</label>
                    <div class="col-sm-8">
                        <input name="measures" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">起始月份-格式：yyyy-MM：</label>
                    <div class="col-sm-8">
                        <input name="startMonth" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量1：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet1" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量2：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet2" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量3：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet3" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量4：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet4" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量5：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet5" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量6：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet6" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量7：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet7" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量8：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet8" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量9：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet9" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量10：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet10" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量11：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet11" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">满足数量12：</label>
                    <div class="col-sm-8">
                        <input name="quantityMeet12" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">是否推送过数据 0未推送 1已推送：</label>
                    <div class="col-sm-8">
                        <input name="hasPush" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">插入时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="insertTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">调整时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="modifyTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">插入人：</label>
                    <div class="col-sm-8">
                        <input name="insertBy" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">调整人：</label>
                    <div class="col-sm-8">
                        <input name="modifyBy" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "edi/mmrp"
        $("#form-mmrp-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-mmrp-add').serialize());
            }
        }

        $("input[name='insertTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='modifyTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>