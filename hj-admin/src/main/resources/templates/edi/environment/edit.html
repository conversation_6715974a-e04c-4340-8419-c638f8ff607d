<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改环境业务数据')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-environment-edit" th:object="${supplierProEnvironment}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商代码：</label>
                    <div class="col-sm-8">
                        <input name="supplierCode" th:field="*{supplierCode}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商名称：</label>
                    <div class="col-sm-8">
                        <input name="supplierName" th:field="*{supplierName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工厂代码：</label>
                    <div class="col-sm-8">
                        <input name="plantId" th:field="*{plantId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工厂名称：</label>
                    <div class="col-sm-8">
                        <input name="plantName" th:field="*{plantName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">车间代码：</label>
                    <div class="col-sm-8">
                        <input name="workshopId" th:field="*{workshopId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">车间名称：</label>
                    <div class="col-sm-8">
                        <input name="workshopName" th:field="*{workshopName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">产线代码：</label>
                    <div class="col-sm-8">
                        <input name="productionLineId" th:field="*{productionLineId}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">产线名称：</label>
                    <div class="col-sm-8">
                        <input name="productionLineName" th:field="*{productionLineName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">环境指标名称 , 例如：温度、湿度、洁净度等：</label>
                    <div class="col-sm-8">
                        <input name="envIndicatorName" th:field="*{envIndicatorName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">指标实测值 , ：</label>
                    <div class="col-sm-8">
                        <input name="numValue" th:field="*{numValue}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">上限值 , ：</label>
                    <div class="col-sm-8">
                        <input name="upperLimit" th:field="*{upperLimit}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">下限值 , ：</label>
                    <div class="col-sm-8">
                        <input name="lowerLimit" th:field="*{lowerLimit}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">单位 , 相应的单位名称，如度数：</label>
                    <div class="col-sm-8">
                        <input name="chineseUnit" th:field="*{chineseUnit}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">采集仪器代码 , 环境采集的仪器/工具代码：</label>
                    <div class="col-sm-8">
                        <input name="equipmentCode" th:field="*{equipmentCode}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">采集仪器名称 , 环境采集的仪器/工具名称：</label>
                    <div class="col-sm-8">
                        <input name="equipmentName" th:field="*{equipmentName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">数据采集的点位：</label>
                    <div class="col-sm-8">
                        <input name="dataCollectionPoint" th:field="*{dataCollectionPoint}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">数据采集的时间,格式(yyyy-MM-dd HH:mm:ss)：</label>
                    <div class="col-sm-8">
                        <input name="collectTime" th:field="*{collectTime}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">是否推送过数据 0未推送 1已推送：</label>
                    <div class="col-sm-8">
                        <input name="hasPush" th:field="*{hasPush}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">插入时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="insertTime" th:value="${#dates.format(supplierProEnvironment.insertTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">调整时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="modifyTime" th:value="${#dates.format(supplierProEnvironment.modifyTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">插入人：</label>
                    <div class="col-sm-8">
                        <input name="insertBy" th:field="*{insertBy}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">调整人：</label>
                    <div class="col-sm-8">
                        <input name="modifyBy" th:field="*{modifyBy}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "edi/environment";
        $("#form-environment-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-environment-edit').serialize());
            }
        }

        $("input[name='insertTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='modifyTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>