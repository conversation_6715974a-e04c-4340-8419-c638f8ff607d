<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改来料检验数据')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-stock-edit" th:object="${supplierProMaterialStock}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商代码：</label>
                    <div class="col-sm-8">
                        <input name="supplierCode" th:field="*{supplierCode}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商名称：</label>
                    <div class="col-sm-8">
                        <input name="supplierName" th:field="*{supplierName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">子件编码：</label>
                    <div class="col-sm-8">
                        <input name="supplierSubCode" th:field="*{supplierSubCode}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">子件名称：</label>
                    <div class="col-sm-8">
                        <input name="supplierSubName" th:field="*{supplierSubName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">分供方代码：</label>
                    <div class="col-sm-8">
                        <input name="subSupplierCode" th:field="*{subSupplierCode}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">分供方名称：</label>
                    <div class="col-sm-8">
                        <input name="subSupplierName" th:field="*{subSupplierName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">分供方地址 , 分供方发货地址：省市区：</label>
                    <div class="col-sm-8">
                        <input name="subSupplierAddress" th:field="*{subSupplierAddress}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">分供方子件编码：</label>
                    <div class="col-sm-8">
                        <input name="componentCode" th:field="*{componentCode}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">分供方子件名称：</label>
                    <div class="col-sm-8">
                        <input name="componentName" th:field="*{componentName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">子件批次号：</label>
                    <div class="col-sm-8">
                        <input name="subBatchNo" th:field="*{subBatchNo}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">子件批次数量：</label>
                    <div class="col-sm-8">
                        <input name="subBatchNum" th:field="*{subBatchNum}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">子件SN码：</label>
                    <div class="col-sm-8">
                        <input name="subBatchSn" th:field="*{subBatchSn}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">检验人员编号：</label>
                    <div class="col-sm-8">
                        <input name="empCode" th:field="*{empCode}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">检验人员姓名：</label>
                    <div class="col-sm-8">
                        <input name="empName" th:field="*{empName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">检测设备编号：</label>
                    <div class="col-sm-8">
                        <input name="deviceCode" th:field="*{deviceCode}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">检测设备名称：</label>
                    <div class="col-sm-8">
                        <input name="deviceName" th:field="*{deviceName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">参数名称/特性名称：</label>
                    <div class="col-sm-8">
                        <input name="featureName" th:field="*{featureName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">参数单位/特性单位：</label>
                    <div class="col-sm-8">
                        <input name="featureUnit" th:field="*{featureUnit}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">参数/特性标准值：</label>
                    <div class="col-sm-8">
                        <input name="standardValue" th:field="*{standardValue}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">参数/特性上限值：</label>
                    <div class="col-sm-8">
                        <input name="featureUpper" th:field="*{featureUpper}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">参数/特性下限值：</label>
                    <div class="col-sm-8">
                        <input name="featureLower" th:field="*{featureLower}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">参数/特性实测值：</label>
                    <div class="col-sm-8">
                        <input name="featureValue" th:field="*{featureValue}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">来料检验单号：</label>
                    <div class="col-sm-8">
                        <input name="checkNo" th:field="*{checkNo}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">来料检验结果，OK合格/NG不合格：</label>
                    <div class="col-sm-8">
                        <input name="checkResult" th:field="*{checkResult}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">检验时间,格式(yyyy-MM-dd HH:mm:ss)：</label>
                    <div class="col-sm-8">
                        <input name="checkTime" th:field="*{checkTime}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项要求频率：</label>
                    <div class="col-sm-8">
                        <input name="samplingRate" th:field="*{samplingRate}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">上下限更新时间，格式(yyyy-MM-dd HH:mm:ss)：</label>
                    <div class="col-sm-8">
                        <input name="limitUpdateTime" th:field="*{limitUpdateTime}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项描述：</label>
                    <div class="col-sm-8">
                        <input name="vendorFieldDesc" th:field="*{vendorFieldDesc}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项代码：</label>
                    <div class="col-sm-8">
                        <input name="vendorFieldCode" th:field="*{vendorFieldCode}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">库存有效日期,格式(yyyy-MM-dd HH:mm:ss)：</label>
                    <div class="col-sm-8">
                        <input name="deadLine" th:field="*{deadLine}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">是否推送过数据 0未推送 1已推送：</label>
                    <div class="col-sm-8">
                        <input name="hasPush" th:field="*{hasPush}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">插入时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="insertTime" th:value="${#dates.format(supplierProMaterialStock.insertTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">调整时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="modifyTime" th:value="${#dates.format(supplierProMaterialStock.modifyTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">插入人：</label>
                    <div class="col-sm-8">
                        <input name="insertBy" th:field="*{insertBy}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">调整人：</label>
                    <div class="col-sm-8">
                        <input name="modifyBy" th:field="*{modifyBy}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "edi/stock";
        $("#form-stock-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-stock-edit').serialize());
            }
        }

        $("input[name='insertTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='modifyTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>