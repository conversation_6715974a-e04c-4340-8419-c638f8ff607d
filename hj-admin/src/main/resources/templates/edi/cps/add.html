<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增过程控制项质量数据')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-cps-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商代码：</label>
                    <div class="col-sm-8">
                        <input name="supplierCode" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商名称：</label>
                    <div class="col-sm-8">
                        <input name="supplierName" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商总成零件号：</label>
                    <div class="col-sm-8">
                        <input name="vendorProductNo" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商总成零件名称：</label>
                    <div class="col-sm-8">
                        <input name="vendorProductName" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商总成SN码：</label>
                    <div class="col-sm-8">
                        <input name="vendorProductSn" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">供应商总成批次号：</label>
                    <div class="col-sm-8">
                        <input name="vendorProductBatch" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">奇瑞零件号：</label>
                    <div class="col-sm-8">
                        <input name="cheryProductNo" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">奇瑞零件名称：</label>
                    <div class="col-sm-8">
                        <input name="cheryProductName" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">奇瑞SN码：</label>
                    <div class="col-sm-8">
                        <input name="cheryProductSn" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">生产批次号：</label>
                    <div class="col-sm-8">
                        <input name="productBatchNo" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">生产工单号：</label>
                    <div class="col-sm-8">
                        <input name="manufactureNo" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工厂代码：</label>
                    <div class="col-sm-8">
                        <input name="plantId" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工厂名称：</label>
                    <div class="col-sm-8">
                        <input name="plantName" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">车间代码：</label>
                    <div class="col-sm-8">
                        <input name="workshopId" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">车间名称：</label>
                    <div class="col-sm-8">
                        <input name="workshopName" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">产线代码：</label>
                    <div class="col-sm-8">
                        <input name="productionLineId" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">产线名称：</label>
                    <div class="col-sm-8">
                        <input name="productionLineName" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工位代码：</label>
                    <div class="col-sm-8">
                        <input name="stationId" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工位名称：</label>
                    <div class="col-sm-8">
                        <input name="stationName" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工位人员编号：</label>
                    <div class="col-sm-8">
                        <input name="empCode" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工位人员姓名：</label>
                    <div class="col-sm-8">
                        <input name="empName" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项名称：</label>
                    <div class="col-sm-8">
                        <input name="vendorFieldName" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项代码：</label>
                    <div class="col-sm-8">
                        <input name="vendorFieldCode" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项点位：</label>
                    <div class="col-sm-8">
                        <input name="gatherSpot" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项要求频率：</label>
                    <div class="col-sm-8">
                        <input name="samplingRate" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">上下限更新时间,格式(yyyy-MM-dd HH:mm:ss)：</label>
                    <div class="col-sm-8">
                        <input name="limitUpdateTime" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项描述：</label>
                    <div class="col-sm-8">
                        <input name="vendorFieldDesc" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">载体编码：</label>
                    <div class="col-sm-8">
                        <input name="carrierCode" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">投入数量：</label>
                    <div class="col-sm-8">
                        <input name="intputQty" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">一次合格数量：</label>
                    <div class="col-sm-8">
                        <input name="fttQty" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">参数 , 是传Y，否传N：</label>
                    <div class="col-sm-8">
                        <input name="parameter" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">特性 , 是传Y，否传N：</label>
                    <div class="col-sm-8">
                        <input name="characteristic" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">CC项 , 是传Y，否传N：</label>
                    <div class="col-sm-8">
                        <input name="cc" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">SC项 , 是传Y，否传N：</label>
                    <div class="col-sm-8">
                        <input name="sc" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">SPC , 是传Y，否传N：</label>
                    <div class="col-sm-8">
                        <input name="spc" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项标准值：</label>
                    <div class="col-sm-8">
                        <input name="standardValue" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项上限：</label>
                    <div class="col-sm-8">
                        <input name="upperLimit" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项下限：</label>
                    <div class="col-sm-8">
                        <input name="lowerLimit" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项实测值：</label>
                    <div class="col-sm-8">
                        <input name="decimalValue" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控制项值的单位名称-中文：</label>
                    <div class="col-sm-8">
                        <input name="unitCn" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">控控制项单位英文：</label>
                    <div class="col-sm-8">
                        <input name="unitEn" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">检测结果：</label>
                    <div class="col-sm-8">
                        <input name="checkResult" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">在线检测(inline,offline,both) , 可选项: inline - 在生产线上进行检测. offline - 从生产线上拿下来进行检测. both - inline 和 offline 同时存在：</label>
                    <div class="col-sm-8">
                        <input name="detectionMode" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">班次(白班，晚班，中班)：</label>
                    <div class="col-sm-8">
                        <input name="workShift" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">采集时间,格式(yyyy-MM-dd HH:mm:ss)：</label>
                    <div class="col-sm-8">
                        <input name="collectTime" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">检测方式(人工,设备)：</label>
                    <div class="col-sm-8">
                        <input name="checkMode" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">检测设备编号：</label>
                    <div class="col-sm-8">
                        <input name="deviceCode" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">检测设备名称：</label>
                    <div class="col-sm-8">
                        <input name="deviceName" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">是否推送过数据 0未推送 1已推送：</label>
                    <div class="col-sm-8">
                        <input name="hasPush" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">插入时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="insertTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">调整时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="modifyTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">插入人：</label>
                    <div class="col-sm-8">
                        <input name="insertBy" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">调整人：</label>
                    <div class="col-sm-8">
                        <input name="modifyBy" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "edi/cps"
        $("#form-cps-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-cps-add').serialize());
            }
        }

        $("input[name='insertTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='modifyTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>