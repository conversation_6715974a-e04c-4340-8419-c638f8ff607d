<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('排序供货列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>车型：</label>
                                <input type="text" name="models"/>
                            </li>
                            <li>
                                <label>VIN：</label>
                                <input type="text" name="vin"/>
                            </li>
                            <li>
                                <label>产线代码：</label>
                                <input type="text" name="productionLineId"/>
                            </li>
                            <li>
                                <label>产线名称：</label>
                                <input type="text" name="productionLineName"/>
                            </li>
                            <li>
                                <label>物料编码：</label>
                                <input type="text" name="materialCode"/>
                            </li>
                            <li>
                                <label>物料描述：</label>
                                <input type="text" name="materialDescription"/>
                            </li>
                            <li>
                                <label>排序日期:时间格式 yyyy-MM-dd：</label>
                                <input type="text" name="sortDate"/>
                            </li>
                            <li>
                                <label>排序时间:时间格式 HH:mm:ss：</label>
                                <input type="text" name="sortTime"/>
                            </li>
                            <li>
                                <label>上线日期：</label>
                                <input type="text" name="onLineDate"/>
                            </li>
                            <li>
                                <label>上线时间:时间格式：20:34:12：</label>
                                <input type="text" name="onLineTime"/>
                            </li>
                            <li>
                                <label>车型类别：</label>
                                <input type="text" name="modelCategory"/>
                            </li>
                            <li>
                                <label>动力总成物料号：</label>
                                <input type="text" name="assemblyMaterialCode"/>
                            </li>
                            <li>
                                <label>发动机物料号：</label>
                                <input type="text" name="motorMaterialCode"/>
                            </li>
                            <li>
                                <label>工厂：</label>
                                <input type="text" name="plant"/>
                            </li>
                            <li>
                                <label>创建人：</label>
                                <input type="text" name="createByUser"/>
                            </li>
                            <li>
                                <label>修改人：</label>
                                <input type="text" name="updateByUser"/>
                            </li>
                            <li>
                                <label>是否删除(0:否,1是)(系统默认字段与业务数据无关)：</label>
                                <input type="text" name="isDelete"/>
                            </li>
                            <li>
                                <label>版本号(系统默认字段与业务数据无关)：</label>
                                <input type="text" name="version"/>
                            </li>
                            <li>
                                <label>插入时间：</label>
                                <input type="text" class="time-input" placeholder="请选择插入时间" name="insertTime"/>
                            </li>
                            <li>
                                <label>调整时间：</label>
                                <input type="text" class="time-input" placeholder="请选择调整时间" name="modifyTime"/>
                            </li>
                            <li>
                                <label>插入人：</label>
                                <input type="text" name="insertBy"/>
                            </li>
                            <li>
                                <label>调整人：</label>
                                <input type="text" name="modifyBy"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:cschedul:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:cschedul:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:cschedul:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:cschedul:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:cschedul:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:cschedul:remove')}]];
        var prefix = ctx + "edi/cschedul";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "排序供货",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
                    visible: false
                },
                {
                    field: 'models',
                    title: '车型'
                },
                {
                    field: 'vin',
                    title: 'VIN'
                },
                {
                    field: 'productionLineId',
                    title: '产线代码'
                },
                {
                    field: 'productionLineName',
                    title: '产线名称'
                },
                {
                    field: 'materialCode',
                    title: '物料编码'
                },
                {
                    field: 'materialDescription',
                    title: '物料描述'
                },
                {
                    field: 'sortDate',
                    title: '排序日期:时间格式 yyyy-MM-dd'
                },
                {
                    field: 'sortTime',
                    title: '排序时间:时间格式 HH:mm:ss'
                },
                {
                    field: 'onLineDate',
                    title: '上线日期'
                },
                {
                    field: 'onLineTime',
                    title: '上线时间:时间格式：20:34:12'
                },
                {
                    field: 'modelCategory',
                    title: '车型类别'
                },
                {
                    field: 'assemblyMaterialCode',
                    title: '动力总成物料号'
                },
                {
                    field: 'motorMaterialCode',
                    title: '发动机物料号'
                },
                {
                    field: 'plant',
                    title: '工厂'
                },
                {
                    field: 'createByUser',
                    title: '创建人'
                },
                {
                    field: 'updateByUser',
                    title: '修改人'
                },
                {
                    field: 'isDelete',
                    title: '是否删除(0:否,1是)(系统默认字段与业务数据无关)'
                },
                {
                    field: 'version',
                    title: '版本号(系统默认字段与业务数据无关)'
                },
                {
                    field: 'insertTime',
                    title: '插入时间'
                },
                {
                    field: 'modifyTime',
                    title: '调整时间'
                },
                {
                    field: 'insertBy',
                    title: '插入人'
                },
                {
                    field: 'modifyBy',
                    title: '调整人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>