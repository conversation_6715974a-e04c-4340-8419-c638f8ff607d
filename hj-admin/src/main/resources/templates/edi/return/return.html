<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('退货单列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>退货单号：</label>
                                <input type="text" name="returnNumber"/>
                            </li>
                            <li>
                                <label>行项目号：</label>
                                <input type="text" name="serialNumber"/>
                            </li>
                            <li>
                                <label>退货单状态：</label>
                                <input type="text" name="serialSrate"/>
                            </li>
                            <li>
                                <label>取货地：</label>
                                <input type="text" name="pickUpLocation"/>
                            </li>
                            <li>
                                <label>需求取货时间:格式：yyyy-MM-dd HH:mm:ss：</label>
                                <input type="text" name="demandPickupTime"/>
                            </li>
                            <li>
                                <label>取货道口：</label>
                                <input type="text" name="pickUpCrossings"/>
                            </li>
                            <li>
                                <label>反馈信息:供应商在LES系统反馈的信息：</label>
                                <input type="text" name="feedback"/>
                            </li>
                            <li>
                                <label>工厂:示例：1000-奇瑞汽车超一工厂：</label>
                                <input type="text" name="plant"/>
                            </li>
                            <li>
                                <label>零件号：</label>
                                <input type="text" name="materialCode"/>
                            </li>
                            <li>
                                <label>零件名称：</label>
                                <input type="text" name="materialDescription"/>
                            </li>
                            <li>
                                <label>数量 ：</label>
                                <input type="text" name="quantityDelivery"/>
                            </li>
                            <li>
                                <label>批次号：</label>
                                <input type="text" name="lotNumber"/>
                            </li>
                            <li>
                                <label>判定人：</label>
                                <input type="text" name="judge"/>
                            </li>
                            <li>
                                <label>退货原因：</label>
                                <input type="text" name="returnReason"/>
                            </li>
                            <li>
                                <label>创建人：</label>
                                <input type="text" name="createByUser"/>
                            </li>
                            <li>
                                <label>修改人：</label>
                                <input type="text" name="updateByUser"/>
                            </li>
                            <li>
                                <label>是否删除(0:否,1是)(系统默认字段与业务数据无关)：</label>
                                <input type="text" name="isDelete"/>
                            </li>
                            <li>
                                <label>版本号(系统默认字段与业务数据无关)：</label>
                                <input type="text" name="version"/>
                            </li>
                            <li>
                                <label>插入时间：</label>
                                <input type="text" class="time-input" placeholder="请选择插入时间" name="insertTime"/>
                            </li>
                            <li>
                                <label>调整时间：</label>
                                <input type="text" class="time-input" placeholder="请选择调整时间" name="modifyTime"/>
                            </li>
                            <li>
                                <label>插入人：</label>
                                <input type="text" name="insertBy"/>
                            </li>
                            <li>
                                <label>调整人：</label>
                                <input type="text" name="modifyBy"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:return:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:return:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:return:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:return:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:return:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:return:remove')}]];
        var prefix = ctx + "edi/return";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "退货单",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
                    visible: false
                },
                {
                    field: 'returnNumber',
                    title: '退货单号'
                },
                {
                    field: 'serialNumber',
                    title: '行项目号'
                },
                {
                    field: 'serialSrate',
                    title: '退货单状态'
                },
                {
                    field: 'pickUpLocation',
                    title: '取货地'
                },
                {
                    field: 'demandPickupTime',
                    title: '需求取货时间:格式：yyyy-MM-dd HH:mm:ss'
                },
                {
                    field: 'pickUpCrossings',
                    title: '取货道口'
                },
                {
                    field: 'feedback',
                    title: '反馈信息:供应商在LES系统反馈的信息'
                },
                {
                    field: 'plant',
                    title: '工厂:示例：1000-奇瑞汽车超一工厂'
                },
                {
                    field: 'materialCode',
                    title: '零件号'
                },
                {
                    field: 'materialDescription',
                    title: '零件名称'
                },
                {
                    field: 'quantityDelivery',
                    title: '数量 '
                },
                {
                    field: 'returnType',
                    title: '退货类型:0-不合格品；1-合格品'
                },
                {
                    field: 'lotNumber',
                    title: '批次号'
                },
                {
                    field: 'judge',
                    title: '判定人'
                },
                {
                    field: 'returnReason',
                    title: '退货原因'
                },
                {
                    field: 'createByUser',
                    title: '创建人'
                },
                {
                    field: 'updateByUser',
                    title: '修改人'
                },
                {
                    field: 'isDelete',
                    title: '是否删除(0:否,1是)(系统默认字段与业务数据无关)'
                },
                {
                    field: 'version',
                    title: '版本号(系统默认字段与业务数据无关)'
                },
                {
                    field: 'insertTime',
                    title: '插入时间'
                },
                {
                    field: 'modifyTime',
                    title: '调整时间'
                },
                {
                    field: 'insertBy',
                    title: '插入人'
                },
                {
                    field: 'modifyBy',
                    title: '调整人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>