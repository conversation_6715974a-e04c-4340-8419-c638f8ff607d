<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('导出选择列')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
		    <div class="col-sm-12 search-collapse">
				<form id="export-form">
					<div class="select-list">
						<ul>
							<li>
								用户姓名：<input type="text" name="userName" value=""/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			<div class="btn-group-sm" id="toolbar" role="group">
		        <i class="fa fa-info-circle" style="color: red;"></i>  勾选数据导出指定列，否则为全部   
		        <a class="btn btn-warning" onclick="exportSelected()">
		            <i class="fa fa-download"></i> 导出
		        </a>
            </div>
			<div class="col-sm-12 select-table table-striped">
				<table id="bootstrap-table"></table>
			</div>
		</div>
	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">
        var prefix = ctx + "demo/table";
        var datas = [[${@dict.getType('sys_normal_disable')}]];

        $(function() {
            var options = {
                url: prefix + "/list",
		        showSearch: false,
		        showRefresh: false,
		        showToggle: false,
		        showColumns: false,
		        clickToSelect: true,
		        rememberSelected: true,
                columns: [{
                	field: 'state',
		            checkbox: true
		        },
				{
					field : 'userId', 
					title : '用户ID'
				},
				{
					field : 'userCode', 
					title : '用户编号'
				},
				{
					field : 'userName', 
					title : '用户姓名'
				},
				{
					field : 'userPhone', 
					title : '用户手机'
				},
				{
					field : 'userEmail', 
					title : '用户邮箱'
				},
				{
				    field : 'userBalance',
				    title : '用户余额'
				},
				{
                    field: 'status',
                    title: '用户状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                    	return $.table.selectDictLabel(datas, value);
                    }
                },
		        {
		            title: '操作',
		            align: 'center',
		            formatter: function(value, row, index) {
		            	var actions = [];
		            	actions.push('<a class="btn btn-success btn-xs" href="javascript:;"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:;"><i class="fa fa-remove"></i>删除</a>');
						return actions.join('');
		            }
		        }]
            };
            $.table.init(options);
        });
        
        // 导出数据
        function exportSelected() {
        	var userIds = $.table.selectColumns("userId");
        	var dataParam = $("#export-form").serializeArray();
        	var tipMsg = "确定导出所有数据吗？";
        	if($.common.isNotEmpty(userIds)){
        		tipMsg = "确定导出勾选" + userIds.length + "条数据吗？";
        		dataParam.push({ "name": "userIds", "value": userIds });
        	}
        	$.modal.confirm(tipMsg, function() {
    			$.post(prefix + "/exportData", dataParam, function(result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
        	});
        }
    </script>
</body>
</html>