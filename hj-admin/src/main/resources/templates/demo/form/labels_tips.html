<!DOCTYPE html>
<html lang="zh">
<head>
	<th:block th:include="include :: header('标签 & 提示')" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeIn">
        <div class="row">
            <div class="col-sm-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>徽章 (Badges)</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                            <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                                <i class="fa fa-wrench"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-user">
                                <li><a href="javascript:;">选项1</a>
                                </li>
                                <li><a href="javascript:;">选项2</a>
                                </li>
                            </ul>
                            <a class="close-link">
                                <i class="fa fa-times"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <p>
                            要添加徽章，只需要在元素上添加<code>.badge</code>即可，改变徽章的颜色可使用如下class，如<code>.badge-primary</code>。
                        </p>
                        <p><span class="badge">空</span>
                        </p>
                        <p><span class="badge badge-primary">badge-primary</span>
                        </p>
                        <p><span class="badge badge-info">badge-info</span>
                        </p>
                        <p><span class="badge badge-success">badge-success</span>
                        </p>
                        <p><span class="badge badge-warning">badge-warning</span>
                        </p>
                        <p><span class="badge badge-danger">badge-danger</span>
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-sm-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>标签 (Labels)</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                            <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                                <i class="fa fa-wrench"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-user">
                                <li><a href="javascript:;">选项1</a>
                                </li>
                                <li><a href="javascript:;">选项2</a>
                                </li>
                            </ul>
                            <a class="close-link">
                                <i class="fa fa-times"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <p>
                            要添加徽章，只需要在元素上添加class<code>.label</code>即可，如果需要修改颜色，添加如下class，如<code>.label-primary</code>
                        </p>
                        <p><span class="label">空</span>
                        </p>
                        <p><span class="label label-primary">label-primary</span>
                        </p>
                        <p><span class="label label-info">label-info</span>
                        </p>
                        <p><span class="label label-success">label-success</span>
                        </p>
                        <p><span class="label label-warning">label-warning</span>
                        </p>
                        <p><span class="label label-danger">label-danger</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-sm-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>通知样式</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                            <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                                <i class="fa fa-wrench"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-user">
                                <li><a href="javascript:;">选项1</a>
                                </li>
                                <li><a href="javascript:;">选项2</a>
                                </li>
                            </ul>
                            <a class="close-link">
                                <i class="fa fa-times"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="alert alert-success">
                            RuoYi是一个很棒的后台UI框架 <a class="alert-link" href="javascript:;">了解更多</a>.
                        </div>
                        <div class="alert alert-info">
                            RuoYi是一个很棒的后台UI框架 <a class="alert-link" href="javascript:;">了解更多</a>.
                        </div>
                        <div class="alert alert-warning">
                            RuoYi是一个很棒的后台UI框架 <a class="alert-link" href="javascript:;">了解更多</a>.
                        </div>
                        <div class="alert alert-danger">
                            RuoYi是一个很棒的后台UI框架 <a class="alert-link" href="javascript:;">了解更多</a>.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>带关闭按钮的通知样式</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                            <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                                <i class="fa fa-wrench"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-user">
                                <li><a href="javascript:;">选项1</a>
                                </li>
                                <li><a href="javascript:;">选项2</a>
                                </li>
                            </ul>
                            <a class="close-link">
                                <i class="fa fa-times"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="alert alert-success alert-dismissable">
                            <button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
                            RuoYi是一个很棒的后台UI框架 <a class="alert-link" href="javascript:;">了解更多</a>.
                        </div>
                        <div class="alert alert-info alert-dismissable">
                            <button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
                            RuoYi是一个很棒的后台UI框架 <a class="alert-link" href="javascript:;">了解更多</a>.
                        </div>
                        <div class="alert alert-warning alert-dismissable">
                            <button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
                            RuoYi是一个很棒的后台UI框架 <a class="alert-link" href="javascript:;">了解更多</a>.
                        </div>
                        <div class="alert alert-danger alert-dismissable">
                            <button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
                            RuoYi是一个很棒的后台UI框架 <a class="alert-link" href="javascript:;">了解更多</a>.
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>工具提示</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                            <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                                <i class="fa fa-wrench"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-user">
                                <li><a href="javascript:;">选项1</a>
                                </li>
                                <li><a href="javascript:;">选项2</a>
                                </li>
                            </ul>
                            <a class="close-link">
                                <i class="fa fa-times"></i>
                            </a>
                        </div>
                    </div>
                    <div class="text-center">
                        <h4>工具提示示例 <small>深色背景</small></h4>
                        <div>
                            <button type="button" class="btn btn-default" data-toggle="tooltip" data-placement="left" title="这里是提示内容">左侧提示</button>
                            <button type="button" class="btn btn-default" data-toggle="tooltip" data-placement="top" title="这里是提示内容">顶部提示</button>
                            <button type="button" class="btn btn-default" data-toggle="tooltip" data-placement="bottom" title="这里是提示内容">底部提示</button>
                            <button type="button" class="btn btn-default" data-toggle="tooltip" data-placement="right" title="这里是提示内容">右侧提示</button>
                        </div>
                        <br>
                        <h4>工具提示 - 单击提示</h4>
                        <div class="text-center" >
                            <button type="button" class="btn btn-primary"  data-toggle="popover" data-placement="auto left" data-content="气泡提示">
                                气泡提示左
                            </button>
                            <button type="button" class="btn btn-primary"  data-toggle="popover" data-placement="auto top" data-content="气泡提示">
                                气泡提示顶部
                            </button>
                            <button type="button" class="btn btn-primary"  data-toggle="popover" data-placement="auto bottom" data-content="气泡提示">
                                气泡提示底部
                            </button>
                            <button type="button" class="btn btn-primary" data-toggle="popover" data-placement="auto right" data-content="气泡提示">
                                气泡提示右
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
	    $("[data-toggle='tooltip']").tooltip();
	    $("[data-toggle=popover]").popover();
    </script>
</body>
</html>
