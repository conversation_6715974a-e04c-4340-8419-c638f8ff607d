<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>登录宏景EDI系统</title>
    <meta name="description" content="宏景EDI后台管理框架">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/login.min.css" th:href="@{/css/login.min.css}" rel="stylesheet"/>
    <link href="../static/hj/css/ry-ui.css" th:href="@{/hj/css/ry-ui.css?v=4.8.1}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">label.error { position:inherit;  }</style>
    <script>
        if(window.top!==window.self){alert('未登录或登录超时。请重新登录');window.top.location=window.location};
    </script>
</head>
<body class="signin">
    <div class="signinpanel">
            <div class="col-sm-7" style="background: #fff; border-radius: 5px; text-align: center;">
                <form id="signupForm" autocomplete="off">
                    <h4 class="no-margins" style="color: #333;">登录：</h4>
                    <input type="text"     name="username" class="form-control uname"     placeholder="用户名" value=""    />
                    <input type="password" name="password" class="form-control pword"     placeholder="密码"   value="" />
					<div class="row m-t" th:if="${captchaEnabled==true}">
						<div class="col-xs-6">
						    <input type="text" name="validateCode" class="form-control code" placeholder="验证码" maxlength="5" />
						</div>
						<div class="col-xs-6" style="color: #333;">
							<a href="javascript:void(0);" title="点击更换验证码">
								<img th:src="@{/captcha/captchaImage(type=${captchaType})}" class="imgcode" width="85%"/>
							</a>
						</div>
					</div>
<!--                    <div  th:if="${isRemembered}" th:classappend="${captchaEnabled==false} ? 'm-t'" style="text-align: left">-->
<!--				        <input type="checkbox" id="rememberme" name="rememberme"> <label for="rememberme" style="color: #333;">记住我</label>-->
<!--				    </div>-->
                    <button class="btn btn-success btn-block" id="btnSubmit" data-loading="正在验证登录，请稍候...">登录</button>
                </form>
            </div>
    </div>
<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; var captchaEnabled = [[${captchaEnabled}]];</script>
<!--[if lte IE 8]><script>window.location.href=ctx+'html/ie.html';</script><![endif]-->
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/hj/js/ry-ui.js" th:src="@{/hj/js/ry-ui.js?v=4.8.1}"></script>
<script src="../static/hj/login.js" th:src="@{/hj/login.js}"></script>
</body>
</html>
