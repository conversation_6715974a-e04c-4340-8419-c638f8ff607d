package com.hj.admin.domain.push;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 工艺装备对象 supplier_pro_process_equipment
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierProProcessEquipment extends BaseDomain {

    /**
     * id
     */
    private Long id;


    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 工艺装备分类(1,2,3,4)
     */
    @Excel(name = "工艺装备分类(1,2,3,4)")
    private String deviceType;

    /**
     * 工艺装备代码
     */
    @Excel(name = "工艺装备代码")
    private String deviceId;

    /**
     * 工艺装备名称
     */
    @Excel(name = "工艺装备名称")
    private String deviceName;

    /**
     * 生产厂家
     */
    @Excel(name = "生产厂家")
    private String manufacturer;

    /**
     * 工艺装备型号
     */
    @Excel(name = "工艺装备型号")
    private String modelNumber;

    /**
     * 工艺装备制造日期
     */
    @Excel(name = "工艺装备制造日期")
    private String productionDate;

    /**
     * 主要材质
     */
    @Excel(name = "主要材质")
    private String material;

    /**
     * 当前存放地点
     */
    @Excel(name = "当前存放地点")
    private String currentLocation;

    /**
     * 工艺装备状态(报废,正常)
     */
    @Excel(name = "工艺装备状态(报废,正常)")
    private String deviceStatus;

    /**
     * 设计寿命单位
     */
    @Excel(name = "设计寿命单位")
    private String designLifeUnits;

    /**
     * 设计寿命
     */
    @Excel(name = "设计寿命")
    private Long designLifeValue;

    /**
     * 当前剩余寿命
     */
    @Excel(name = "当前剩余寿命")
    private Long currentUsageCount;

    /**
     * 每月有效工作天数
     */
    @Excel(name = "每月有效工作天数")
    private Long effectiveDays;

    /**
     * 每天最大加工工时
     */
    @Excel(name = "每天最大加工工时")
    private Long maxProcessHours;

    /**
     * 每天常规加工工时
     */
    @Excel(name = "每天常规加工工时")
    private Long regularProcessHours;

    /**
     * 投产日期
     */
    @Excel(name = "投产日期")
    private String deviceStartDate;

    /**
     * 报废停产日期
     */
    @Excel(name = "报废停产日期")
    private String deviceEndDate;

    /**
     * 单台设备投资金额
     */
    @Excel(name = "单台设备投资金额")
    private Long machineCosts;

    /**
     * 设备购买周期
     */
    @Excel(name = "设备购买周期")
    private Long machinePurchasePeriod;

    /**
     * 设备类型(1,2,3,4,5,6,7,8,9)
     */
    @Excel(name = "设备类型(1,2,3,4,5,6,7,8,9)")
    private String machineType;

    /**
     * 单位小时产出
     */
    @Excel(name = "单位小时产出")
    private Long unitperHour;

    /**
     * 穴腔数量
     */
    @Excel(name = "穴腔数量")
    private Long cavityCount;

    /**
     * 模具尺寸规格
     */
    @Excel(name = "模具尺寸规格")
    private String moldSize;

    /**
     * 模具复制模费用
     */
    @Excel(name = "模具复制模费用")
    private Long copyMoldCosts;

    /**
     * 模具大修次数
     */
    @Excel(name = "模具大修次数")
    private Long overhaulCount;

    /**
     * 检具最近校准日期
     */
    @Excel(name = "检具最近校准日期")
    private String calibrationDate;

    /**
     * 检具校准到期天数
     */
    @Excel(name = "检具校准到期天数")
    private String calibrationDueDays;

    /**
     * 检具检测单位
     */
    @Excel(name = "检具检测单位")
    private String unitType;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
    private Integer hasPush;


}
