package com.hj.admin.domain.pull;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 整车月度生产计划对象 supplier_pro_planing
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierProPlaning extends BaseDomain {

    /**
     * 主键：拉取数据供应商根据主键id判断是否存在，在新增和更新
     */
    private Long id;

    /**
     * 需求发布版次:唯一版次ID
     */
    @Excel(name = "需求发布版次:唯一版次ID ")
    private String releaseEdition;

    /**
     * 车型
     */
    @Excel(name = "车型")
    private String models;

    /**
     * 销售单位
     */
    @Excel(name = "销售单位")
    private String salseDepartment;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * 动力总成
     */
    @Excel(name = "动力总成")
    private String assembly;

    /**
     * 版型
     */
    @Excel(name = "版型")
    private String pattern;

    /**
     * 内饰
     */
    @Excel(name = "内饰")
    private String omterior;

    /**
     * 物料号
     */
    @Excel(name = "物料号")
    private String materialCode;

    /**
     * 起始月份-格式：yyyy-MM
     */
    @Excel(name = "起始月份-格式：yyyy-MM")
    private String startMonth;

    /**
     * 数量1
     */
    @Excel(name = "数量1")
    private Long quantity1;

    /**
     * 数量2
     */
    @Excel(name = "数量2")
    private Long quantity2;

    /**
     * 数量3
     */
    @Excel(name = "数量3")
    private Long quantity3;

    /**
     * 数量4
     */
    @Excel(name = "数量4")
    private Long quantity4;

    /**
     * 数量5
     */
    @Excel(name = "数量5")
    private Long quantity5;

    /**
     * 数量6
     */
    @Excel(name = "数量6")
    private Long quantity6;

    /**
     * 工厂
     */
    @Excel(name = "工厂")
    private String plant;

    /**
     * 二级车型代码-细
     */
    @Excel(name = "二级车型代码-细")
    private String models2;

    /**
     * 排量
     */
    @Excel(name = "排量")
    private String displacement;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createByUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String updateByUser;

    /**
     * 是否删除(0:否,1是)(系统默认字段与业务数据无关)
     */
    @Excel(name = "是否删除(0:否,1是)(系统默认字段与业务数据无关)")
    private Long isDelete;

    /**
     * 版本号(系统默认字段与业务数据无关)
     */
    @Excel(name = "版本号(系统默认字段与业务数据无关)")
    private Long version;


}
