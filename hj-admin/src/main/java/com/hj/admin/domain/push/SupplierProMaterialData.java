package com.hj.admin.domain.push;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 物料主数据对象 supplier_pro_material_data
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierProMaterialData extends BaseDomain {

    /**
     * id
     */
    private Long id;


    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 供应商总成零件号
     */
    @Excel(name = "供应商总成零件号")
    private String vendorProductNo;

    /**
     * 供应商总成零件名称
     */
    @Excel(name = "供应商总成零件名称")
    private String vendorProductName;

    /**
     * 类型(成品,半成品,原材料)
     */
    @Excel(name = "类型(成品,半成品,原材料)")
    private String type;

    /**
     * 供应商零件版本号
     */
    @Excel(name = "供应商零件版本号")
    private String vendorHardwareRevision;

    /**
     * 奇瑞零件号
     */
    @Excel(name = "奇瑞零件号")
    private String cheryProductNo;

    /**
     * 奇瑞零件名称
     */
    @Excel(name = "奇瑞零件名称")
    private String cheryProductName;

    /**
     * 奇瑞硬件版本号
     */
    @Excel(name = "奇瑞硬件版本号")
    private String oemHardwareRevision;

    /**
     * 奇瑞软件版本号
     */
    @Excel(name = "奇瑞软件版本号")
    private String oemSoftwareRevision;

    /**
     * 车型
     */
    @Excel(name = "车型")
    private String oemModel;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    private String oemProjectName;

    /**
     * 是否SOP(Y/N)
     */
    @Excel(name = "是否SOP(Y/N)")
    private String launched;

    /**
     * 数据同步执行时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "数据同步执行时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String dateTime;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 芯片采购类型(AVAP,CS,CMcontro)
     */
    @Excel(name = "芯片采购类型(AVAP,CS,CMcontro)")
    private String procurementType;

    /**
     * 芯片MPN标识码
     */
    @Excel(name = "芯片MPN标识码")
    private String mpnCode;

    /**
     * 芯片MPN标识名称
     */
    @Excel(name = "芯片MPN标识名称")
    private String mpnName;

    /**
     * 物料有效期（天）
     */
    @Excel(name = "物料有效期", readConverterExp = "天=")
    private String validDays;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
    private Integer hasPush;


}
