package com.hj.admin.domain.pull;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 采购订单对象 supplier_po
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierPo extends BaseDomain {

    /**
     * 主键：拉取数据供应商根据主键id判断是否存在，在新增和更新
     */
    private Long id;

    /**
     * 采购订单号
     */
    @Excel(name = "采购订单号")
    private String purchaseOrder;

    /**
     * 行项目号
     */
    @Excel(name = "行项目号")
    private String serialNumber;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 凭证日期-格式：yyyy-MM-dd
     */
    @Excel(name = "凭证日期-格式：yyyy-MM-dd")
    private String voucherDate;

    /**
     * 需方联系人
     */
    @Excel(name = "需方联系人")
    private String purchaser;

    /**
     * 供方联系人
     */
    @Excel(name = "供方联系人")
    private String supplier;

    /**
     * 物料编码
     */
    @Excel(name = "物料编码")
    private String materialCode;

    /**
     * 物料描述
     */
    @Excel(name = "物料描述")
    private String materialDescription;

    /**
     * 需求数量
     */
    @Excel(name = "需求数量")
    private Long quantityDemand;

    /**
     * 物料单位
     */
    @Excel(name = "物料单位")
    private String materialUnit;

    /**
     * 交货日期-格式：yyyy-MM-dd
     */
    @Excel(name = "交货日期-格式：yyyy-MM-dd")
    private String deliveryDate;

    /**
     * 交货地点
     */
    @Excel(name = "交货地点")
    private String deliveryPlace;

    /**
     * 到货数量
     */
    @Excel(name = "到货数量")
    private Long quantityDelivery;

    /**
     * 备注:含批次号信息
     */
    @Excel(name = "备注:含批次号信息")
    private String note;

    /**
     * 项目类别文本
     */
    @Excel(name = "项目类别文本")
    private String itemType;

    /**
     * 国际贸易条件
     */
    @Excel(name = "国际贸易条件")
    private String tradeTerms;

    /**
     * 出口国家
     */
    @Excel(name = "出口国家")
    private String country;

    /**
     * 批次
     */
    @Excel(name = "批次")
    private String batch;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createByUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String updateByUser;

    /**
     * 是否删除(0:否,1是)(系统默认字段与业务数据无关)
     */
    @Excel(name = "是否删除(0:否,1是)(系统默认字段与业务数据无关)")
    private Long isDelete;

    /**
     * 版本号(系统默认字段与业务数据无关)
     */
    @Excel(name = "版本号(系统默认字段与业务数据无关)")
    private Long version;


}
