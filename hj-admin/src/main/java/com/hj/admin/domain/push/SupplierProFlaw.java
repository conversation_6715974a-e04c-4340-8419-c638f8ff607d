package com.hj.admin.domain.push;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 缺陷业务数据对象 supplier_pro_flaw
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierProFlaw extends BaseDomain {

    /**
     * id
     */
    private Long id;


    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 车间代码
     */
    @Excel(name = "车间代码")
    private String workshopId;

    /**
     * 车间名称
     */
    @Excel(name = "车间名称")
    private String workshopName;

    /**
     * 产线代码
     */
    @Excel(name = "产线代码")
    private String productionLineId;

    /**
     * 产线名称
     */
    @Excel(name = "产线名称")
    private String productionLineName;

    /**
     * 工位代码
     */
    @Excel(name = "工位代码")
    private String stationId;

    /**
     * 工位名称
     */
    @Excel(name = "工位名称")
    private String stationName;

    /**
     * 缺陷代码
     */
    @Excel(name = "缺陷代码")
    private String defectsCode;

    /**
     * 缺陷名称
     */
    @Excel(name = "缺陷名称")
    private String defectsName;

    /**
     * 缺陷分类(外观,尺寸,材料,功能,性能,其他)
     */
    @Excel(name = "缺陷分类(外观,尺寸,材料,功能,性能,其他)")
    private String classOfName;

    /**
     * 供应商总成零件号
     */
    @Excel(name = "供应商总成零件号")
    private String vendorProductNo;

    /**
     * 供应商总成零件名称
     */
    @Excel(name = "供应商总成零件名称")
    private String vendorProductName;

    /**
     * 供应商总成批次号
     */
    @Excel(name = "供应商总成批次号")
    private String vendorProductBatch;

    /**
     * 供应商总成SN码
     */
    @Excel(name = "供应商总成SN码")
    private String vendorProductSn;

    /**
     * 奇瑞零件号
     */
    @Excel(name = "奇瑞零件号")
    private String cheryProductNo;

    /**
     * 奇瑞零件名称
     */
    @Excel(name = "奇瑞零件名称")
    private String cheryProductName;

    /**
     * 奇瑞SN码
     */
    @Excel(name = "奇瑞SN码")
    private String cheryProductSn;

    /**
     * 生产批次号
     */
    @Excel(name = "生产批次号")
    private String productBatchNo;

    /**
     * 生产工单号
     */
    @Excel(name = "生产工单号")
    private String manufactureNo;

    /**
     * 班次(白班，晚班，中班)
     */
    @Excel(name = "班次(白班，晚班，中班)")
    private String workShift;

    /**
     * 缺陷件数
     */
    @Excel(name = "缺陷件数")
    private Long numberofdefect;

    /**
     * 缺陷描述
     */
    @Excel(name = "缺陷描述")
    private String defectsDesc;

    /**
     * 缺陷等级 , （1.严重、2.一般、3.轻微 ）
     */
    @Excel(name = "缺陷等级 , ", readConverterExp = "1=.严重、2.一般、3.轻微")
    private String defectsLevel;

    /**
     * 缺陷录入时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "缺陷录入时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String statisticalTime;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
    private Integer hasPush;


}
