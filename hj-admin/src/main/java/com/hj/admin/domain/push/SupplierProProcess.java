package com.hj.admin.domain.push;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 工艺对象 supplier_pro_process
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierProProcess extends BaseDomain {

    /**
     * id
     */
    private Long id;


    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 奇瑞零件号
     */
    @Excel(name = "奇瑞零件号")
    private String cheryProductNo;

    /**
     * 奇瑞零件名称
     */
    @Excel(name = "奇瑞零件名称")
    private String cheryProductName;

    /**
     * 供应商总成零件号
     */
    @Excel(name = "供应商总成零件号")
    private String vendorProductNo;

    /**
     * 供应商总成零件名称
     */
    @Excel(name = "供应商总成零件名称")
    private String vendorProductName;

    /**
     * 工艺编码
     */
    @Excel(name = "工艺编码")
    private String techCode;

    /**
     * 工艺名称
     */
    @Excel(name = "工艺名称")
    private String techName;

    /**
     * 工艺版本
     */
    @Excel(name = "工艺版本")
    private String techVersion;

    /**
     * 是否启用
     */
    @Excel(name = "是否启用")
    private String isEnabled;

    /**
     * 最大加工能力
     */
    @Excel(name = "最大加工能力")
    private String maxProcessingCapacity;

    /**
     * 承诺比例
     */
    @Excel(name = "承诺比例")
    private Long promiseRatio;

    /**
     * 按滚动预测需求的供货周期
     */
    @Excel(name = "按滚动预测需求的供货周期")
    private Long makeSamePeriod;

    private List<SupplierProProcessChild> childList;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
    private Integer hasPush;


}
