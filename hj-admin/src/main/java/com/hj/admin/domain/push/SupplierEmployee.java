package com.hj.admin.domain.push;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 人员资质信息对象 supplier_employee
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierEmployee extends BaseDomain {

    /**
     * id
     */
    private Long id;

    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 车间代码
     */
    @Excel(name = "车间代码")
    private String workshopId;

    /**
     * 车间名称
     */
    @Excel(name = "车间名称")
    private String workshopName;

    /**
     * 产线代码
     */
    @Excel(name = "产线代码")
    private String productionLineId;

    /**
     * 产线名称
     */
    @Excel(name = "产线名称")
    private String productionLineName;

    /**
     * 工位代码
     */
    @Excel(name = "工位代码")
    private String stationId;

    /**
     * 工位名称
     */
    @Excel(name = "工位名称")
    private String stationName;

    /**
     * 工位人员账号
     */
    @Excel(name = "工位人员账号")
    private String operatorId;

    /**
     * 工位人员姓名
     */
    @Excel(name = "工位人员姓名")
    private String operatorName;

    /**
     * 是否有资质(Y,N)
     */
    @Excel(name = "是否有资质(Y,N)")
    private String haveQuantity;

    /**
     * 供应商修改时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "供应商修改时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String dataUpdateTime;

    /**
     * 岗位代码
     */
    @Excel(name = "岗位代码")
    private String positionId;

    /**
     * 岗位名称
     */
    @Excel(name = "岗位名称")
    private String positionName;

    /**
     * 资质等级(Level_4，Level_3，Level_2, Level_1)
     */
    @Excel(name = "资质等级(Level_4，Level_3，Level_2, Level_1)")
    private String qualificationLevel;

    /**
     * 资质获取时间
     */
    @Excel(name = "资质获取时间")
    private String checkInTime;

    /**
     * 资质失去时间
     */
    @Excel(name = "资质失去时间")
    private String checkOutTime;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
    private Integer hasPush;


}
