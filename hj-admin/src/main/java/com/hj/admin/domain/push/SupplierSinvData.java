package com.hj.admin.domain.push;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商共享库存对象 supplier_sinv_data
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierSinvData extends BaseDomain {

    /**
     * id
     */
    private Long id;


    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 零件号
     */
    @Excel(name = "零件号")
    private String materialCode;

    /**
     * 零件名称
     */
    @Excel(name = "零件名称")
    private String materialDescription;

    /**
     * 物料类型(成品,半成品,原材料)
     */
    @Excel(name = "物料类型(成品,半成品,原材料)")
    private String materialType;

    /**
     * 当前库存数量
     */
    @Excel(name = "当前库存数量")
    private Long quantityCurrent;

    /**
     * 原材料在途数量
     */
    @Excel(name = "原材料在途数量")
    private Long quantityPlan;

    /**
     * 库存状态(生产件,呆滞件,备件,KD件)
     */
    @Excel(name = "库存状态(生产件,呆滞件,备件,KD件)")
    private String inventoryStatus;

    /**
     * 安全库存
     */
    @Excel(name = "安全库存")
    private Long safetyStock;

    /**
     * 生产/采购周期:成品即半成品为生产周期（天），原材料为采购周期（天）
     */
    @Excel(name = "生产/采购周期:成品即半成品为生产周期", readConverterExp = "天=")
    private String productionCycle;

    /**
     * 库存更新时间-格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "库存更新时间-格式：yyyy-MM-dd HH:mm:ss")
    private String dataUpdateTime;

    /**
     * 批次
     */
    @Excel(name = "批次")
    private String supplierBatch;

    /**
     * 有效期截止日期
     */
    @Excel(name = "有效期截止日期")
    private String supplieryxqDate;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
    private Integer hasPush;


}
