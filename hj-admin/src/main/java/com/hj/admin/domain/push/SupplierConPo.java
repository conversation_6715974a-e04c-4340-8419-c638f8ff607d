package com.hj.admin.domain.push;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 采购订单风险确认对象 supplier_con_po
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierConPo extends BaseDomain {

    /**
     * id
     */
    private Long id;

    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 采购订单号
     */
    @Excel(name = "采购订单号")
    private String purchaseOrder;

    /**
     * 行项目号
     */
    @Excel(name = "行项目号")
    private String serialNumber;

    /**
     * 满足数量
     */
    @Excel(name = "满足数量")
    private String quantityMeet;

    /**
     * 反馈结果 , 1-异常；0-无异常（匹配峰值需求缺口，如果可满足峰值，即选择无异常）
     */
    @Excel(name = "反馈结果 , 1-异常；0-无异常", readConverterExp = "匹=配峰值需求缺口，如果可满足峰值，即选择无异常")
    private String feedbackResults;

    /**
     * 风险类型 , 当反馈结果=1时，此字段必输 1. 生产节拍不足 2. 人员不足 3. 原材料不足 4. 设备异常 5. 其他
     */
    @Excel(name = "风险类型 , 当反馈结果=1时，此字段必输 1. 生产节拍不足 2. 人员不足 3. 原材料不足 4. 设备异常 5. 其他")
    private String ventureType;

    /**
     * 具体风险 , 当反馈结果=1时，此字段必输 描述具体风险
     */
    @Excel(name = "具体风险 , 当反馈结果=1时，此字段必输 描述具体风险")
    private String ventureSpecific;

    /**
     * 应对措施 , 当反馈结果=1时，此字段必输 描述具体应对措施
     */
    @Excel(name = "应对措施 , 当反馈结果=1时，此字段必输 描述具体应对措施")
    private String measures;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
    private Integer hasPush;


}
