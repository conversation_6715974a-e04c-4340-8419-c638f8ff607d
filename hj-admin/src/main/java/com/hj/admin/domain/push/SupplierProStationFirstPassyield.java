package com.hj.admin.domain.push;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 工位一次合格率对象 supplier_pro_station_first_passyield
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierProStationFirstPassyield extends BaseDomain {

    /**
     * id
     */
    private Long id;


    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 车间代码
     */
    @Excel(name = "车间代码")
    private String workshopId;

    /**
     * 车间名称
     */
    @Excel(name = "车间名称")
    private String workshopName;

    /**
     * 产线代码
     */
    @Excel(name = "产线代码")
    private String productionLineId;

    /**
     * 产线名称
     */
    @Excel(name = "产线名称")
    private String productionLineName;

    /**
     * 工位代码
     */
    @Excel(name = "工位代码")
    private String stationId;

    /**
     * 工位名称
     */
    @Excel(name = "工位名称")
    private String stationName;

    /**
     * 奇瑞零件号
     */
    @Excel(name = "奇瑞零件号")
    private String cheryProductNo;

    /**
     * 奇瑞零件名称
     */
    @Excel(name = "奇瑞零件名称")
    private String cheryProductName;

    /**
     * 供应商总成零件号
     */
    @Excel(name = "供应商总成零件号")
    private String vendorProductNo;

    /**
     * 供应商总成零件名称
     */
    @Excel(name = "供应商总成零件名称")
    private String vendorProductName;

    /**
     * 生产批次号
     */
    @Excel(name = "生产批次号")
    private String productBatchNo;

    /**
     * 生产工单号
     */
    @Excel(name = "生产工单号")
    private String manufactureNo;

    /**
     * 批次计划数量
     */
    @Excel(name = "批次计划数量")
    private Long workOrderNumber;

    /**
     * 不合格数
     */
    @Excel(name = "不合格数")
    private Long defectiveNumber;

    /**
     * 合格数
     */
    @Excel(name = "合格数")
    private Long acceptableNumber;

    /**
     * 一次合格率实际值
     */
    @Excel(name = "一次合格率实际值")
    private Long oncePassRateRealValue;

    /**
     * 一次合格率目标值
     */
    @Excel(name = "一次合格率目标值")
    private Long oncePassRateTagValue;

    /**
     * 班次 , 班次如何区分需备注(白班，晚班，中班)
     */
    @Excel(name = "班次 , 班次如何区分需备注(白班，晚班，中班)")
    private String workShift;

    /**
     * 生产日期,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "生产日期,格式(yyyy-MM-dd HH:mm:ss)")
    private String statisticalTime;

    /**
     * 值统计时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "值统计时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String dateTime;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
    private Integer hasPush;


}
