package com.hj.admin.domain.pull;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 奇瑞RDC共享库存对象 supplier_inv_data
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierInvData extends BaseDomain {

    /**
     * 主键：拉取数据供应商根据主键id判断是否存在，在新增和更新
     */
    private Long id;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 零件号
     */
    @Excel(name = "零件号")
    private String materialCode;

    /**
     * 零件描述
     */
    @Excel(name = "零件描述")
    private String materialDescription;

    /**
     * 当前库存数量
     */
    @Excel(name = "当前库存数量")
    private Long quantityCurrent;

    /**
     * 库存状态
     */
    @Excel(name = "库存状态")
    private String stockState;

    /**
     * 更新时间:格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "更新时间:格式：yyyy-MM-dd HH:mm:ss")
    private String dataUpdateTime;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createByUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String updateByUser;

    /**
     * 是否删除(0:否,1是)(系统默认字段与业务数据无关)
     */
    @Excel(name = "是否删除(0:否,1是)(系统默认字段与业务数据无关)")
    private Long isDelete;

    /**
     * 版本号(系统默认字段与业务数据无关)
     */
    @Excel(name = "版本号(系统默认字段与业务数据无关)")
    private Long version;


}
