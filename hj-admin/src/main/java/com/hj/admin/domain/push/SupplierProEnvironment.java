package com.hj.admin.domain.push;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 环境业务数据对象 supplier_pro_environment
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierProEnvironment extends BaseDomain {

    /**
     * id
     */
    private Long id;


    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 车间代码
     */
    @Excel(name = "车间代码")
    private String workshopId;

    /**
     * 车间名称
     */
    @Excel(name = "车间名称")
    private String workshopName;

    /**
     * 产线代码
     */
    @Excel(name = "产线代码")
    private String productionLineId;

    /**
     * 产线名称
     */
    @Excel(name = "产线名称")
    private String productionLineName;

    /**
     * 环境指标名称 , 例如：温度、湿度、洁净度等
     */
    @Excel(name = "环境指标名称 , 例如：温度、湿度、洁净度等")
    private String envIndicatorName;

    /**
     * 指标实测值 , （最大支持11位整数+5位小数）
     */
    @Excel(name = "指标实测值 , ", readConverterExp = "最=大支持11位整数+5位小数")
    private Long numValue;

    /**
     * 上限值 , （最大支持11位整数+5位小数）
     */
    @Excel(name = "上限值 , ", readConverterExp = "最=大支持11位整数+5位小数")
    private Long upperLimit;

    /**
     * 下限值 , （最大支持11位整数+5位小数）
     */
    @Excel(name = "下限值 , ", readConverterExp = "最=大支持11位整数+5位小数")
    private Long lowerLimit;

    /**
     * 单位 , 相应的单位名称，如度数
     */
    @Excel(name = "单位 , 相应的单位名称，如度数")
    private String chineseUnit;

    /**
     * 采集仪器代码 , 环境采集的仪器/工具代码
     */
    @Excel(name = "采集仪器代码 , 环境采集的仪器/工具代码")
    private String equipmentCode;

    /**
     * 采集仪器名称 , 环境采集的仪器/工具名称
     */
    @Excel(name = "采集仪器名称 , 环境采集的仪器/工具名称")
    private String equipmentName;

    /**
     * 数据采集的点位
     */
    @Excel(name = "数据采集的点位")
    private String dataCollectionPoint;

    /**
     * 数据采集的时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "数据采集的时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String collectTime;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
    private Integer hasPush;


}
