package com.hj.admin.domain.pull;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 日物料需求计划对象 supplier_mrp_date
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierMrpDate extends BaseDomain {

    /**
     * 主键：拉取数据供应商根据主键id判断是否存在，在新增和更新
     */
    private Long id;

    /**
     * 需求发布版次:用于日物料需求计划风险确认接口反馈数据的ID
     */
    @Excel(name = "需求发布版次:用于日物料需求计划风险确认接口反馈数据的ID")
    private String releaseEdition;

    /**
     * 零件号:奇瑞零件号
     */
    @Excel(name = "零件号:奇瑞零件号")
    private String materialCode;

    /**
     * 零件名称
     */
    @Excel(name = "零件名称 ")
    private String materialDescription;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 起始日期-格式：yyyy-MM-dd
     */
    @Excel(name = "起始日期-格式：yyyy-MM-dd")
    private String startDate;

    /**
     * 需求数量1
     */
    @Excel(name = "需求数量1")
    private Long quantityDemand1;

    /**
     * 需求数量2
     */
    @Excel(name = "需求数量2")
    private Long quantityDemand2;

    /**
     * 需求数量3
     */
    @Excel(name = "需求数量3")
    private Long quantityDemand3;

    /**
     * 需求数量4
     */
    @Excel(name = "需求数量4")
    private Long quantityDemand4;

    /**
     * 需求数量5
     */
    @Excel(name = "需求数量5")
    private Long quantityDemand5;

    /**
     * 需求数量6
     */
    @Excel(name = "需求数量6")
    private Long quantityDemand6;

    /**
     * 需求数量7
     */
    @Excel(name = "需求数量7")
    private Long quantityDemand7;

    /**
     * 需求数量8
     */
    @Excel(name = "需求数量8")
    private Long quantityDemand8;

    /**
     * 需求数量9
     */
    @Excel(name = "需求数量9")
    private Long quantityDemand9;

    /**
     * 需求数量10
     */
    @Excel(name = "需求数量10")
    private Long quantityDemand10;

    /**
     * 需求数量11
     */
    @Excel(name = "需求数量11")
    private Long quantityDemand11;

    /**
     * 需求数量12
     */
    @Excel(name = "需求数量12")
    private Long quantityDemand12;

    /**
     * 需求数量13
     */
    @Excel(name = "需求数量13")
    private Long quantityDemand13;

    /**
     * 需求数量14
     */
    @Excel(name = "需求数量14")
    private Long quantityDemand14;

    /**
     * 需求数量15
     */
    @Excel(name = "需求数量15")
    private Long quantityDemand15;

    /**
     * 需求数量16
     */
    @Excel(name = "需求数量16")
    private Long quantityDemand16;

    /**
     * 需求数量17
     */
    @Excel(name = "需求数量17")
    private Long quantityDemand17;

    /**
     * 需求数量18
     */
    @Excel(name = "需求数量18")
    private Long quantityDemand18;

    /**
     * 需求数量19
     */
    @Excel(name = "需求数量19")
    private Long quantityDemand19;

    /**
     * 需求数量20
     */
    @Excel(name = "需求数量20")
    private Long quantityDemand20;

    /**
     * 需求数量21
     */
    @Excel(name = "需求数量21")
    private Long quantityDemand21;

    /**
     * 需求数量22
     */
    @Excel(name = "需求数量22")
    private Long quantityDemand22;

    /**
     * 需求数量23
     */
    @Excel(name = "需求数量23")
    private Long quantityDemand23;

    /**
     * 需求数量24
     */
    @Excel(name = "需求数量24")
    private Long quantityDemand24;

    /**
     * 需求数量25
     */
    @Excel(name = "需求数量25")
    private Long quantityDemand25;

    /**
     * 需求数量26
     */
    @Excel(name = "需求数量26")
    private Long quantityDemand26;

    /**
     * 需求数量27
     */
    @Excel(name = "需求数量27")
    private Long quantityDemand27;

    /**
     * 需求数量28
     */
    @Excel(name = "需求数量28")
    private Long quantityDemand28;

    /**
     * 需求数量29
     */
    @Excel(name = "需求数量29")
    private Long quantityDemand29;

    /**
     * 需求数量30
     */
    @Excel(name = "需求数量30")
    private Long quantityDemand30;

    /**
     * 需求数量31
     */
    @Excel(name = "需求数量31")
    private Long quantityDemand31;

    /**
     * 当文件夹数据发生变更时(更新需求=1/否则=0)
     */
    @Excel(name = "当文件夹数据发生变更时(更新需求=1/否则=0)")
    private String isUpdate;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createByUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String updateByUser;

    /**
     * 是否删除(0:否,1是)(系统默认字段与业务数据无关)
     */
    @Excel(name = "是否删除(0:否,1是)(系统默认字段与业务数据无关)")
    private Long isDelete;

    /**
     * 版本号(系统默认字段与业务数据无关)
     */
    @Excel(name = "版本号(系统默认字段与业务数据无关)")
    private Long version;


}
