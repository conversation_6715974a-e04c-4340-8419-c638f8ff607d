package com.hj.admin.domain.pull;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 过焊装未过总装对象 supplier_pro_hschedul
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierProHschedul extends BaseDomain {

    /**
     * 主键：拉取数据供应商根据主键id判断是否存在，在新增和更新
     */
    private Long id;

    /**
     * 车型
     */
    @Excel(name = "车型")
    private String models;

    /**
     * VIN
     */
    @Excel(name = "VIN")
    private String vin;

    /**
     * 产线代码
     */
    @Excel(name = "产线代码")
    private String productionLineId;

    /**
     * 产线名称
     */
    @Excel(name = "产线名称")
    private String productionLineName;

    /**
     * 物料编码
     */
    @Excel(name = "物料编码")
    private String materialCode;

    /**
     * 物料描述
     */
    @Excel(name = "物料描述")
    private String materialDescription;

    /**
     * 生产备注(报工类型)
     */
    @Excel(name = "生产备注(报工类型)")
    private String productionType;

    /**
     * 上线日期时间-格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "上线日期时间-格式：yyyy-MM-dd HH:mm:ss")
    private String onLineTime;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createByUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String updateByUser;

    /**
     * 是否删除(0:否,1是)(系统默认字段与业务数据无关)
     */
    @Excel(name = "是否删除(0:否,1是)(系统默认字段与业务数据无关)")
    private Long isDelete;

    /**
     * 版本号(系统默认字段与业务数据无关)
     */
    @Excel(name = "版本号(系统默认字段与业务数据无关)")
    private Long version;


}
