package com.hj.admin.domain.push;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商基础信息对象 supplier_info
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierInfo extends BaseDomain {

    /**
     * id
     */
    private Long id;

    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 车间代码
     */
    @Excel(name = "车间代码")
    private String workshopId;

    /**
     * 车间名称
     */
    @Excel(name = "车间名称")
    private String workshopName;

    /**
     * 产线代码
     */
    @Excel(name = "产线代码")
    private String productionLineId;

    /**
     * 产线名称
     */
    @Excel(name = "产线名称")
    private String productionLineName;

    /**
     * 工位代码
     */
    @Excel(name = "工位代码")
    private String stationId;

    /**
     * 工位名称
     */
    @Excel(name = "工位名称")
    private String stationName;

    /**
     * 是否关键工位（Y/N）
     */
    @Excel(name = "是否关键工位", readConverterExp = "Y=/N")
    private String keyStation;

    /**
     * 供应商修改时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "供应商修改时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String dataUpdateTime;

    /**
     * 产线顺序
     */
    @Excel(name = "产线顺序")
    private Long productionLineOrder;

    /**
     * 工位顺序
     */
    @Excel(name = "工位顺序")
    private Long stationOrder;

    /**
     * 供应商总成零件号
     */
    @Excel(name = "供应商总成零件号")
    private String vendorProductNo;

    /**
     * 供应商总成零件名称
     */
    @Excel(name = "供应商总成零件名称")
    private String vendorProductName;

    /**
     * 奇瑞零件号
     */
    @Excel(name = "奇瑞零件号")
    private String cheryProductNo;

    /**
     * 奇瑞零件名称
     */
    @Excel(name = "奇瑞零件名称")
    private String cheryProductName;

    /**
     * 是否已推送 0未推送 1已推送
     */
    @Excel(name = "是否已推送 0未推送 1已推送")
    private Integer hasPush;


}
