package com.hj.admin.mapper;

import com.hj.admin.domain.push.SupplierInfo;

import java.util.List;

/**
 * 供应商基础信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierInfoMapper 
{
    /**
     * 查询供应商基础信息
     * 
     * @param id 供应商基础信息主键
     * @return 供应商基础信息
     */
    public SupplierInfo selectSupplierInfoById(Long id);

    /**
     * 查询供应商基础信息列表
     * 
     * @param supplierInfo 供应商基础信息
     * @return 供应商基础信息集合
     */
    public List<SupplierInfo> selectSupplierInfoList(SupplierInfo supplierInfo);

    /**
     * 新增供应商基础信息
     * 
     * @param supplierInfo 供应商基础信息
     * @return 结果
     */
    public int insertSupplierInfo(SupplierInfo supplierInfo);

    /**
     * 修改供应商基础信息
     * 
     * @param supplierInfo 供应商基础信息
     * @return 结果
     */
    public int updateSupplierInfo(SupplierInfo supplierInfo);

    /**
     * 删除供应商基础信息
     * 
     * @param id 供应商基础信息主键
     * @return 结果
     */
    public int deleteSupplierInfoById(Long id);

    /**
     * 批量删除供应商基础信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierInfoByIds(String[] ids);
}
