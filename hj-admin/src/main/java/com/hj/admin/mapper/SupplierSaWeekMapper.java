package com.hj.admin.mapper;

import java.util.List;
import com.hj.admin.domain.pull.SupplierSaWeek;

/**
 * 计划协议Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierSaWeekMapper 
{
    /**
     * 查询计划协议
     * 
     * @param id 计划协议主键
     * @return 计划协议
     */
    public SupplierSaWeek selectSupplierSaWeekById(Long id);

    /**
     * 查询计划协议列表
     * 
     * @param supplierSaWeek 计划协议
     * @return 计划协议集合
     */
    public List<SupplierSaWeek> selectSupplierSaWeekList(SupplierSaWeek supplierSaWeek);

    /**
     * 新增计划协议
     * 
     * @param supplierSaWeek 计划协议
     * @return 结果
     */
    public int insertSupplierSaWeek(SupplierSaWeek supplierSaWeek);

    /**
     * 修改计划协议
     * 
     * @param supplierSaWeek 计划协议
     * @return 结果
     */
    public int updateSupplierSaWeek(SupplierSaWeek supplierSaWeek);

    /**
     * 删除计划协议
     * 
     * @param id 计划协议主键
     * @return 结果
     */
    public int deleteSupplierSaWeekById(Long id);

    /**
     * 批量删除计划协议
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierSaWeekByIds(String[] ids);
}
