package com.hj.admin.mapper;

import com.hj.admin.domain.push.SupplierSinvData;

import java.util.List;

/**
 * 供应商共享库存Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface SupplierSinvDataMapper 
{
    /**
     * 查询供应商共享库存
     * 
     * @param id 供应商共享库存主键
     * @return 供应商共享库存
     */
    public SupplierSinvData selectSupplierSinvDataById(Long id);

    /**
     * 查询供应商共享库存列表
     * 
     * @param supplierSinvData 供应商共享库存
     * @return 供应商共享库存集合
     */
    public List<SupplierSinvData> selectSupplierSinvDataList(SupplierSinvData supplierSinvData);

    /**
     * 新增供应商共享库存
     * 
     * @param supplierSinvData 供应商共享库存
     * @return 结果
     */
    public int insertSupplierSinvData(SupplierSinvData supplierSinvData);

    /**
     * 修改供应商共享库存
     * 
     * @param supplierSinvData 供应商共享库存
     * @return 结果
     */
    public int updateSupplierSinvData(SupplierSinvData supplierSinvData);

    /**
     * 删除供应商共享库存
     * 
     * @param id 供应商共享库存主键
     * @return 结果
     */
    public int deleteSupplierSinvDataById(Long id);

    /**
     * 批量删除供应商共享库存
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierSinvDataByIds(String[] ids);
}
