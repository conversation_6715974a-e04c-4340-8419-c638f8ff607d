package com.hj.admin.mapper;

import java.util.List;
import com.hj.admin.domain.push.SupplierProAttachmentData;

/**
 * 附件类数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProAttachmentDataMapper 
{
    /**
     * 查询附件类数据
     * 
     * @param id 附件类数据主键
     * @return 附件类数据
     */
    public SupplierProAttachmentData selectSupplierProAttachmentDataById(Long id);

    /**
     * 查询附件类数据列表
     * 
     * @param supplierProAttachmentData 附件类数据
     * @return 附件类数据集合
     */
    public List<SupplierProAttachmentData> selectSupplierProAttachmentDataList(SupplierProAttachmentData supplierProAttachmentData);

    /**
     * 新增附件类数据
     * 
     * @param supplierProAttachmentData 附件类数据
     * @return 结果
     */
    public int insertSupplierProAttachmentData(SupplierProAttachmentData supplierProAttachmentData);

    /**
     * 修改附件类数据
     * 
     * @param supplierProAttachmentData 附件类数据
     * @return 结果
     */
    public int updateSupplierProAttachmentData(SupplierProAttachmentData supplierProAttachmentData);

    /**
     * 删除附件类数据
     * 
     * @param id 附件类数据主键
     * @return 结果
     */
    public int deleteSupplierProAttachmentDataById(Long id);

    /**
     * 批量删除附件类数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProAttachmentDataByIds(String[] ids);
}
