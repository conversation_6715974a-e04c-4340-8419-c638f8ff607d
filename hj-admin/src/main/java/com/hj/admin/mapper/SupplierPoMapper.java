package com.hj.admin.mapper;

import java.util.List;
import com.hj.admin.domain.pull.SupplierPo;

/**
 * 采购订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierPoMapper 
{
    /**
     * 查询采购订单
     * 
     * @param id 采购订单主键
     * @return 采购订单
     */
    public SupplierPo selectSupplierPoById(Long id);

    /**
     * 查询采购订单列表
     * 
     * @param supplierPo 采购订单
     * @return 采购订单集合
     */
    public List<SupplierPo> selectSupplierPoList(SupplierPo supplierPo);

    /**
     * 新增采购订单
     * 
     * @param supplierPo 采购订单
     * @return 结果
     */
    public int insertSupplierPo(SupplierPo supplierPo);

    /**
     * 修改采购订单
     * 
     * @param supplierPo 采购订单
     * @return 结果
     */
    public int updateSupplierPo(SupplierPo supplierPo);

    /**
     * 删除采购订单
     * 
     * @param id 采购订单主键
     * @return 结果
     */
    public int deleteSupplierPoById(Long id);

    /**
     * 批量删除采购订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierPoByIds(String[] ids);
}
