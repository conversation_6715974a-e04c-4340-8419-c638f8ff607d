package com.hj.admin.mapper;

import java.util.List;
import com.hj.admin.domain.pull.SupplierProCschedul;

/**
 * 排序供货Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProCschedulMapper 
{
    /**
     * 查询排序供货
     * 
     * @param id 排序供货主键
     * @return 排序供货
     */
    public SupplierProCschedul selectSupplierProCschedulById(Long id);

    /**
     * 查询排序供货列表
     * 
     * @param supplierProCschedul 排序供货
     * @return 排序供货集合
     */
    public List<SupplierProCschedul> selectSupplierProCschedulList(SupplierProCschedul supplierProCschedul);

    /**
     * 新增排序供货
     * 
     * @param supplierProCschedul 排序供货
     * @return 结果
     */
    public int insertSupplierProCschedul(SupplierProCschedul supplierProCschedul);

    /**
     * 修改排序供货
     * 
     * @param supplierProCschedul 排序供货
     * @return 结果
     */
    public int updateSupplierProCschedul(SupplierProCschedul supplierProCschedul);

    /**
     * 删除排序供货
     * 
     * @param id 排序供货主键
     * @return 结果
     */
    public int deleteSupplierProCschedulById(Long id);

    /**
     * 批量删除排序供货
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProCschedulByIds(String[] ids);
}
