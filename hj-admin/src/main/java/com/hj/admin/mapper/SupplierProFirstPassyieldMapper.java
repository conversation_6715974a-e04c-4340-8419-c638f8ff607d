package com.hj.admin.mapper;

import java.util.List;
import com.hj.admin.domain.push.SupplierProFirstPassyield;

/**
 * 产品一次合格率Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProFirstPassyieldMapper 
{
    /**
     * 查询产品一次合格率
     * 
     * @param id 产品一次合格率主键
     * @return 产品一次合格率
     */
    public SupplierProFirstPassyield selectSupplierProFirstPassyieldById(Long id);

    /**
     * 查询产品一次合格率列表
     * 
     * @param supplierProFirstPassyield 产品一次合格率
     * @return 产品一次合格率集合
     */
    public List<SupplierProFirstPassyield> selectSupplierProFirstPassyieldList(SupplierProFirstPassyield supplierProFirstPassyield);

    /**
     * 新增产品一次合格率
     * 
     * @param supplierProFirstPassyield 产品一次合格率
     * @return 结果
     */
    public int insertSupplierProFirstPassyield(SupplierProFirstPassyield supplierProFirstPassyield);

    /**
     * 修改产品一次合格率
     * 
     * @param supplierProFirstPassyield 产品一次合格率
     * @return 结果
     */
    public int updateSupplierProFirstPassyield(SupplierProFirstPassyield supplierProFirstPassyield);

    /**
     * 删除产品一次合格率
     * 
     * @param id 产品一次合格率主键
     * @return 结果
     */
    public int deleteSupplierProFirstPassyieldById(Long id);

    /**
     * 批量删除产品一次合格率
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProFirstPassyieldByIds(String[] ids);
}
