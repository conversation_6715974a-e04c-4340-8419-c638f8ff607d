package com.hj.admin.mapper;

import java.util.List;
import com.hj.admin.domain.push.SupplierProMaterialData;

/**
 * 物料主数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProMaterialDataMapper 
{
    /**
     * 查询物料主数据
     * 
     * @param id 物料主数据主键
     * @return 物料主数据
     */
    public SupplierProMaterialData selectSupplierProMaterialDataById(Long id);

    /**
     * 查询物料主数据列表
     * 
     * @param supplierProMaterialData 物料主数据
     * @return 物料主数据集合
     */
    public List<SupplierProMaterialData> selectSupplierProMaterialDataList(SupplierProMaterialData supplierProMaterialData);

    /**
     * 新增物料主数据
     * 
     * @param supplierProMaterialData 物料主数据
     * @return 结果
     */
    public int insertSupplierProMaterialData(SupplierProMaterialData supplierProMaterialData);

    /**
     * 修改物料主数据
     * 
     * @param supplierProMaterialData 物料主数据
     * @return 结果
     */
    public int updateSupplierProMaterialData(SupplierProMaterialData supplierProMaterialData);

    /**
     * 删除物料主数据
     * 
     * @param id 物料主数据主键
     * @return 结果
     */
    public int deleteSupplierProMaterialDataById(Long id);

    /**
     * 批量删除物料主数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProMaterialDataByIds(String[] ids);
}
