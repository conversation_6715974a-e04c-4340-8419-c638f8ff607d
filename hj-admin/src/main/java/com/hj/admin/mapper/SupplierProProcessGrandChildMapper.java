package com.hj.admin.mapper;

import java.util.List;
import com.hj.admin.domain.push.SupplierProProcessGrandChild;

/**
 * 工艺子子集（关联supplier_pro_process_child）Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProProcessGrandChildMapper 
{
    /**
     * 查询工艺子子集（关联supplier_pro_process_child）
     * 
     * @param id 工艺子子集（关联supplier_pro_process_child）主键
     * @return 工艺子子集（关联supplier_pro_process_child）
     */
    public SupplierProProcessGrandChild selectSupplierProProcessGrandChildById(Long id);

    /**
     * 查询工艺子子集（关联supplier_pro_process_child）列表
     * 
     * @param supplierProProcessGrandChild 工艺子子集（关联supplier_pro_process_child）
     * @return 工艺子子集（关联supplier_pro_process_child）集合
     */
    public List<SupplierProProcessGrandChild> selectSupplierProProcessGrandChildList(SupplierProProcessGrandChild supplierProProcessGrandChild);

    /**
     * 新增工艺子子集（关联supplier_pro_process_child）
     * 
     * @param supplierProProcessGrandChild 工艺子子集（关联supplier_pro_process_child）
     * @return 结果
     */
    public int insertSupplierProProcessGrandChild(SupplierProProcessGrandChild supplierProProcessGrandChild);

    /**
     * 修改工艺子子集（关联supplier_pro_process_child）
     * 
     * @param supplierProProcessGrandChild 工艺子子集（关联supplier_pro_process_child）
     * @return 结果
     */
    public int updateSupplierProProcessGrandChild(SupplierProProcessGrandChild supplierProProcessGrandChild);

    /**
     * 删除工艺子子集（关联supplier_pro_process_child）
     * 
     * @param id 工艺子子集（关联supplier_pro_process_child）主键
     * @return 结果
     */
    public int deleteSupplierProProcessGrandChildById(Long id);

    /**
     * 批量删除工艺子子集（关联supplier_pro_process_child）
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProProcessGrandChildByIds(String[] ids);
}
