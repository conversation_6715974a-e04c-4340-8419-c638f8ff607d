package com.hj.admin.mapper;

import java.util.List;
import com.hj.admin.domain.pull.SupplierReturn;

/**
 * 退货单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierReturnMapper 
{
    /**
     * 查询退货单
     * 
     * @param id 退货单主键
     * @return 退货单
     */
    public SupplierReturn selectSupplierReturnById(Long id);

    /**
     * 查询退货单列表
     * 
     * @param supplierReturn 退货单
     * @return 退货单集合
     */
    public List<SupplierReturn> selectSupplierReturnList(SupplierReturn supplierReturn);

    /**
     * 新增退货单
     * 
     * @param supplierReturn 退货单
     * @return 结果
     */
    public int insertSupplierReturn(SupplierReturn supplierReturn);

    /**
     * 修改退货单
     * 
     * @param supplierReturn 退货单
     * @return 结果
     */
    public int updateSupplierReturn(SupplierReturn supplierReturn);

    /**
     * 删除退货单
     * 
     * @param id 退货单主键
     * @return 结果
     */
    public int deleteSupplierReturnById(Long id);

    /**
     * 批量删除退货单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierReturnByIds(String[] ids);
}
