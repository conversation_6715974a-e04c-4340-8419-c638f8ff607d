package com.hj.admin.mapper;

import java.util.List;
import com.hj.admin.domain.push.SupplierProData;

/**
 * 生产过程数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProDataMapper 
{
    /**
     * 查询生产过程数据
     * 
     * @param id 生产过程数据主键
     * @return 生产过程数据
     */
    public SupplierProData selectSupplierProDataById(Long id);

    /**
     * 查询生产过程数据列表
     * 
     * @param supplierProData 生产过程数据
     * @return 生产过程数据集合
     */
    public List<SupplierProData> selectSupplierProDataList(SupplierProData supplierProData);

    /**
     * 新增生产过程数据
     * 
     * @param supplierProData 生产过程数据
     * @return 结果
     */
    public int insertSupplierProData(SupplierProData supplierProData);

    /**
     * 修改生产过程数据
     * 
     * @param supplierProData 生产过程数据
     * @return 结果
     */
    public int updateSupplierProData(SupplierProData supplierProData);

    /**
     * 删除生产过程数据
     * 
     * @param id 生产过程数据主键
     * @return 结果
     */
    public int deleteSupplierProDataById(Long id);

    /**
     * 批量删除生产过程数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProDataByIds(String[] ids);
}
