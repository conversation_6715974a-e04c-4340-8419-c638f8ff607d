package com.hj.admin.mapper;

import java.util.List;
import com.hj.admin.domain.pull.SupplierMrpMonth;

/**
 * M+6月物料需求计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierMrpMonthMapper 
{
    /**
     * 查询M+6月物料需求计划
     * 
     * @param id M+6月物料需求计划主键
     * @return M+6月物料需求计划
     */
    public SupplierMrpMonth selectSupplierMrpMonthById(Long id);

    /**
     * 查询M+6月物料需求计划列表
     * 
     * @param supplierMrpMonth M+6月物料需求计划
     * @return M+6月物料需求计划集合
     */
    public List<SupplierMrpMonth> selectSupplierMrpMonthList(SupplierMrpMonth supplierMrpMonth);

    /**
     * 新增M+6月物料需求计划
     * 
     * @param supplierMrpMonth M+6月物料需求计划
     * @return 结果
     */
    public int insertSupplierMrpMonth(SupplierMrpMonth supplierMrpMonth);

    /**
     * 修改M+6月物料需求计划
     * 
     * @param supplierMrpMonth M+6月物料需求计划
     * @return 结果
     */
    public int updateSupplierMrpMonth(SupplierMrpMonth supplierMrpMonth);

    /**
     * 删除M+6月物料需求计划
     * 
     * @param id M+6月物料需求计划主键
     * @return 结果
     */
    public int deleteSupplierMrpMonthById(Long id);

    /**
     * 批量删除M+6月物料需求计划
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierMrpMonthByIds(String[] ids);
}
