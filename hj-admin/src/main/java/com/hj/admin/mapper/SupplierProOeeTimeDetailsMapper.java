package com.hj.admin.mapper;

import java.util.List;
import com.hj.admin.domain.push.SupplierProOeeTimeDetails;

/**
 * OEE时间明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProOeeTimeDetailsMapper 
{
    /**
     * 查询OEE时间明细
     * 
     * @param id OEE时间明细主键
     * @return OEE时间明细
     */
    public SupplierProOeeTimeDetails selectSupplierProOeeTimeDetailsById(Long id);

    /**
     * 查询OEE时间明细列表
     * 
     * @param supplierProOeeTimeDetails OEE时间明细
     * @return OEE时间明细集合
     */
    public List<SupplierProOeeTimeDetails> selectSupplierProOeeTimeDetailsList(SupplierProOeeTimeDetails supplierProOeeTimeDetails);

    /**
     * 新增OEE时间明细
     * 
     * @param supplierProOeeTimeDetails OEE时间明细
     * @return 结果
     */
    public int insertSupplierProOeeTimeDetails(SupplierProOeeTimeDetails supplierProOeeTimeDetails);

    /**
     * 修改OEE时间明细
     * 
     * @param supplierProOeeTimeDetails OEE时间明细
     * @return 结果
     */
    public int updateSupplierProOeeTimeDetails(SupplierProOeeTimeDetails supplierProOeeTimeDetails);

    /**
     * 删除OEE时间明细
     * 
     * @param id OEE时间明细主键
     * @return 结果
     */
    public int deleteSupplierProOeeTimeDetailsById(Long id);

    /**
     * 批量删除OEE时间明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProOeeTimeDetailsByIds(String[] ids);
}
