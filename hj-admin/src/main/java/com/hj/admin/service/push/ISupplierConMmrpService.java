package com.hj.admin.service.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierConMmrp;

/**
 * M+6月物料需求计划风险确认Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierConMmrpService 
{
    /**
     * 查询M+6月物料需求计划风险确认
     * 
     * @param id M+6月物料需求计划风险确认主键
     * @return M+6月物料需求计划风险确认
     */
    public SupplierConMmrp selectSupplierConMmrpById(Long id);

    /**
     * 查询M+6月物料需求计划风险确认列表
     * 
     * @param supplierConMmrp M+6月物料需求计划风险确认
     * @return M+6月物料需求计划风险确认集合
     */
    public List<SupplierConMmrp> selectSupplierConMmrpList(SupplierConMmrp supplierConMmrp);

    /**
     * 新增M+6月物料需求计划风险确认
     * 
     * @param supplierConMmrp M+6月物料需求计划风险确认
     * @return 结果
     */
    public int insertSupplierConMmrp(SupplierConMmrp supplierConMmrp);

    /**
     * 修改M+6月物料需求计划风险确认
     * 
     * @param supplierConMmrp M+6月物料需求计划风险确认
     * @return 结果
     */
    public int updateSupplierConMmrp(SupplierConMmrp supplierConMmrp);

    /**
     * 批量删除M+6月物料需求计划风险确认
     * 
     * @param ids 需要删除的M+6月物料需求计划风险确认主键集合
     * @return 结果
     */
    public int deleteSupplierConMmrpByIds(String ids);

    /**
     * 删除M+6月物料需求计划风险确认信息
     * 
     * @param id M+6月物料需求计划风险确认主键
     * @return 结果
     */
    public int deleteSupplierConMmrpById(Long id);
}
