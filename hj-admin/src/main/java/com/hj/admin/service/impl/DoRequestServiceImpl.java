package com.hj.admin.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.hj.admin.domain.base.CheryPushReq;
import com.hj.admin.domain.base.CheryPullRes;
import com.hj.admin.service.IDoRequestService;
import com.hj.admin.util.CryptoUtils;
import com.hj.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

@Service
public class DoRequestServiceImpl implements IDoRequestService {

    private static final Logger log = LoggerFactory.getLogger(DoRequestServiceImpl.class);

    @Value("${chery.appKey}")
    private String appKey;
    @Value("${chery.appSecret}")
    private String appSecret;
    @Value("${chery.path.prefixUrl}")
    private String prefixUrl;

//    @Resource
//    private RestTemplate restTemplate;

    @Override
    public CheryPullRes pull(String path, Map<String, Object> map) {
        return doRequest(path, JSONUtil.toJsonStr(map));
    }


    @Override
    public CheryPullRes push(String path, CheryPushReq cheryPushReq) {
        cheryPushReq.setBatchNo(cheryPushReq.getList().get(0).getBatchNo());
        return doRequest(path, JSONUtil.toJsonStr(cheryPushReq));
    }

    private CheryPullRes doRequest(String path, String jsonBody) {
        String timestamp = "" + new Date().getTime();
        String nonce = CryptoUtils.generateRandomString(RandomUtil.randomInt(40) + 10);
        String originalString = "method=" + Method.POST.name() + "&path=" + path + "&appKey=" + appKey
                + "&appSecret=" + appSecret + "&timestamp=" + timestamp +
                "&nonce=" + nonce + "&jsonBody=" + jsonBody;
        String encryptedString = CryptoUtils.generateSha512Hex(originalString);

        log.info("原始字符串: {}", originalString);
        log.info("sign: {}", encryptedString);
        log.info("timestamp: {}", timestamp);
        log.info("nonce: {}", nonce);


        HttpRequest request = HttpUtil.createPost(prefixUrl + path)
                .contentType("application/json")
                .header("timestamp", timestamp)
                .header("nonce", nonce)
                .header("appKey", appKey)
                .header("sign", encryptedString)
                .body(jsonBody);
        String response = request.execute().body();
        CheryPullRes cheryPullRes = JSONUtil.toBean(response, CheryPullRes.class, true);
        if (!cheryPullRes.getCode().equals("200")) {
            log.error("请求失败: {}", cheryPullRes.getMessage());
            throw new ServiceException(cheryPullRes.getMessage());
        }
        log.info("响应结果: {}", response);
        return cheryPullRes;

//        HttpHeaders headers = new HttpHeaders();
//        headers.set("Content-Type", "application/json");
//        headers.set("timestamp", timestamp);
//        headers.set("nonce", nonce);
//        headers.set("appKey", appKey);
//        headers.set("sign", encryptedString);
//
//        HttpEntity<Object> requestEntity = new HttpEntity<>(jsonBody, headers);
//
//        ResponseEntity<CheryRes> response = restTemplate.exchange(
//                prefixUrl + path,
//                HttpMethod.POST,
//                requestEntity,
//                CheryRes.class);
//
//        log.info("response-body: {}", response.getBody());
//        if (response.getStatusCode().is2xxSuccessful()) {
//            if (Objects.requireNonNull(response.getBody()).getCode().equals(String.valueOf(HttpStatus.OK.value()))) {
//                return response.getBody();
//            } else {
//                log.error("请求失败: {}", response.getBody().getMessage());
//                throw new ServiceException(response.getBody().getMessage());
//            }
//        }
//        log.error("请求出错：{}", response.getStatusCode());
//        throw new ServiceException("请求出错，错误码：" + response.getStatusCode());
    }

}
