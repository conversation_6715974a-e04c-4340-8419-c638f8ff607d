package com.hj.admin.service.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierProCps;

/**
 * 过程控制项质量数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierProCpsService 
{
    /**
     * 查询过程控制项质量数据
     * 
     * @param id 过程控制项质量数据主键
     * @return 过程控制项质量数据
     */
    public SupplierProCps selectSupplierProCpsById(Long id);

    /**
     * 查询过程控制项质量数据列表
     * 
     * @param supplierProCps 过程控制项质量数据
     * @return 过程控制项质量数据集合
     */
    public List<SupplierProCps> selectSupplierProCpsList(SupplierProCps supplierProCps);

    /**
     * 新增过程控制项质量数据
     * 
     * @param supplierProCps 过程控制项质量数据
     * @return 结果
     */
    public int insertSupplierProCps(SupplierProCps supplierProCps);

    /**
     * 修改过程控制项质量数据
     * 
     * @param supplierProCps 过程控制项质量数据
     * @return 结果
     */
    public int updateSupplierProCps(SupplierProCps supplierProCps);

    /**
     * 批量删除过程控制项质量数据
     * 
     * @param ids 需要删除的过程控制项质量数据主键集合
     * @return 结果
     */
    public int deleteSupplierProCpsByIds(String ids);

    /**
     * 删除过程控制项质量数据信息
     * 
     * @param id 过程控制项质量数据主键
     * @return 结果
     */
    public int deleteSupplierProCpsById(Long id);
}
