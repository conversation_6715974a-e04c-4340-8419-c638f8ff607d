package com.hj.admin.service.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierConDate;

/**
 * 日物料需求计划风险确认Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierConDateService 
{
    /**
     * 查询日物料需求计划风险确认
     * 
     * @param id 日物料需求计划风险确认主键
     * @return 日物料需求计划风险确认
     */
    public SupplierConDate selectSupplierConDateById(Long id);

    /**
     * 查询日物料需求计划风险确认列表
     * 
     * @param supplierConDate 日物料需求计划风险确认
     * @return 日物料需求计划风险确认集合
     */
    public List<SupplierConDate> selectSupplierConDateList(SupplierConDate supplierConDate);

    /**
     * 新增日物料需求计划风险确认
     * 
     * @param supplierConDate 日物料需求计划风险确认
     * @return 结果
     */
    public int insertSupplierConDate(SupplierConDate supplierConDate);

    /**
     * 修改日物料需求计划风险确认
     * 
     * @param supplierConDate 日物料需求计划风险确认
     * @return 结果
     */
    public int updateSupplierConDate(SupplierConDate supplierConDate);

    /**
     * 批量删除日物料需求计划风险确认
     * 
     * @param ids 需要删除的日物料需求计划风险确认主键集合
     * @return 结果
     */
    public int deleteSupplierConDateByIds(String ids);

    /**
     * 删除日物料需求计划风险确认信息
     * 
     * @param id 日物料需求计划风险确认主键
     * @return 结果
     */
    public int deleteSupplierConDateById(Long id);
}
