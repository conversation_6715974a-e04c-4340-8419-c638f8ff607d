package com.hj.admin.service.impl.push;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierProAttachmentDataMapper;
import com.hj.admin.domain.push.SupplierProAttachmentData;
import com.hj.admin.service.push.ISupplierProAttachmentDataService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 附件类数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProAttachmentDataServiceImpl implements ISupplierProAttachmentDataService 
{
    @Resource
    private SupplierProAttachmentDataMapper supplierProAttachmentDataMapper;

    /**
     * 查询附件类数据
     * 
     * @param id 附件类数据主键
     * @return 附件类数据
     */
    @Override
    public SupplierProAttachmentData selectSupplierProAttachmentDataById(Long id)
    {
        return supplierProAttachmentDataMapper.selectSupplierProAttachmentDataById(id);
    }

    /**
     * 查询附件类数据列表
     * 
     * @param supplierProAttachmentData 附件类数据
     * @return 附件类数据
     */
    @Override
    public List<SupplierProAttachmentData> selectSupplierProAttachmentDataList(SupplierProAttachmentData supplierProAttachmentData)
    {
        return supplierProAttachmentDataMapper.selectSupplierProAttachmentDataList(supplierProAttachmentData);
    }

    /**
     * 新增附件类数据
     * 
     * @param supplierProAttachmentData 附件类数据
     * @return 结果
     */
    @Override
    public int insertSupplierProAttachmentData(SupplierProAttachmentData supplierProAttachmentData)
    {
        return supplierProAttachmentDataMapper.insertSupplierProAttachmentData(supplierProAttachmentData);
    }

    /**
     * 修改附件类数据
     * 
     * @param supplierProAttachmentData 附件类数据
     * @return 结果
     */
    @Override
    public int updateSupplierProAttachmentData(SupplierProAttachmentData supplierProAttachmentData)
    {
        return supplierProAttachmentDataMapper.updateSupplierProAttachmentData(supplierProAttachmentData);
    }

    /**
     * 批量删除附件类数据
     * 
     * @param ids 需要删除的附件类数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProAttachmentDataByIds(String ids)
    {
        return supplierProAttachmentDataMapper.deleteSupplierProAttachmentDataByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除附件类数据信息
     * 
     * @param id 附件类数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProAttachmentDataById(Long id)
    {
        return supplierProAttachmentDataMapper.deleteSupplierProAttachmentDataById(id);
    }
}
