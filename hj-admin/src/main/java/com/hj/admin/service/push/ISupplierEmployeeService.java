package com.hj.admin.service.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierEmployee;

/**
 * 人员资质信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierEmployeeService 
{
    /**
     * 查询人员资质信息
     * 
     * @param id 人员资质信息主键
     * @return 人员资质信息
     */
    public SupplierEmployee selectSupplierEmployeeById(Long id);

    /**
     * 查询人员资质信息列表
     * 
     * @param supplierEmployee 人员资质信息
     * @return 人员资质信息集合
     */
    public List<SupplierEmployee> selectSupplierEmployeeList(SupplierEmployee supplierEmployee);

    /**
     * 新增人员资质信息
     * 
     * @param supplierEmployee 人员资质信息
     * @return 结果
     */
    public int insertSupplierEmployee(SupplierEmployee supplierEmployee);

    /**
     * 修改人员资质信息
     * 
     * @param supplierEmployee 人员资质信息
     * @return 结果
     */
    public int updateSupplierEmployee(SupplierEmployee supplierEmployee);

    /**
     * 批量删除人员资质信息
     * 
     * @param ids 需要删除的人员资质信息主键集合
     * @return 结果
     */
    public int deleteSupplierEmployeeByIds(String ids);

    /**
     * 删除人员资质信息信息
     * 
     * @param id 人员资质信息主键
     * @return 结果
     */
    public int deleteSupplierEmployeeById(Long id);
}
