package com.hj.admin.service.impl.push;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierProFirstPassyieldMapper;
import com.hj.admin.domain.push.SupplierProFirstPassyield;
import com.hj.admin.service.push.ISupplierProFirstPassyieldService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 产品一次合格率Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProFirstPassyieldServiceImpl implements ISupplierProFirstPassyieldService 
{
    @Resource
    private SupplierProFirstPassyieldMapper supplierProFirstPassyieldMapper;

    /**
     * 查询产品一次合格率
     * 
     * @param id 产品一次合格率主键
     * @return 产品一次合格率
     */
    @Override
    public SupplierProFirstPassyield selectSupplierProFirstPassyieldById(Long id)
    {
        return supplierProFirstPassyieldMapper.selectSupplierProFirstPassyieldById(id);
    }

    /**
     * 查询产品一次合格率列表
     * 
     * @param supplierProFirstPassyield 产品一次合格率
     * @return 产品一次合格率
     */
    @Override
    public List<SupplierProFirstPassyield> selectSupplierProFirstPassyieldList(SupplierProFirstPassyield supplierProFirstPassyield)
    {
        return supplierProFirstPassyieldMapper.selectSupplierProFirstPassyieldList(supplierProFirstPassyield);
    }

    /**
     * 新增产品一次合格率
     * 
     * @param supplierProFirstPassyield 产品一次合格率
     * @return 结果
     */
    @Override
    public int insertSupplierProFirstPassyield(SupplierProFirstPassyield supplierProFirstPassyield)
    {
        return supplierProFirstPassyieldMapper.insertSupplierProFirstPassyield(supplierProFirstPassyield);
    }

    /**
     * 修改产品一次合格率
     * 
     * @param supplierProFirstPassyield 产品一次合格率
     * @return 结果
     */
    @Override
    public int updateSupplierProFirstPassyield(SupplierProFirstPassyield supplierProFirstPassyield)
    {
        return supplierProFirstPassyieldMapper.updateSupplierProFirstPassyield(supplierProFirstPassyield);
    }

    /**
     * 批量删除产品一次合格率
     * 
     * @param ids 需要删除的产品一次合格率主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProFirstPassyieldByIds(String ids)
    {
        return supplierProFirstPassyieldMapper.deleteSupplierProFirstPassyieldByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除产品一次合格率信息
     * 
     * @param id 产品一次合格率主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProFirstPassyieldById(Long id)
    {
        return supplierProFirstPassyieldMapper.deleteSupplierProFirstPassyieldById(id);
    }
}
