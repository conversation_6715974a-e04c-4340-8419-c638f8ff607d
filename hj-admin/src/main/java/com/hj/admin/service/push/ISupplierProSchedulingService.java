package com.hj.admin.service.push;

import com.hj.admin.domain.push.SupplierProScheduling;

import java.util.List;

/**
 * 排产数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierProSchedulingService 
{
    /**
     * 查询排产数据
     * 
     * @param id 排产数据主键
     * @return 排产数据
     */
    public SupplierProScheduling selectSupplierProSchedulingById(Long id);

    /**
     * 查询排产数据列表
     * 
     * @param supplierProScheduling 排产数据
     * @return 排产数据集合
     */
    public List<SupplierProScheduling> selectSupplierProSchedulingList(SupplierProScheduling supplierProScheduling);

    /**
     * 新增排产数据
     * 
     * @param supplierProScheduling 排产数据
     * @return 结果
     */
    public int insertSupplierProScheduling(SupplierProScheduling supplierProScheduling);

    /**
     * 修改排产数据
     * 
     * @param supplierProScheduling 排产数据
     * @return 结果
     */
    public int updateSupplierProScheduling(SupplierProScheduling supplierProScheduling);

    /**
     * 批量删除排产数据
     * 
     * @param ids 需要删除的排产数据主键集合
     * @return 结果
     */
    public int deleteSupplierProSchedulingByIds(String ids);

    /**
     * 删除排产数据信息
     * 
     * @param id 排产数据主键
     * @return 结果
     */
    public int deleteSupplierProSchedulingById(Long id);
}
