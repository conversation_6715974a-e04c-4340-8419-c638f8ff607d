package com.hj.admin.service.impl.push;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierProCpsMapper;
import com.hj.admin.domain.push.SupplierProCps;
import com.hj.admin.service.push.ISupplierProCpsService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 过程控制项质量数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProCpsServiceImpl implements ISupplierProCpsService 
{
    @Resource
    private SupplierProCpsMapper supplierProCpsMapper;

    /**
     * 查询过程控制项质量数据
     * 
     * @param id 过程控制项质量数据主键
     * @return 过程控制项质量数据
     */
    @Override
    public SupplierProCps selectSupplierProCpsById(Long id)
    {
        return supplierProCpsMapper.selectSupplierProCpsById(id);
    }

    /**
     * 查询过程控制项质量数据列表
     * 
     * @param supplierProCps 过程控制项质量数据
     * @return 过程控制项质量数据
     */
    @Override
    public List<SupplierProCps> selectSupplierProCpsList(SupplierProCps supplierProCps)
    {
        return supplierProCpsMapper.selectSupplierProCpsList(supplierProCps);
    }

    /**
     * 新增过程控制项质量数据
     * 
     * @param supplierProCps 过程控制项质量数据
     * @return 结果
     */
    @Override
    public int insertSupplierProCps(SupplierProCps supplierProCps)
    {
        return supplierProCpsMapper.insertSupplierProCps(supplierProCps);
    }

    /**
     * 修改过程控制项质量数据
     * 
     * @param supplierProCps 过程控制项质量数据
     * @return 结果
     */
    @Override
    public int updateSupplierProCps(SupplierProCps supplierProCps)
    {
        return supplierProCpsMapper.updateSupplierProCps(supplierProCps);
    }

    /**
     * 批量删除过程控制项质量数据
     * 
     * @param ids 需要删除的过程控制项质量数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProCpsByIds(String ids)
    {
        return supplierProCpsMapper.deleteSupplierProCpsByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除过程控制项质量数据信息
     * 
     * @param id 过程控制项质量数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProCpsById(Long id)
    {
        return supplierProCpsMapper.deleteSupplierProCpsById(id);
    }
}
