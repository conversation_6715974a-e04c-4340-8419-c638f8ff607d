package com.hj.admin.service.pull;

import java.util.List;
import com.hj.admin.domain.pull.SupplierDelState;

/**
 * 看板配送单Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierDelStateService 
{
    /**
     * 查询看板配送单
     * 
     * @param id 看板配送单主键
     * @return 看板配送单
     */
    public SupplierDelState selectSupplierDelStateById(Long id);

    /**
     * 查询看板配送单列表
     * 
     * @param supplierDelState 看板配送单
     * @return 看板配送单集合
     */
    public List<SupplierDelState> selectSupplierDelStateList(SupplierDelState supplierDelState);

    /**
     * 新增看板配送单
     * 
     * @param supplierDelState 看板配送单
     * @return 结果
     */
    public int insertSupplierDelState(SupplierDelState supplierDelState);

    /**
     * 修改看板配送单
     * 
     * @param supplierDelState 看板配送单
     * @return 结果
     */
    public int updateSupplierDelState(SupplierDelState supplierDelState);

    /**
     * 批量删除看板配送单
     * 
     * @param ids 需要删除的看板配送单主键集合
     * @return 结果
     */
    public int deleteSupplierDelStateByIds(String ids);

    /**
     * 删除看板配送单信息
     * 
     * @param id 看板配送单主键
     * @return 结果
     */
    public int deleteSupplierDelStateById(Long id);
}
