package com.hj.admin.service.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierProEnvironment;

/**
 * 环境业务数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierProEnvironmentService 
{
    /**
     * 查询环境业务数据
     * 
     * @param id 环境业务数据主键
     * @return 环境业务数据
     */
    public SupplierProEnvironment selectSupplierProEnvironmentById(Long id);

    /**
     * 查询环境业务数据列表
     * 
     * @param supplierProEnvironment 环境业务数据
     * @return 环境业务数据集合
     */
    public List<SupplierProEnvironment> selectSupplierProEnvironmentList(SupplierProEnvironment supplierProEnvironment);

    /**
     * 新增环境业务数据
     * 
     * @param supplierProEnvironment 环境业务数据
     * @return 结果
     */
    public int insertSupplierProEnvironment(SupplierProEnvironment supplierProEnvironment);

    /**
     * 修改环境业务数据
     * 
     * @param supplierProEnvironment 环境业务数据
     * @return 结果
     */
    public int updateSupplierProEnvironment(SupplierProEnvironment supplierProEnvironment);

    /**
     * 批量删除环境业务数据
     * 
     * @param ids 需要删除的环境业务数据主键集合
     * @return 结果
     */
    public int deleteSupplierProEnvironmentByIds(String ids);

    /**
     * 删除环境业务数据信息
     * 
     * @param id 环境业务数据主键
     * @return 结果
     */
    public int deleteSupplierProEnvironmentById(Long id);
}
