package com.hj.admin.service.impl.pull;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierProHschedulMapper;
import com.hj.admin.domain.pull.SupplierProHschedul;
import com.hj.admin.service.pull.ISupplierProHschedulService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 过焊装未过总装Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProHschedulServiceImpl implements ISupplierProHschedulService 
{
    @Resource
    private SupplierProHschedulMapper supplierProHschedulMapper;

    /**
     * 查询过焊装未过总装
     * 
     * @param id 过焊装未过总装主键
     * @return 过焊装未过总装
     */
    @Override
    public SupplierProHschedul selectSupplierProHschedulById(Long id)
    {
        return supplierProHschedulMapper.selectSupplierProHschedulById(id);
    }

    /**
     * 查询过焊装未过总装列表
     * 
     * @param supplierProHschedul 过焊装未过总装
     * @return 过焊装未过总装
     */
    @Override
    public List<SupplierProHschedul> selectSupplierProHschedulList(SupplierProHschedul supplierProHschedul)
    {
        return supplierProHschedulMapper.selectSupplierProHschedulList(supplierProHschedul);
    }

    /**
     * 新增过焊装未过总装
     * 
     * @param supplierProHschedul 过焊装未过总装
     * @return 结果
     */
    @Override
    public int insertSupplierProHschedul(SupplierProHschedul supplierProHschedul)
    {
        supplierProHschedul.setInsertTime(DateUtils.getNowDate());
        return supplierProHschedulMapper.insertSupplierProHschedul(supplierProHschedul);
    }

    /**
     * 修改过焊装未过总装
     * 
     * @param supplierProHschedul 过焊装未过总装
     * @return 结果
     */
    @Override
    public int updateSupplierProHschedul(SupplierProHschedul supplierProHschedul)
    {
        supplierProHschedul.setModifyTime(DateUtils.getNowDate());
        return supplierProHschedulMapper.updateSupplierProHschedul(supplierProHschedul);
    }

    /**
     * 批量删除过焊装未过总装
     * 
     * @param ids 需要删除的过焊装未过总装主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProHschedulByIds(String ids)
    {
        return supplierProHschedulMapper.deleteSupplierProHschedulByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除过焊装未过总装信息
     * 
     * @param id 过焊装未过总装主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProHschedulById(Long id)
    {
        return supplierProHschedulMapper.deleteSupplierProHschedulById(id);
    }
}
