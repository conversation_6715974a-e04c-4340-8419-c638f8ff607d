package com.hj.admin.service.impl.push;

import java.util.List;


import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierEmployeeMapper;
import com.hj.admin.domain.push.SupplierEmployee;
import com.hj.admin.service.push.ISupplierEmployeeService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 人员资质信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierEmployeeServiceImpl implements ISupplierEmployeeService 
{
    @Resource
    private SupplierEmployeeMapper supplierEmployeeMapper;

    /**
     * 查询人员资质信息
     * 
     * @param id 人员资质信息主键
     * @return 人员资质信息
     */
    @Override
    public SupplierEmployee selectSupplierEmployeeById(Long id)
    {
        return supplierEmployeeMapper.selectSupplierEmployeeById(id);
    }

    /**
     * 查询人员资质信息列表
     * 
     * @param supplierEmployee 人员资质信息
     * @return 人员资质信息
     */
    @Override
    public List<SupplierEmployee> selectSupplierEmployeeList(SupplierEmployee supplierEmployee)
    {
        return supplierEmployeeMapper.selectSupplierEmployeeList(supplierEmployee);
    }

    /**
     * 新增人员资质信息
     * 
     * @param supplierEmployee 人员资质信息
     * @return 结果
     */
    @Override
    public int insertSupplierEmployee(SupplierEmployee supplierEmployee)
    {
        return supplierEmployeeMapper.insertSupplierEmployee(supplierEmployee);
    }

    /**
     * 修改人员资质信息
     * 
     * @param supplierEmployee 人员资质信息
     * @return 结果
     */
    @Override
    public int updateSupplierEmployee(SupplierEmployee supplierEmployee)
    {
        return supplierEmployeeMapper.updateSupplierEmployee(supplierEmployee);
    }

    /**
     * 批量删除人员资质信息
     * 
     * @param ids 需要删除的人员资质信息主键
     * @return 结果
     */
    @Override
    public int deleteSupplierEmployeeByIds(String ids)
    {
        return supplierEmployeeMapper.deleteSupplierEmployeeByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除人员资质信息信息
     * 
     * @param id 人员资质信息主键
     * @return 结果
     */
    @Override
    public int deleteSupplierEmployeeById(Long id)
    {
        return supplierEmployeeMapper.deleteSupplierEmployeeById(id);
    }
}
