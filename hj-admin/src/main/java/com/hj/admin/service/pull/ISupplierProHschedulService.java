package com.hj.admin.service.pull;

import java.util.List;
import com.hj.admin.domain.pull.SupplierProHschedul;

/**
 * 过焊装未过总装Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierProHschedulService 
{
    /**
     * 查询过焊装未过总装
     * 
     * @param id 过焊装未过总装主键
     * @return 过焊装未过总装
     */
    public SupplierProHschedul selectSupplierProHschedulById(Long id);

    /**
     * 查询过焊装未过总装列表
     * 
     * @param supplierProHschedul 过焊装未过总装
     * @return 过焊装未过总装集合
     */
    public List<SupplierProHschedul> selectSupplierProHschedulList(SupplierProHschedul supplierProHschedul);

    /**
     * 新增过焊装未过总装
     * 
     * @param supplierProHschedul 过焊装未过总装
     * @return 结果
     */
    public int insertSupplierProHschedul(SupplierProHschedul supplierProHschedul);

    /**
     * 修改过焊装未过总装
     * 
     * @param supplierProHschedul 过焊装未过总装
     * @return 结果
     */
    public int updateSupplierProHschedul(SupplierProHschedul supplierProHschedul);

    /**
     * 批量删除过焊装未过总装
     * 
     * @param ids 需要删除的过焊装未过总装主键集合
     * @return 结果
     */
    public int deleteSupplierProHschedulByIds(String ids);

    /**
     * 删除过焊装未过总装信息
     * 
     * @param id 过焊装未过总装主键
     * @return 结果
     */
    public int deleteSupplierProHschedulById(Long id);
}
