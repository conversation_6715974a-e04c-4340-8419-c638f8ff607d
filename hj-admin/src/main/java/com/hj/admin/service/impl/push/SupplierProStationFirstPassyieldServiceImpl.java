package com.hj.admin.service.impl.push;

import java.util.List;

import com.hj.admin.service.push.ISupplierProDataService;
import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierProStationFirstPassyieldMapper;
import com.hj.admin.domain.push.SupplierProStationFirstPassyield;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 工位一次合格率Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProStationFirstPassyieldServiceImpl implements ISupplierProDataService.ISupplierProStationFirstPassyieldService
{
    @Resource
    private SupplierProStationFirstPassyieldMapper supplierProStationFirstPassyieldMapper;

    /**
     * 查询工位一次合格率
     * 
     * @param id 工位一次合格率主键
     * @return 工位一次合格率
     */
    @Override
    public SupplierProStationFirstPassyield selectSupplierProStationFirstPassyieldById(Long id)
    {
        return supplierProStationFirstPassyieldMapper.selectSupplierProStationFirstPassyieldById(id);
    }

    /**
     * 查询工位一次合格率列表
     * 
     * @param supplierProStationFirstPassyield 工位一次合格率
     * @return 工位一次合格率
     */
    @Override
    public List<SupplierProStationFirstPassyield> selectSupplierProStationFirstPassyieldList(SupplierProStationFirstPassyield supplierProStationFirstPassyield)
    {
        return supplierProStationFirstPassyieldMapper.selectSupplierProStationFirstPassyieldList(supplierProStationFirstPassyield);
    }

    /**
     * 新增工位一次合格率
     * 
     * @param supplierProStationFirstPassyield 工位一次合格率
     * @return 结果
     */
    @Override
    public int insertSupplierProStationFirstPassyield(SupplierProStationFirstPassyield supplierProStationFirstPassyield)
    {
        return supplierProStationFirstPassyieldMapper.insertSupplierProStationFirstPassyield(supplierProStationFirstPassyield);
    }

    /**
     * 修改工位一次合格率
     * 
     * @param supplierProStationFirstPassyield 工位一次合格率
     * @return 结果
     */
    @Override
    public int updateSupplierProStationFirstPassyield(SupplierProStationFirstPassyield supplierProStationFirstPassyield)
    {
        return supplierProStationFirstPassyieldMapper.updateSupplierProStationFirstPassyield(supplierProStationFirstPassyield);
    }

    /**
     * 批量删除工位一次合格率
     * 
     * @param ids 需要删除的工位一次合格率主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProStationFirstPassyieldByIds(String ids)
    {
        return supplierProStationFirstPassyieldMapper.deleteSupplierProStationFirstPassyieldByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除工位一次合格率信息
     * 
     * @param id 工位一次合格率主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProStationFirstPassyieldById(Long id)
    {
        return supplierProStationFirstPassyieldMapper.deleteSupplierProStationFirstPassyieldById(id);
    }
}
