package com.hj.admin.service.impl.pull;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierProTschedulMapper;
import com.hj.admin.domain.pull.SupplierProTschedul;
import com.hj.admin.service.pull.ISupplierProTschedulService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 过涂装未过总装Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProTschedulServiceImpl implements ISupplierProTschedulService 
{
    @Resource
    private SupplierProTschedulMapper supplierProTschedulMapper;

    /**
     * 查询过涂装未过总装
     * 
     * @param id 过涂装未过总装主键
     * @return 过涂装未过总装
     */
    @Override
    public SupplierProTschedul selectSupplierProTschedulById(Long id)
    {
        return supplierProTschedulMapper.selectSupplierProTschedulById(id);
    }

    /**
     * 查询过涂装未过总装列表
     * 
     * @param supplierProTschedul 过涂装未过总装
     * @return 过涂装未过总装
     */
    @Override
    public List<SupplierProTschedul> selectSupplierProTschedulList(SupplierProTschedul supplierProTschedul)
    {
        return supplierProTschedulMapper.selectSupplierProTschedulList(supplierProTschedul);
    }

    /**
     * 新增过涂装未过总装
     * 
     * @param supplierProTschedul 过涂装未过总装
     * @return 结果
     */
    @Override
    public int insertSupplierProTschedul(SupplierProTschedul supplierProTschedul)
    {
        supplierProTschedul.setInsertTime(DateUtils.getNowDate());
        return supplierProTschedulMapper.insertSupplierProTschedul(supplierProTschedul);
    }

    /**
     * 修改过涂装未过总装
     * 
     * @param supplierProTschedul 过涂装未过总装
     * @return 结果
     */
    @Override
    public int updateSupplierProTschedul(SupplierProTschedul supplierProTschedul)
    {
        supplierProTschedul.setModifyTime(DateUtils.getNowDate());
        return supplierProTschedulMapper.updateSupplierProTschedul(supplierProTschedul);
    }

    /**
     * 批量删除过涂装未过总装
     * 
     * @param ids 需要删除的过涂装未过总装主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProTschedulByIds(String ids)
    {
        return supplierProTschedulMapper.deleteSupplierProTschedulByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除过涂装未过总装信息
     * 
     * @param id 过涂装未过总装主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProTschedulById(Long id)
    {
        return supplierProTschedulMapper.deleteSupplierProTschedulById(id);
    }
}
