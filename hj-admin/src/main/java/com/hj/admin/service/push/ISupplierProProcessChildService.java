package com.hj.admin.service.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierProProcessChild;

/**
 * 工艺子集（关联supplier_pro_process）Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierProProcessChildService 
{
    /**
     * 查询工艺子集（关联supplier_pro_process）
     * 
     * @param id 工艺子集（关联supplier_pro_process）主键
     * @return 工艺子集（关联supplier_pro_process）
     */
    public SupplierProProcessChild selectSupplierProProcessChildById(Long id);

    /**
     * 查询工艺子集（关联supplier_pro_process）列表
     * 
     * @param supplierProProcessChild 工艺子集（关联supplier_pro_process）
     * @return 工艺子集（关联supplier_pro_process）集合
     */
    public List<SupplierProProcessChild> selectSupplierProProcessChildList(SupplierProProcessChild supplierProProcessChild);

    /**
     * 新增工艺子集（关联supplier_pro_process）
     * 
     * @param supplierProProcessChild 工艺子集（关联supplier_pro_process）
     * @return 结果
     */
    public int insertSupplierProProcessChild(SupplierProProcessChild supplierProProcessChild);

    /**
     * 修改工艺子集（关联supplier_pro_process）
     * 
     * @param supplierProProcessChild 工艺子集（关联supplier_pro_process）
     * @return 结果
     */
    public int updateSupplierProProcessChild(SupplierProProcessChild supplierProProcessChild);

    /**
     * 批量删除工艺子集（关联supplier_pro_process）
     * 
     * @param ids 需要删除的工艺子集（关联supplier_pro_process）主键集合
     * @return 结果
     */
    public int deleteSupplierProProcessChildByIds(String ids);

    /**
     * 删除工艺子集（关联supplier_pro_process）信息
     * 
     * @param id 工艺子集（关联supplier_pro_process）主键
     * @return 结果
     */
    public int deleteSupplierProProcessChildById(Long id);
}
