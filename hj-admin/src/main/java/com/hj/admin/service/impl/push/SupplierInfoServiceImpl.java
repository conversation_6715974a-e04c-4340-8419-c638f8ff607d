package com.hj.admin.service.impl.push;

import com.hj.admin.domain.push.SupplierInfo;
import com.hj.admin.mapper.SupplierInfoMapper;
import com.hj.admin.service.push.ISupplierInfoService;
import com.hj.common.core.text.Convert;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 供应商基础信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierInfoServiceImpl implements ISupplierInfoService
{
    @Resource
    private SupplierInfoMapper supplierInfoMapper;

    /**
     * 查询供应商基础信息
     * 
     * @param id 供应商基础信息主键
     * @return 供应商基础信息
     */
    @Override
    public SupplierInfo selectSupplierInfoById(Long id)
    {
        return supplierInfoMapper.selectSupplierInfoById(id);
    }

    /**
     * 查询供应商基础信息列表
     * 
     * @param supplierInfo 供应商基础信息
     * @return 供应商基础信息
     */
    @Override
    public List<SupplierInfo> selectSupplierInfoList(SupplierInfo supplierInfo)
    {
        return supplierInfoMapper.selectSupplierInfoList(supplierInfo);
    }

    /**
     * 新增供应商基础信息
     * 
     * @param supplierInfo 供应商基础信息
     * @return 结果
     */
    @Override
    public int insertSupplierInfo(SupplierInfo supplierInfo)
    {
        return supplierInfoMapper.insertSupplierInfo(supplierInfo);
    }

    /**
     * 修改供应商基础信息
     * 
     * @param supplierInfo 供应商基础信息
     * @return 结果
     */
    @Override
    public int updateSupplierInfo(SupplierInfo supplierInfo)
    {
        return supplierInfoMapper.updateSupplierInfo(supplierInfo);
    }

    /**
     * 批量删除供应商基础信息
     * 
     * @param ids 需要删除的供应商基础信息主键
     * @return 结果
     */
    @Override
    public int deleteSupplierInfoByIds(String ids)
    {
        return supplierInfoMapper.deleteSupplierInfoByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除供应商基础信息信息
     * 
     * @param id 供应商基础信息主键
     * @return 结果
     */
    @Override
    public int deleteSupplierInfoById(Long id)
    {
        return supplierInfoMapper.deleteSupplierInfoById(id);
    }
}
