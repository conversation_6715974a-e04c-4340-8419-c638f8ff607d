package com.hj.admin.service.impl.pull;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierDelStateMapper;
import com.hj.admin.domain.pull.SupplierDelState;
import com.hj.admin.service.pull.ISupplierDelStateService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 看板配送单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierDelStateServiceImpl implements ISupplierDelStateService 
{
    @Resource
    private SupplierDelStateMapper supplierDelStateMapper;

    /**
     * 查询看板配送单
     * 
     * @param id 看板配送单主键
     * @return 看板配送单
     */
    @Override
    public SupplierDelState selectSupplierDelStateById(Long id)
    {
        return supplierDelStateMapper.selectSupplierDelStateById(id);
    }

    /**
     * 查询看板配送单列表
     * 
     * @param supplierDelState 看板配送单
     * @return 看板配送单
     */
    @Override
    public List<SupplierDelState> selectSupplierDelStateList(SupplierDelState supplierDelState)
    {
        return supplierDelStateMapper.selectSupplierDelStateList(supplierDelState);
    }

    /**
     * 新增看板配送单
     * 
     * @param supplierDelState 看板配送单
     * @return 结果
     */
    @Override
    public int insertSupplierDelState(SupplierDelState supplierDelState)
    {
        supplierDelState.setInsertTime(DateUtils.getNowDate());
        return supplierDelStateMapper.insertSupplierDelState(supplierDelState);
    }

    /**
     * 修改看板配送单
     * 
     * @param supplierDelState 看板配送单
     * @return 结果
     */
    @Override
    public int updateSupplierDelState(SupplierDelState supplierDelState)
    {
        supplierDelState.setModifyTime(DateUtils.getNowDate());
        return supplierDelStateMapper.updateSupplierDelState(supplierDelState);
    }

    /**
     * 批量删除看板配送单
     * 
     * @param ids 需要删除的看板配送单主键
     * @return 结果
     */
    @Override
    public int deleteSupplierDelStateByIds(String ids)
    {
        return supplierDelStateMapper.deleteSupplierDelStateByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除看板配送单信息
     * 
     * @param id 看板配送单主键
     * @return 结果
     */
    @Override
    public int deleteSupplierDelStateById(Long id)
    {
        return supplierDelStateMapper.deleteSupplierDelStateById(id);
    }
}
