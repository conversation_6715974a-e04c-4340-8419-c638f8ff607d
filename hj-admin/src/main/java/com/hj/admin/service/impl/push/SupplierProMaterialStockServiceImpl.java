package com.hj.admin.service.impl.push;

import com.hj.admin.domain.push.SupplierProMaterialStock;
import com.hj.admin.mapper.SupplierProMaterialStockMapper;
import com.hj.admin.service.push.ISupplierProMaterialStockService;
import com.hj.common.core.text.Convert;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 来料检验数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class SupplierProMaterialStockServiceImpl implements ISupplierProMaterialStockService
{
    @Resource
    private SupplierProMaterialStockMapper supplierProMaterialStockMapper;

    /**
     * 查询来料检验数据
     * 
     * @param id 来料检验数据主键
     * @return 来料检验数据
     */
    @Override
    public SupplierProMaterialStock selectSupplierProMaterialStockById(Long id)
    {
        return supplierProMaterialStockMapper.selectSupplierProMaterialStockById(id);
    }

    /**
     * 查询来料检验数据列表
     * 
     * @param supplierProMaterialStock 来料检验数据
     * @return 来料检验数据
     */
    @Override
    public List<SupplierProMaterialStock> selectSupplierProMaterialStockList(SupplierProMaterialStock supplierProMaterialStock)
    {
        return supplierProMaterialStockMapper.selectSupplierProMaterialStockList(supplierProMaterialStock);
    }

    /**
     * 新增来料检验数据
     * 
     * @param supplierProMaterialStock 来料检验数据
     * @return 结果
     */
    @Override
    public int insertSupplierProMaterialStock(SupplierProMaterialStock supplierProMaterialStock)
    {
        return supplierProMaterialStockMapper.insertSupplierProMaterialStock(supplierProMaterialStock);
    }

    /**
     * 修改来料检验数据
     * 
     * @param supplierProMaterialStock 来料检验数据
     * @return 结果
     */
    @Override
    public int updateSupplierProMaterialStock(SupplierProMaterialStock supplierProMaterialStock)
    {
        return supplierProMaterialStockMapper.updateSupplierProMaterialStock(supplierProMaterialStock);
    }

    /**
     * 批量删除来料检验数据
     * 
     * @param ids 需要删除的来料检验数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProMaterialStockByIds(String ids)
    {
        return supplierProMaterialStockMapper.deleteSupplierProMaterialStockByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除来料检验数据信息
     * 
     * @param id 来料检验数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProMaterialStockById(Long id)
    {
        return supplierProMaterialStockMapper.deleteSupplierProMaterialStockById(id);
    }
}
