package com.hj.admin.service.impl.push;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierProMaterialDataMapper;
import com.hj.admin.domain.push.SupplierProMaterialData;
import com.hj.admin.service.push.ISupplierProMaterialDataService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 物料主数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProMaterialDataServiceImpl implements ISupplierProMaterialDataService 
{
    @Resource
    private SupplierProMaterialDataMapper supplierProMaterialDataMapper;

    /**
     * 查询物料主数据
     * 
     * @param id 物料主数据主键
     * @return 物料主数据
     */
    @Override
    public SupplierProMaterialData selectSupplierProMaterialDataById(Long id)
    {
        return supplierProMaterialDataMapper.selectSupplierProMaterialDataById(id);
    }

    /**
     * 查询物料主数据列表
     * 
     * @param supplierProMaterialData 物料主数据
     * @return 物料主数据
     */
    @Override
    public List<SupplierProMaterialData> selectSupplierProMaterialDataList(SupplierProMaterialData supplierProMaterialData)
    {
        return supplierProMaterialDataMapper.selectSupplierProMaterialDataList(supplierProMaterialData);
    }

    /**
     * 新增物料主数据
     * 
     * @param supplierProMaterialData 物料主数据
     * @return 结果
     */
    @Override
    public int insertSupplierProMaterialData(SupplierProMaterialData supplierProMaterialData)
    {
        return supplierProMaterialDataMapper.insertSupplierProMaterialData(supplierProMaterialData);
    }

    /**
     * 修改物料主数据
     * 
     * @param supplierProMaterialData 物料主数据
     * @return 结果
     */
    @Override
    public int updateSupplierProMaterialData(SupplierProMaterialData supplierProMaterialData)
    {
        return supplierProMaterialDataMapper.updateSupplierProMaterialData(supplierProMaterialData);
    }

    /**
     * 批量删除物料主数据
     * 
     * @param ids 需要删除的物料主数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProMaterialDataByIds(String ids)
    {
        return supplierProMaterialDataMapper.deleteSupplierProMaterialDataByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除物料主数据信息
     * 
     * @param id 物料主数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProMaterialDataById(Long id)
    {
        return supplierProMaterialDataMapper.deleteSupplierProMaterialDataById(id);
    }
}
