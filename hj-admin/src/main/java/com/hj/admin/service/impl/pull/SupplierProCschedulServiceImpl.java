package com.hj.admin.service.impl.pull;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierProCschedulMapper;
import com.hj.admin.domain.pull.SupplierProCschedul;
import com.hj.admin.service.pull.ISupplierProCschedulService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 排序供货Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProCschedulServiceImpl implements ISupplierProCschedulService 
{
    @Resource
    private SupplierProCschedulMapper supplierProCschedulMapper;

    /**
     * 查询排序供货
     * 
     * @param id 排序供货主键
     * @return 排序供货
     */
    @Override
    public SupplierProCschedul selectSupplierProCschedulById(Long id)
    {
        return supplierProCschedulMapper.selectSupplierProCschedulById(id);
    }

    /**
     * 查询排序供货列表
     * 
     * @param supplierProCschedul 排序供货
     * @return 排序供货
     */
    @Override
    public List<SupplierProCschedul> selectSupplierProCschedulList(SupplierProCschedul supplierProCschedul)
    {
        return supplierProCschedulMapper.selectSupplierProCschedulList(supplierProCschedul);
    }

    /**
     * 新增排序供货
     * 
     * @param supplierProCschedul 排序供货
     * @return 结果
     */
    @Override
    public int insertSupplierProCschedul(SupplierProCschedul supplierProCschedul)
    {
        supplierProCschedul.setInsertTime(DateUtils.getNowDate());
        return supplierProCschedulMapper.insertSupplierProCschedul(supplierProCschedul);
    }

    /**
     * 修改排序供货
     * 
     * @param supplierProCschedul 排序供货
     * @return 结果
     */
    @Override
    public int updateSupplierProCschedul(SupplierProCschedul supplierProCschedul)
    {
        supplierProCschedul.setModifyTime(DateUtils.getNowDate());
        return supplierProCschedulMapper.updateSupplierProCschedul(supplierProCschedul);
    }

    /**
     * 批量删除排序供货
     * 
     * @param ids 需要删除的排序供货主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProCschedulByIds(String ids)
    {
        return supplierProCschedulMapper.deleteSupplierProCschedulByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除排序供货信息
     * 
     * @param id 排序供货主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProCschedulById(Long id)
    {
        return supplierProCschedulMapper.deleteSupplierProCschedulById(id);
    }
}
