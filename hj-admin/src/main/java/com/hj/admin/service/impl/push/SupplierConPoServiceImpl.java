package com.hj.admin.service.impl.push;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierConPoMapper;
import com.hj.admin.domain.push.SupplierConPo;
import com.hj.admin.service.push.ISupplierConPoService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 采购订单风险确认Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierConPoServiceImpl implements ISupplierConPoService 
{
    @Resource
    private SupplierConPoMapper supplierConPoMapper;

    /**
     * 查询采购订单风险确认
     * 
     * @param id 采购订单风险确认主键
     * @return 采购订单风险确认
     */
    @Override
    public SupplierConPo selectSupplierConPoById(Long id)
    {
        return supplierConPoMapper.selectSupplierConPoById(id);
    }

    /**
     * 查询采购订单风险确认列表
     * 
     * @param supplierConPo 采购订单风险确认
     * @return 采购订单风险确认
     */
    @Override
    public List<SupplierConPo> selectSupplierConPoList(SupplierConPo supplierConPo)
    {
        return supplierConPoMapper.selectSupplierConPoList(supplierConPo);
    }

    /**
     * 新增采购订单风险确认
     * 
     * @param supplierConPo 采购订单风险确认
     * @return 结果
     */
    @Override
    public int insertSupplierConPo(SupplierConPo supplierConPo)
    {
        return supplierConPoMapper.insertSupplierConPo(supplierConPo);
    }

    /**
     * 修改采购订单风险确认
     * 
     * @param supplierConPo 采购订单风险确认
     * @return 结果
     */
    @Override
    public int updateSupplierConPo(SupplierConPo supplierConPo)
    {
        return supplierConPoMapper.updateSupplierConPo(supplierConPo);
    }

    /**
     * 批量删除采购订单风险确认
     * 
     * @param ids 需要删除的采购订单风险确认主键
     * @return 结果
     */
    @Override
    public int deleteSupplierConPoByIds(String ids)
    {
        return supplierConPoMapper.deleteSupplierConPoByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除采购订单风险确认信息
     * 
     * @param id 采购订单风险确认主键
     * @return 结果
     */
    @Override
    public int deleteSupplierConPoById(Long id)
    {
        return supplierConPoMapper.deleteSupplierConPoById(id);
    }
}
