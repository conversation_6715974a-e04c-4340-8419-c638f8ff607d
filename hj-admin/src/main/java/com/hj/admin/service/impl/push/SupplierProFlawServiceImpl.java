package com.hj.admin.service.impl.push;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierProFlawMapper;
import com.hj.admin.domain.push.SupplierProFlaw;
import com.hj.admin.service.push.ISupplierProFlawService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 缺陷业务数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProFlawServiceImpl implements ISupplierProFlawService 
{
    @Resource
    private SupplierProFlawMapper supplierProFlawMapper;

    /**
     * 查询缺陷业务数据
     * 
     * @param id 缺陷业务数据主键
     * @return 缺陷业务数据
     */
    @Override
    public SupplierProFlaw selectSupplierProFlawById(Long id)
    {
        return supplierProFlawMapper.selectSupplierProFlawById(id);
    }

    /**
     * 查询缺陷业务数据列表
     * 
     * @param supplierProFlaw 缺陷业务数据
     * @return 缺陷业务数据
     */
    @Override
    public List<SupplierProFlaw> selectSupplierProFlawList(SupplierProFlaw supplierProFlaw)
    {
        return supplierProFlawMapper.selectSupplierProFlawList(supplierProFlaw);
    }

    /**
     * 新增缺陷业务数据
     * 
     * @param supplierProFlaw 缺陷业务数据
     * @return 结果
     */
    @Override
    public int insertSupplierProFlaw(SupplierProFlaw supplierProFlaw)
    {
        return supplierProFlawMapper.insertSupplierProFlaw(supplierProFlaw);
    }

    /**
     * 修改缺陷业务数据
     * 
     * @param supplierProFlaw 缺陷业务数据
     * @return 结果
     */
    @Override
    public int updateSupplierProFlaw(SupplierProFlaw supplierProFlaw)
    {
        return supplierProFlawMapper.updateSupplierProFlaw(supplierProFlaw);
    }

    /**
     * 批量删除缺陷业务数据
     * 
     * @param ids 需要删除的缺陷业务数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProFlawByIds(String ids)
    {
        return supplierProFlawMapper.deleteSupplierProFlawByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除缺陷业务数据信息
     * 
     * @param id 缺陷业务数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProFlawById(Long id)
    {
        return supplierProFlawMapper.deleteSupplierProFlawById(id);
    }
}
