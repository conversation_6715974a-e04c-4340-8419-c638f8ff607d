package com.hj.admin.service.impl.pull;

import java.util.List;


import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierInvDataMapper;
import com.hj.admin.domain.pull.SupplierInvData;
import com.hj.admin.service.pull.ISupplierInvDataService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 奇瑞RDC共享库存Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierInvDataServiceImpl implements ISupplierInvDataService 
{
    @Resource
    private SupplierInvDataMapper supplierInvDataMapper;

    /**
     * 查询奇瑞RDC共享库存
     * 
     * @param id 奇瑞RDC共享库存主键
     * @return 奇瑞RDC共享库存
     */
    @Override
    public SupplierInvData selectSupplierInvDataById(Long id)
    {
        return supplierInvDataMapper.selectSupplierInvDataById(id);
    }

    /**
     * 查询奇瑞RDC共享库存列表
     * 
     * @param supplierInvData 奇瑞RDC共享库存
     * @return 奇瑞RDC共享库存
     */
    @Override
    public List<SupplierInvData> selectSupplierInvDataList(SupplierInvData supplierInvData)
    {
        return supplierInvDataMapper.selectSupplierInvDataList(supplierInvData);
    }

    /**
     * 新增奇瑞RDC共享库存
     * 
     * @param supplierInvData 奇瑞RDC共享库存
     * @return 结果
     */
    @Override
    public int insertSupplierInvData(SupplierInvData supplierInvData)
    {
        supplierInvData.setInsertTime(DateUtils.getNowDate());
        return supplierInvDataMapper.insertSupplierInvData(supplierInvData);
    }

    /**
     * 修改奇瑞RDC共享库存
     * 
     * @param supplierInvData 奇瑞RDC共享库存
     * @return 结果
     */
    @Override
    public int updateSupplierInvData(SupplierInvData supplierInvData)
    {
        supplierInvData.setModifyTime(DateUtils.getNowDate());
        return supplierInvDataMapper.updateSupplierInvData(supplierInvData);
    }

    /**
     * 批量删除奇瑞RDC共享库存
     * 
     * @param ids 需要删除的奇瑞RDC共享库存主键
     * @return 结果
     */
    @Override
    public int deleteSupplierInvDataByIds(String ids)
    {
        return supplierInvDataMapper.deleteSupplierInvDataByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除奇瑞RDC共享库存信息
     * 
     * @param id 奇瑞RDC共享库存主键
     * @return 结果
     */
    @Override
    public int deleteSupplierInvDataById(Long id)
    {
        return supplierInvDataMapper.deleteSupplierInvDataById(id);
    }
}
