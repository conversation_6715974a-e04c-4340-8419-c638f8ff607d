package com.hj.admin.service.impl.push;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierConMmrpMapper;
import com.hj.admin.domain.push.SupplierConMmrp;
import com.hj.admin.service.push.ISupplierConMmrpService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * M+6月物料需求计划风险确认Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierConMmrpServiceImpl implements ISupplierConMmrpService 
{
    @Resource
    private SupplierConMmrpMapper supplierConMmrpMapper;

    /**
     * 查询M+6月物料需求计划风险确认
     * 
     * @param id M+6月物料需求计划风险确认主键
     * @return M+6月物料需求计划风险确认
     */
    @Override
    public SupplierConMmrp selectSupplierConMmrpById(Long id)
    {
        return supplierConMmrpMapper.selectSupplierConMmrpById(id);
    }

    /**
     * 查询M+6月物料需求计划风险确认列表
     * 
     * @param supplierConMmrp M+6月物料需求计划风险确认
     * @return M+6月物料需求计划风险确认
     */
    @Override
    public List<SupplierConMmrp> selectSupplierConMmrpList(SupplierConMmrp supplierConMmrp)
    {
        return supplierConMmrpMapper.selectSupplierConMmrpList(supplierConMmrp);
    }

    /**
     * 新增M+6月物料需求计划风险确认
     * 
     * @param supplierConMmrp M+6月物料需求计划风险确认
     * @return 结果
     */
    @Override
    public int insertSupplierConMmrp(SupplierConMmrp supplierConMmrp)
    {
        return supplierConMmrpMapper.insertSupplierConMmrp(supplierConMmrp);
    }

    /**
     * 修改M+6月物料需求计划风险确认
     * 
     * @param supplierConMmrp M+6月物料需求计划风险确认
     * @return 结果
     */
    @Override
    public int updateSupplierConMmrp(SupplierConMmrp supplierConMmrp)
    {
        return supplierConMmrpMapper.updateSupplierConMmrp(supplierConMmrp);
    }

    /**
     * 批量删除M+6月物料需求计划风险确认
     * 
     * @param ids 需要删除的M+6月物料需求计划风险确认主键
     * @return 结果
     */
    @Override
    public int deleteSupplierConMmrpByIds(String ids)
    {
        return supplierConMmrpMapper.deleteSupplierConMmrpByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除M+6月物料需求计划风险确认信息
     * 
     * @param id M+6月物料需求计划风险确认主键
     * @return 结果
     */
    @Override
    public int deleteSupplierConMmrpById(Long id)
    {
        return supplierConMmrpMapper.deleteSupplierConMmrpById(id);
    }
}
