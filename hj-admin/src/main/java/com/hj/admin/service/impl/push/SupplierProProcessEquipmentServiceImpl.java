package com.hj.admin.service.impl.push;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierProProcessEquipmentMapper;
import com.hj.admin.domain.push.SupplierProProcessEquipment;
import com.hj.admin.service.push.ISupplierProProcessEquipmentService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 工艺装备Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProProcessEquipmentServiceImpl implements ISupplierProProcessEquipmentService 
{
    @Resource
    private SupplierProProcessEquipmentMapper supplierProProcessEquipmentMapper;

    /**
     * 查询工艺装备
     * 
     * @param id 工艺装备主键
     * @return 工艺装备
     */
    @Override
    public SupplierProProcessEquipment selectSupplierProProcessEquipmentById(Long id)
    {
        return supplierProProcessEquipmentMapper.selectSupplierProProcessEquipmentById(id);
    }

    /**
     * 查询工艺装备列表
     * 
     * @param supplierProProcessEquipment 工艺装备
     * @return 工艺装备
     */
    @Override
    public List<SupplierProProcessEquipment> selectSupplierProProcessEquipmentList(SupplierProProcessEquipment supplierProProcessEquipment)
    {
        return supplierProProcessEquipmentMapper.selectSupplierProProcessEquipmentList(supplierProProcessEquipment);
    }

    /**
     * 新增工艺装备
     * 
     * @param supplierProProcessEquipment 工艺装备
     * @return 结果
     */
    @Override
    public int insertSupplierProProcessEquipment(SupplierProProcessEquipment supplierProProcessEquipment)
    {
        return supplierProProcessEquipmentMapper.insertSupplierProProcessEquipment(supplierProProcessEquipment);
    }

    /**
     * 修改工艺装备
     * 
     * @param supplierProProcessEquipment 工艺装备
     * @return 结果
     */
    @Override
    public int updateSupplierProProcessEquipment(SupplierProProcessEquipment supplierProProcessEquipment)
    {
        return supplierProProcessEquipmentMapper.updateSupplierProProcessEquipment(supplierProProcessEquipment);
    }

    /**
     * 批量删除工艺装备
     * 
     * @param ids 需要删除的工艺装备主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProProcessEquipmentByIds(String ids)
    {
        return supplierProProcessEquipmentMapper.deleteSupplierProProcessEquipmentByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除工艺装备信息
     * 
     * @param id 工艺装备主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProProcessEquipmentById(Long id)
    {
        return supplierProProcessEquipmentMapper.deleteSupplierProProcessEquipmentById(id);
    }
}
