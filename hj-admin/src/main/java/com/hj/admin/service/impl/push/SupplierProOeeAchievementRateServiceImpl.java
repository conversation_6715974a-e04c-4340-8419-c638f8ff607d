package com.hj.admin.service.impl.push;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierProOeeAchievementRateMapper;
import com.hj.admin.domain.push.SupplierProOeeAchievementRate;
import com.hj.admin.service.push.ISupplierProOeeAchievementRateService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 设备OEE达成率Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProOeeAchievementRateServiceImpl implements ISupplierProOeeAchievementRateService 
{
    @Resource
    private SupplierProOeeAchievementRateMapper supplierProOeeAchievementRateMapper;

    /**
     * 查询设备OEE达成率
     * 
     * @param id 设备OEE达成率主键
     * @return 设备OEE达成率
     */
    @Override
    public SupplierProOeeAchievementRate selectSupplierProOeeAchievementRateById(Long id)
    {
        return supplierProOeeAchievementRateMapper.selectSupplierProOeeAchievementRateById(id);
    }

    /**
     * 查询设备OEE达成率列表
     * 
     * @param supplierProOeeAchievementRate 设备OEE达成率
     * @return 设备OEE达成率
     */
    @Override
    public List<SupplierProOeeAchievementRate> selectSupplierProOeeAchievementRateList(SupplierProOeeAchievementRate supplierProOeeAchievementRate)
    {
        return supplierProOeeAchievementRateMapper.selectSupplierProOeeAchievementRateList(supplierProOeeAchievementRate);
    }

    /**
     * 新增设备OEE达成率
     * 
     * @param supplierProOeeAchievementRate 设备OEE达成率
     * @return 结果
     */
    @Override
    public int insertSupplierProOeeAchievementRate(SupplierProOeeAchievementRate supplierProOeeAchievementRate)
    {
        return supplierProOeeAchievementRateMapper.insertSupplierProOeeAchievementRate(supplierProOeeAchievementRate);
    }

    /**
     * 修改设备OEE达成率
     * 
     * @param supplierProOeeAchievementRate 设备OEE达成率
     * @return 结果
     */
    @Override
    public int updateSupplierProOeeAchievementRate(SupplierProOeeAchievementRate supplierProOeeAchievementRate)
    {
        return supplierProOeeAchievementRateMapper.updateSupplierProOeeAchievementRate(supplierProOeeAchievementRate);
    }

    /**
     * 批量删除设备OEE达成率
     * 
     * @param ids 需要删除的设备OEE达成率主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProOeeAchievementRateByIds(String ids)
    {
        return supplierProOeeAchievementRateMapper.deleteSupplierProOeeAchievementRateByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除设备OEE达成率信息
     * 
     * @param id 设备OEE达成率主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProOeeAchievementRateById(Long id)
    {
        return supplierProOeeAchievementRateMapper.deleteSupplierProOeeAchievementRateById(id);
    }
}
