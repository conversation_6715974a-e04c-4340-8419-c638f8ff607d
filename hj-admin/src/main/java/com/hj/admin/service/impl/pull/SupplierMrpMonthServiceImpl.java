package com.hj.admin.service.impl.pull;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierMrpMonthMapper;
import com.hj.admin.domain.pull.SupplierMrpMonth;
import com.hj.admin.service.pull.ISupplierMrpMonthService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * M+6月物料需求计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierMrpMonthServiceImpl implements ISupplierMrpMonthService 
{
    @Resource
    private SupplierMrpMonthMapper supplierMrpMonthMapper;

    /**
     * 查询M+6月物料需求计划
     * 
     * @param id M+6月物料需求计划主键
     * @return M+6月物料需求计划
     */
    @Override
    public SupplierMrpMonth selectSupplierMrpMonthById(Long id)
    {
        return supplierMrpMonthMapper.selectSupplierMrpMonthById(id);
    }

    /**
     * 查询M+6月物料需求计划列表
     * 
     * @param supplierMrpMonth M+6月物料需求计划
     * @return M+6月物料需求计划
     */
    @Override
    public List<SupplierMrpMonth> selectSupplierMrpMonthList(SupplierMrpMonth supplierMrpMonth)
    {
        return supplierMrpMonthMapper.selectSupplierMrpMonthList(supplierMrpMonth);
    }

    /**
     * 新增M+6月物料需求计划
     * 
     * @param supplierMrpMonth M+6月物料需求计划
     * @return 结果
     */
    @Override
    public int insertSupplierMrpMonth(SupplierMrpMonth supplierMrpMonth)
    {
        supplierMrpMonth.setInsertTime(DateUtils.getNowDate());
        return supplierMrpMonthMapper.insertSupplierMrpMonth(supplierMrpMonth);
    }

    /**
     * 修改M+6月物料需求计划
     * 
     * @param supplierMrpMonth M+6月物料需求计划
     * @return 结果
     */
    @Override
    public int updateSupplierMrpMonth(SupplierMrpMonth supplierMrpMonth)
    {
        supplierMrpMonth.setModifyTime(DateUtils.getNowDate());
        return supplierMrpMonthMapper.updateSupplierMrpMonth(supplierMrpMonth);
    }

    /**
     * 批量删除M+6月物料需求计划
     * 
     * @param ids 需要删除的M+6月物料需求计划主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMrpMonthByIds(String ids)
    {
        return supplierMrpMonthMapper.deleteSupplierMrpMonthByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除M+6月物料需求计划信息
     * 
     * @param id M+6月物料需求计划主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMrpMonthById(Long id)
    {
        return supplierMrpMonthMapper.deleteSupplierMrpMonthById(id);
    }
}
