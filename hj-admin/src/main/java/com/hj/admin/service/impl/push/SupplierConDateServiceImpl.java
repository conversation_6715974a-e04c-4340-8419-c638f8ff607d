package com.hj.admin.service.impl.push;

import java.util.List;


import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierConDateMapper;
import com.hj.admin.domain.push.SupplierConDate;
import com.hj.admin.service.push.ISupplierConDateService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 日物料需求计划风险确认Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierConDateServiceImpl implements ISupplierConDateService 
{
    @Resource
    private SupplierConDateMapper supplierConDateMapper;

    /**
     * 查询日物料需求计划风险确认
     * 
     * @param id 日物料需求计划风险确认主键
     * @return 日物料需求计划风险确认
     */
    @Override
    public SupplierConDate selectSupplierConDateById(Long id)
    {
        return supplierConDateMapper.selectSupplierConDateById(id);
    }

    /**
     * 查询日物料需求计划风险确认列表
     * 
     * @param supplierConDate 日物料需求计划风险确认
     * @return 日物料需求计划风险确认
     */
    @Override
    public List<SupplierConDate> selectSupplierConDateList(SupplierConDate supplierConDate)
    {
        return supplierConDateMapper.selectSupplierConDateList(supplierConDate);
    }

    /**
     * 新增日物料需求计划风险确认
     * 
     * @param supplierConDate 日物料需求计划风险确认
     * @return 结果
     */
    @Override
    public int insertSupplierConDate(SupplierConDate supplierConDate)
    {
        return supplierConDateMapper.insertSupplierConDate(supplierConDate);
    }

    /**
     * 修改日物料需求计划风险确认
     * 
     * @param supplierConDate 日物料需求计划风险确认
     * @return 结果
     */
    @Override
    public int updateSupplierConDate(SupplierConDate supplierConDate)
    {
        return supplierConDateMapper.updateSupplierConDate(supplierConDate);
    }

    /**
     * 批量删除日物料需求计划风险确认
     * 
     * @param ids 需要删除的日物料需求计划风险确认主键
     * @return 结果
     */
    @Override
    public int deleteSupplierConDateByIds(String ids)
    {
        return supplierConDateMapper.deleteSupplierConDateByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除日物料需求计划风险确认信息
     * 
     * @param id 日物料需求计划风险确认主键
     * @return 结果
     */
    @Override
    public int deleteSupplierConDateById(Long id)
    {
        return supplierConDateMapper.deleteSupplierConDateById(id);
    }
}
