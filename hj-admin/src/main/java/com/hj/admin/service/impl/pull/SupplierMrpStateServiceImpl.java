package com.hj.admin.service.impl.pull;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.SupplierMrpStateMapper;
import com.hj.admin.domain.pull.SupplierMrpState;
import com.hj.admin.service.pull.ISupplierMrpStateService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 日MRP状态监控Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierMrpStateServiceImpl implements ISupplierMrpStateService 
{
    @Resource
    private SupplierMrpStateMapper supplierMrpStateMapper;

    /**
     * 查询日MRP状态监控
     * 
     * @param id 日MRP状态监控主键
     * @return 日MRP状态监控
     */
    @Override
    public SupplierMrpState selectSupplierMrpStateById(Long id)
    {
        return supplierMrpStateMapper.selectSupplierMrpStateById(id);
    }

    /**
     * 查询日MRP状态监控列表
     * 
     * @param supplierMrpState 日MRP状态监控
     * @return 日MRP状态监控
     */
    @Override
    public List<SupplierMrpState> selectSupplierMrpStateList(SupplierMrpState supplierMrpState)
    {
        return supplierMrpStateMapper.selectSupplierMrpStateList(supplierMrpState);
    }

    /**
     * 新增日MRP状态监控
     * 
     * @param supplierMrpState 日MRP状态监控
     * @return 结果
     */
    @Override
    public int insertSupplierMrpState(SupplierMrpState supplierMrpState)
    {
        supplierMrpState.setInsertTime(DateUtils.getNowDate());
        return supplierMrpStateMapper.insertSupplierMrpState(supplierMrpState);
    }

    /**
     * 修改日MRP状态监控
     * 
     * @param supplierMrpState 日MRP状态监控
     * @return 结果
     */
    @Override
    public int updateSupplierMrpState(SupplierMrpState supplierMrpState)
    {
        supplierMrpState.setModifyTime(DateUtils.getNowDate());
        return supplierMrpStateMapper.updateSupplierMrpState(supplierMrpState);
    }

    /**
     * 批量删除日MRP状态监控
     * 
     * @param ids 需要删除的日MRP状态监控主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMrpStateByIds(String ids)
    {
        return supplierMrpStateMapper.deleteSupplierMrpStateByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除日MRP状态监控信息
     * 
     * @param id 日MRP状态监控主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMrpStateById(Long id)
    {
        return supplierMrpStateMapper.deleteSupplierMrpStateById(id);
    }
}
