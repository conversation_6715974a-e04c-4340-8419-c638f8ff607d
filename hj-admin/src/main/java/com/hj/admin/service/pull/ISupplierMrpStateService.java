package com.hj.admin.service.pull;

import java.util.List;
import com.hj.admin.domain.pull.SupplierMrpState;

/**
 * 日MRP状态监控Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierMrpStateService 
{
    /**
     * 查询日MRP状态监控
     * 
     * @param id 日MRP状态监控主键
     * @return 日MRP状态监控
     */
    public SupplierMrpState selectSupplierMrpStateById(Long id);

    /**
     * 查询日MRP状态监控列表
     * 
     * @param supplierMrpState 日MRP状态监控
     * @return 日MRP状态监控集合
     */
    public List<SupplierMrpState> selectSupplierMrpStateList(SupplierMrpState supplierMrpState);

    /**
     * 新增日MRP状态监控
     * 
     * @param supplierMrpState 日MRP状态监控
     * @return 结果
     */
    public int insertSupplierMrpState(SupplierMrpState supplierMrpState);

    /**
     * 修改日MRP状态监控
     * 
     * @param supplierMrpState 日MRP状态监控
     * @return 结果
     */
    public int updateSupplierMrpState(SupplierMrpState supplierMrpState);

    /**
     * 批量删除日MRP状态监控
     * 
     * @param ids 需要删除的日MRP状态监控主键集合
     * @return 结果
     */
    public int deleteSupplierMrpStateByIds(String ids);

    /**
     * 删除日MRP状态监控信息
     * 
     * @param id 日MRP状态监控主键
     * @return 结果
     */
    public int deleteSupplierMrpStateById(Long id);
}
