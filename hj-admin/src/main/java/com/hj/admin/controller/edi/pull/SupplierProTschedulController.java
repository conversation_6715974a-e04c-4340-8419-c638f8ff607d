package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierProTschedul;
import com.hj.admin.service.pull.ISupplierProTschedulService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 过涂装未过总装Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/tschedul")
public class SupplierProTschedulController extends BaseController
{
    private String prefix = "edi/tschedul";

    @Resource
    private ISupplierProTschedulService supplierProTschedulService;

    @RequiresPermissions("edi:tschedul:view")
    @GetMapping()
    public String tschedul()
    {
        return prefix + "/tschedul";
    }

    /**
     * 查询过涂装未过总装列表
     */
    @RequiresPermissions("edi:tschedul:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProTschedul supplierProTschedul)
    {
        startPage();
        List<SupplierProTschedul> list = supplierProTschedulService.selectSupplierProTschedulList(supplierProTschedul);
        return getDataTable(list);
    }

    /**
     * 导出过涂装未过总装列表
     */
    @RequiresPermissions("edi:tschedul:export")
    @Log(title = "过涂装未过总装", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProTschedul supplierProTschedul)
    {
        List<SupplierProTschedul> list = supplierProTschedulService.selectSupplierProTschedulList(supplierProTschedul);
        ExcelUtil<SupplierProTschedul> util = new ExcelUtil<SupplierProTschedul>(SupplierProTschedul.class);
        return util.exportExcel(list, "过涂装未过总装数据");
    }

    /**
     * 新增过涂装未过总装
     */
    @RequiresPermissions("edi:tschedul:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存过涂装未过总装
     */
    @RequiresPermissions("edi:tschedul:add")
    @Log(title = "过涂装未过总装", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProTschedul supplierProTschedul)
    {
        return toAjax(supplierProTschedulService.insertSupplierProTschedul(supplierProTschedul));
    }

    /**
     * 修改过涂装未过总装
     */
    @RequiresPermissions("edi:tschedul:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProTschedul supplierProTschedul = supplierProTschedulService.selectSupplierProTschedulById(id);
        mmap.put("supplierProTschedul", supplierProTschedul);
        return prefix + "/edit";
    }

    /**
     * 修改保存过涂装未过总装
     */
    @RequiresPermissions("edi:tschedul:edit")
    @Log(title = "过涂装未过总装", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProTschedul supplierProTschedul)
    {
        return toAjax(supplierProTschedulService.updateSupplierProTschedul(supplierProTschedul));
    }

    /**
     * 删除过涂装未过总装
     */
    @RequiresPermissions("edi:tschedul:remove")
    @Log(title = "过涂装未过总装", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProTschedulService.deleteSupplierProTschedulByIds(ids));
    }
}
