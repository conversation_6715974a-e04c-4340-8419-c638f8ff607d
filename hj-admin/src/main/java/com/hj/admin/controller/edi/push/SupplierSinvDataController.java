package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierSinvData;
import com.hj.admin.service.push.ISupplierSinvDataService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * 供应商共享库存Controller
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Controller
@RequestMapping("/sinv/data")
public class SupplierSinvDataController extends BaseController {
    private final String prefix = "edi/sinv-data";

    @Resource
    private ISupplierSinvDataService supplierSinvDataService;

    @RequiresPermissions("system:data:view")
    @GetMapping()
    public String data() {
        return prefix + "/data";
    }

    /**
     * 查询供应商共享库存列表
     */
    @RequiresPermissions("system:data:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierSinvData supplierSinvData) {
        startPage();
        List<SupplierSinvData> list = supplierSinvDataService.selectSupplierSinvDataList(supplierSinvData);
        return getDataTable(list);
    }

    /**
     * 导出供应商共享库存列表
     */
    @RequiresPermissions("system:data:export")
    @Log(title = "供应商共享库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierSinvData supplierSinvData) {
        List<SupplierSinvData> list = supplierSinvDataService.selectSupplierSinvDataList(supplierSinvData);
        ExcelUtil<SupplierSinvData> util = new ExcelUtil<SupplierSinvData>(SupplierSinvData.class);
        return util.exportExcel(list, "供应商共享库存数据");
    }

    /**
     * 新增供应商共享库存
     */
    @RequiresPermissions("system:data:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存供应商共享库存
     */
    @RequiresPermissions("system:data:add")
    @Log(title = "供应商共享库存", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierSinvData supplierSinvData) {
        return toAjax(supplierSinvDataService.insertSupplierSinvData(supplierSinvData));
    }

    /**
     * 修改供应商共享库存
     */
    @RequiresPermissions("system:data:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierSinvData supplierSinvData = supplierSinvDataService.selectSupplierSinvDataById(id);
        mmap.put("supplierSinvData", supplierSinvData);
        return prefix + "/edit";
    }

    /**
     * 修改保存供应商共享库存
     */
    @RequiresPermissions("system:data:edit")
    @Log(title = "供应商共享库存", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierSinvData supplierSinvData) {
        return toAjax(supplierSinvDataService.updateSupplierSinvData(supplierSinvData));
    }

    /**
     * 删除供应商共享库存
     */
    @RequiresPermissions("system:data:remove")
    @Log(title = "供应商共享库存", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierSinvDataService.deleteSupplierSinvDataByIds(ids));
    }
}
