package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierProAttachmentData;
import com.hj.admin.service.push.ISupplierProAttachmentDataService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 附件类数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/proAttachmentData")
public class SupplierProAttachmentDataController extends BaseController
{
    private String prefix = "edi/proAttachmentData";

    @Resource
    private ISupplierProAttachmentDataService supplierProAttachmentDataService;

    @RequiresPermissions("edi:data:view")
    @GetMapping()
    public String data()
    {
        return prefix + "/data";
    }

    /**
     * 查询附件类数据列表
     */
    @RequiresPermissions("edi:data:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProAttachmentData supplierProAttachmentData)
    {
        startPage();
        List<SupplierProAttachmentData> list = supplierProAttachmentDataService.selectSupplierProAttachmentDataList(supplierProAttachmentData);
        return getDataTable(list);
    }

    /**
     * 导出附件类数据列表
     */
    @RequiresPermissions("edi:data:export")
    @Log(title = "附件类数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProAttachmentData supplierProAttachmentData)
    {
        List<SupplierProAttachmentData> list = supplierProAttachmentDataService.selectSupplierProAttachmentDataList(supplierProAttachmentData);
        ExcelUtil<SupplierProAttachmentData> util = new ExcelUtil<SupplierProAttachmentData>(SupplierProAttachmentData.class);
        return util.exportExcel(list, "附件类数据数据");
    }

    /**
     * 新增附件类数据
     */
    @RequiresPermissions("edi:data:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存附件类数据
     */
    @RequiresPermissions("edi:data:add")
    @Log(title = "附件类数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProAttachmentData supplierProAttachmentData)
    {
        return toAjax(supplierProAttachmentDataService.insertSupplierProAttachmentData(supplierProAttachmentData));
    }

    /**
     * 修改附件类数据
     */
    @RequiresPermissions("edi:data:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProAttachmentData supplierProAttachmentData = supplierProAttachmentDataService.selectSupplierProAttachmentDataById(id);
        mmap.put("supplierProAttachmentData", supplierProAttachmentData);
        return prefix + "/edit";
    }

    /**
     * 修改保存附件类数据
     */
    @RequiresPermissions("edi:data:edit")
    @Log(title = "附件类数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProAttachmentData supplierProAttachmentData)
    {
        return toAjax(supplierProAttachmentDataService.updateSupplierProAttachmentData(supplierProAttachmentData));
    }

    /**
     * 删除附件类数据
     */
    @RequiresPermissions("edi:data:remove")
    @Log(title = "附件类数据", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProAttachmentDataService.deleteSupplierProAttachmentDataByIds(ids));
    }
}
