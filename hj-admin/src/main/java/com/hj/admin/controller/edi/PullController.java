package com.hj.admin.controller.edi;

import com.hj.admin.domain.base.CheryPullRes;
import com.hj.admin.service.IDoRequestService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;
import java.util.Map;

@RestController
@RequestMapping("pull")
public class PullController {

    private final IDoRequestService doRequestService;

    public PullController(IDoRequestService doRequestService) {
        this.doRequestService = doRequestService;
    }

    @Value("${chery.path.pull.plannedLogistics}")
    private String plannedLogisticsPath;

    @PostMapping("supplierProPlaning")
    public CheryPullRes supplierProPlaning() {
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("date", "2025-08-01");
        map.put("pageSize", 1000);
        map.put("pageNum", 1);
        map.put("isForce", false);
        return doRequestService.pull(plannedLogisticsPath, map);
    }

}
