package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierProFlaw;
import com.hj.admin.service.push.ISupplierProFlawService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 缺陷业务数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/flaw")
public class SupplierProFlawController extends BaseController
{
    private String prefix = "edi/flaw";

    @Resource
    private ISupplierProFlawService supplierProFlawService;

    @RequiresPermissions("edi:flaw:view")
    @GetMapping()
    public String flaw()
    {
        return prefix + "/flaw";
    }

    /**
     * 查询缺陷业务数据列表
     */
    @RequiresPermissions("edi:flaw:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProFlaw supplierProFlaw)
    {
        startPage();
        List<SupplierProFlaw> list = supplierProFlawService.selectSupplierProFlawList(supplierProFlaw);
        return getDataTable(list);
    }

    /**
     * 导出缺陷业务数据列表
     */
    @RequiresPermissions("edi:flaw:export")
    @Log(title = "缺陷业务数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProFlaw supplierProFlaw)
    {
        List<SupplierProFlaw> list = supplierProFlawService.selectSupplierProFlawList(supplierProFlaw);
        ExcelUtil<SupplierProFlaw> util = new ExcelUtil<SupplierProFlaw>(SupplierProFlaw.class);
        return util.exportExcel(list, "缺陷业务数据数据");
    }

    /**
     * 新增缺陷业务数据
     */
    @RequiresPermissions("edi:flaw:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存缺陷业务数据
     */
    @RequiresPermissions("edi:flaw:add")
    @Log(title = "缺陷业务数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProFlaw supplierProFlaw)
    {
        return toAjax(supplierProFlawService.insertSupplierProFlaw(supplierProFlaw));
    }

    /**
     * 修改缺陷业务数据
     */
    @RequiresPermissions("edi:flaw:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProFlaw supplierProFlaw = supplierProFlawService.selectSupplierProFlawById(id);
        mmap.put("supplierProFlaw", supplierProFlaw);
        return prefix + "/edit";
    }

    /**
     * 修改保存缺陷业务数据
     */
    @RequiresPermissions("edi:flaw:edit")
    @Log(title = "缺陷业务数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProFlaw supplierProFlaw)
    {
        return toAjax(supplierProFlawService.updateSupplierProFlaw(supplierProFlaw));
    }

    /**
     * 删除缺陷业务数据
     */
    @RequiresPermissions("edi:flaw:remove")
    @Log(title = "缺陷业务数据", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProFlawService.deleteSupplierProFlawByIds(ids));
    }
}
