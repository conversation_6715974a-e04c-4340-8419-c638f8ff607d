package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierInfo;
import com.hj.admin.service.push.ISupplierInfoService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 供应商基础信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/com/info")
public class SupplierInfoController extends BaseController
{
    private String prefix = "com/info";

    @Resource
    private ISupplierInfoService supplierInfoService;

    @RequiresPermissions("com:info:view")
    @GetMapping()
    public String info()
    {
        return prefix + "/info";
    }

    /**
     * 查询供应商基础信息列表
     */
    @RequiresPermissions("com:info:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierInfo supplierInfo)
    {
        startPage();
        List<SupplierInfo> list = supplierInfoService.selectSupplierInfoList(supplierInfo);
        return getDataTable(list);
    }

    /**
     * 导出供应商基础信息列表
     */
    @RequiresPermissions("com:info:export")
    @Log(title = "供应商基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierInfo supplierInfo)
    {
        List<SupplierInfo> list = supplierInfoService.selectSupplierInfoList(supplierInfo);
        ExcelUtil<SupplierInfo> util = new ExcelUtil<SupplierInfo>(SupplierInfo.class);
        return util.exportExcel(list, "供应商基础信息数据");
    }

    /**
     * 新增供应商基础信息
     */
    @RequiresPermissions("com:info:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存供应商基础信息
     */
    @RequiresPermissions("com:info:add")
    @Log(title = "供应商基础信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierInfo supplierInfo)
    {
        return toAjax(supplierInfoService.insertSupplierInfo(supplierInfo));
    }

    /**
     * 修改供应商基础信息
     */
    @RequiresPermissions("com:info:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierInfo supplierInfo = supplierInfoService.selectSupplierInfoById(id);
        mmap.put("supplierInfo", supplierInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存供应商基础信息
     */
    @RequiresPermissions("com:info:edit")
    @Log(title = "供应商基础信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierInfo supplierInfo)
    {
        return toAjax(supplierInfoService.updateSupplierInfo(supplierInfo));
    }

    /**
     * 删除供应商基础信息
     */
    @RequiresPermissions("com:info:remove")
    @Log(title = "供应商基础信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierInfoService.deleteSupplierInfoByIds(ids));
    }
}
