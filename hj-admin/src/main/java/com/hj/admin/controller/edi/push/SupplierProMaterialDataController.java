package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierProMaterialData;
import com.hj.admin.service.push.ISupplierProMaterialDataService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 物料主数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/data")
public class SupplierProMaterialDataController extends BaseController
{
    private String prefix = "edi/data";

    @Resource
    private ISupplierProMaterialDataService supplierProMaterialDataService;

    @RequiresPermissions("edi:data:view")
    @GetMapping()
    public String data()
    {
        return prefix + "/data";
    }

    /**
     * 查询物料主数据列表
     */
    @RequiresPermissions("edi:data:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProMaterialData supplierProMaterialData)
    {
        startPage();
        List<SupplierProMaterialData> list = supplierProMaterialDataService.selectSupplierProMaterialDataList(supplierProMaterialData);
        return getDataTable(list);
    }

    /**
     * 导出物料主数据列表
     */
    @RequiresPermissions("edi:data:export")
    @Log(title = "物料主数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProMaterialData supplierProMaterialData)
    {
        List<SupplierProMaterialData> list = supplierProMaterialDataService.selectSupplierProMaterialDataList(supplierProMaterialData);
        ExcelUtil<SupplierProMaterialData> util = new ExcelUtil<SupplierProMaterialData>(SupplierProMaterialData.class);
        return util.exportExcel(list, "物料主数据数据");
    }

    /**
     * 新增物料主数据
     */
    @RequiresPermissions("edi:data:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存物料主数据
     */
    @RequiresPermissions("edi:data:add")
    @Log(title = "物料主数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProMaterialData supplierProMaterialData)
    {
        return toAjax(supplierProMaterialDataService.insertSupplierProMaterialData(supplierProMaterialData));
    }

    /**
     * 修改物料主数据
     */
    @RequiresPermissions("edi:data:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProMaterialData supplierProMaterialData = supplierProMaterialDataService.selectSupplierProMaterialDataById(id);
        mmap.put("supplierProMaterialData", supplierProMaterialData);
        return prefix + "/edit";
    }

    /**
     * 修改保存物料主数据
     */
    @RequiresPermissions("edi:data:edit")
    @Log(title = "物料主数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProMaterialData supplierProMaterialData)
    {
        return toAjax(supplierProMaterialDataService.updateSupplierProMaterialData(supplierProMaterialData));
    }

    /**
     * 删除物料主数据
     */
    @RequiresPermissions("edi:data:remove")
    @Log(title = "物料主数据", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProMaterialDataService.deleteSupplierProMaterialDataByIds(ids));
    }
}
