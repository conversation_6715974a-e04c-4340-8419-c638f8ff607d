package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierProProcess;
import com.hj.admin.service.push.ISupplierProProcessService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工艺Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/process")
public class SupplierProProcessController extends BaseController
{
    private String prefix = "edi/process";

    @Resource
    private ISupplierProProcessService supplierProProcessService;

    @RequiresPermissions("edi:process:view")
    @GetMapping()
    public String process()
    {
        return prefix + "/process";
    }

    /**
     * 查询工艺列表
     */
    @RequiresPermissions("edi:process:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProProcess supplierProProcess)
    {
        startPage();
        List<SupplierProProcess> list = supplierProProcessService.selectSupplierProProcessList(supplierProProcess);
        return getDataTable(list);
    }

    /**
     * 导出工艺列表
     */
    @RequiresPermissions("edi:process:export")
    @Log(title = "工艺", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProProcess supplierProProcess)
    {
        List<SupplierProProcess> list = supplierProProcessService.selectSupplierProProcessList(supplierProProcess);
        ExcelUtil<SupplierProProcess> util = new ExcelUtil<SupplierProProcess>(SupplierProProcess.class);
        return util.exportExcel(list, "工艺数据");
    }

    /**
     * 新增工艺
     */
    @RequiresPermissions("edi:process:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存工艺
     */
    @RequiresPermissions("edi:process:add")
    @Log(title = "工艺", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProProcess supplierProProcess)
    {
        return toAjax(supplierProProcessService.insertSupplierProProcess(supplierProProcess));
    }

    /**
     * 修改工艺
     */
    @RequiresPermissions("edi:process:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProProcess supplierProProcess = supplierProProcessService.selectSupplierProProcessById(id);
        mmap.put("supplierProProcess", supplierProProcess);
        return prefix + "/edit";
    }

    /**
     * 修改保存工艺
     */
    @RequiresPermissions("edi:process:edit")
    @Log(title = "工艺", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProProcess supplierProProcess)
    {
        return toAjax(supplierProProcessService.updateSupplierProProcess(supplierProProcess));
    }

    /**
     * 删除工艺
     */
    @RequiresPermissions("edi:process:remove")
    @Log(title = "工艺", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProProcessService.deleteSupplierProProcessByIds(ids));
    }
}
