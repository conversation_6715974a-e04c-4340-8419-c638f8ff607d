package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierProOeeTimeDetails;
import com.hj.admin.service.push.ISupplierProOeeTimeDetailsService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * OEE时间明细Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/details")
public class SupplierProOeeTimeDetailsController extends BaseController
{
    private String prefix = "edi/details";

    @Resource
    private ISupplierProOeeTimeDetailsService supplierProOeeTimeDetailsService;

    @RequiresPermissions("edi:details:view")
    @GetMapping()
    public String details()
    {
        return prefix + "/details";
    }

    /**
     * 查询OEE时间明细列表
     */
    @RequiresPermissions("edi:details:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProOeeTimeDetails supplierProOeeTimeDetails)
    {
        startPage();
        List<SupplierProOeeTimeDetails> list = supplierProOeeTimeDetailsService.selectSupplierProOeeTimeDetailsList(supplierProOeeTimeDetails);
        return getDataTable(list);
    }

    /**
     * 导出OEE时间明细列表
     */
    @RequiresPermissions("edi:details:export")
    @Log(title = "OEE时间明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProOeeTimeDetails supplierProOeeTimeDetails)
    {
        List<SupplierProOeeTimeDetails> list = supplierProOeeTimeDetailsService.selectSupplierProOeeTimeDetailsList(supplierProOeeTimeDetails);
        ExcelUtil<SupplierProOeeTimeDetails> util = new ExcelUtil<SupplierProOeeTimeDetails>(SupplierProOeeTimeDetails.class);
        return util.exportExcel(list, "OEE时间明细数据");
    }

    /**
     * 新增OEE时间明细
     */
    @RequiresPermissions("edi:details:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存OEE时间明细
     */
    @RequiresPermissions("edi:details:add")
    @Log(title = "OEE时间明细", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProOeeTimeDetails supplierProOeeTimeDetails)
    {
        return toAjax(supplierProOeeTimeDetailsService.insertSupplierProOeeTimeDetails(supplierProOeeTimeDetails));
    }

    /**
     * 修改OEE时间明细
     */
    @RequiresPermissions("edi:details:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProOeeTimeDetails supplierProOeeTimeDetails = supplierProOeeTimeDetailsService.selectSupplierProOeeTimeDetailsById(id);
        mmap.put("supplierProOeeTimeDetails", supplierProOeeTimeDetails);
        return prefix + "/edit";
    }

    /**
     * 修改保存OEE时间明细
     */
    @RequiresPermissions("edi:details:edit")
    @Log(title = "OEE时间明细", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProOeeTimeDetails supplierProOeeTimeDetails)
    {
        return toAjax(supplierProOeeTimeDetailsService.updateSupplierProOeeTimeDetails(supplierProOeeTimeDetails));
    }

    /**
     * 删除OEE时间明细
     */
    @RequiresPermissions("edi:details:remove")
    @Log(title = "OEE时间明细", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProOeeTimeDetailsService.deleteSupplierProOeeTimeDetailsByIds(ids));
    }
}
