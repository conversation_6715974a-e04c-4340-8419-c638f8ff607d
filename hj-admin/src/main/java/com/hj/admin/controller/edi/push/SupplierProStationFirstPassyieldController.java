package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierProStationFirstPassyield;
import com.hj.admin.service.push.ISupplierProDataService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工位一次合格率Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/passyield")
public class SupplierProStationFirstPassyieldController extends BaseController
{
    private String prefix = "edi/passyield";

    @Resource
    private ISupplierProDataService.ISupplierProStationFirstPassyieldService supplierProStationFirstPassyieldService;

    @RequiresPermissions("edi:passyield:view")
    @GetMapping()
    public String passyield()
    {
        return prefix + "/passyield";
    }

    /**
     * 查询工位一次合格率列表
     */
    @RequiresPermissions("edi:passyield:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProStationFirstPassyield supplierProStationFirstPassyield)
    {
        startPage();
        List<SupplierProStationFirstPassyield> list = supplierProStationFirstPassyieldService.selectSupplierProStationFirstPassyieldList(supplierProStationFirstPassyield);
        return getDataTable(list);
    }

    /**
     * 导出工位一次合格率列表
     */
    @RequiresPermissions("edi:passyield:export")
    @Log(title = "工位一次合格率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProStationFirstPassyield supplierProStationFirstPassyield)
    {
        List<SupplierProStationFirstPassyield> list = supplierProStationFirstPassyieldService.selectSupplierProStationFirstPassyieldList(supplierProStationFirstPassyield);
        ExcelUtil<SupplierProStationFirstPassyield> util = new ExcelUtil<SupplierProStationFirstPassyield>(SupplierProStationFirstPassyield.class);
        return util.exportExcel(list, "工位一次合格率数据");
    }

    /**
     * 新增工位一次合格率
     */
    @RequiresPermissions("edi:passyield:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存工位一次合格率
     */
    @RequiresPermissions("edi:passyield:add")
    @Log(title = "工位一次合格率", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProStationFirstPassyield supplierProStationFirstPassyield)
    {
        return toAjax(supplierProStationFirstPassyieldService.insertSupplierProStationFirstPassyield(supplierProStationFirstPassyield));
    }

    /**
     * 修改工位一次合格率
     */
    @RequiresPermissions("edi:passyield:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProStationFirstPassyield supplierProStationFirstPassyield = supplierProStationFirstPassyieldService.selectSupplierProStationFirstPassyieldById(id);
        mmap.put("supplierProStationFirstPassyield", supplierProStationFirstPassyield);
        return prefix + "/edit";
    }

    /**
     * 修改保存工位一次合格率
     */
    @RequiresPermissions("edi:passyield:edit")
    @Log(title = "工位一次合格率", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProStationFirstPassyield supplierProStationFirstPassyield)
    {
        return toAjax(supplierProStationFirstPassyieldService.updateSupplierProStationFirstPassyield(supplierProStationFirstPassyield));
    }

    /**
     * 删除工位一次合格率
     */
    @RequiresPermissions("edi:passyield:remove")
    @Log(title = "工位一次合格率", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProStationFirstPassyieldService.deleteSupplierProStationFirstPassyieldByIds(ids));
    }
}
