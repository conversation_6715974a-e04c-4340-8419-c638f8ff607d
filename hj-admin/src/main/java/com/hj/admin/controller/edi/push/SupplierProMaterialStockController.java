package com.hj.admin.controller.edi.push;

import java.util.List;

import com.hj.admin.domain.push.SupplierProMaterialStock;
import com.hj.admin.service.push.ISupplierProMaterialStockService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;

import javax.annotation.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 来料检验数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Controller
@RequestMapping("/edi/stock")
public class SupplierProMaterialStockController extends BaseController
{
    private String prefix = "edi/stock";

    @Resource
    private ISupplierProMaterialStockService supplierProMaterialStockService;

    @RequiresPermissions("edi:stock:view")
    @GetMapping()
    public String stock()
    {
        return prefix + "/stock";
    }

    /**
     * 查询来料检验数据列表
     */
    @RequiresPermissions("edi:stock:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProMaterialStock supplierProMaterialStock)
    {
        startPage();
        List<SupplierProMaterialStock> list = supplierProMaterialStockService.selectSupplierProMaterialStockList(supplierProMaterialStock);
        return getDataTable(list);
    }

    /**
     * 导出来料检验数据列表
     */
    @RequiresPermissions("edi:stock:export")
    @Log(title = "来料检验数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProMaterialStock supplierProMaterialStock)
    {
        List<SupplierProMaterialStock> list = supplierProMaterialStockService.selectSupplierProMaterialStockList(supplierProMaterialStock);
        ExcelUtil<SupplierProMaterialStock> util = new ExcelUtil<SupplierProMaterialStock>(SupplierProMaterialStock.class);
        return util.exportExcel(list, "来料检验数据数据");
    }

    /**
     * 新增来料检验数据
     */
    @RequiresPermissions("edi:stock:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存来料检验数据
     */
    @RequiresPermissions("edi:stock:add")
    @Log(title = "来料检验数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProMaterialStock supplierProMaterialStock)
    {
        return toAjax(supplierProMaterialStockService.insertSupplierProMaterialStock(supplierProMaterialStock));
    }

    /**
     * 修改来料检验数据
     */
    @RequiresPermissions("edi:stock:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProMaterialStock supplierProMaterialStock = supplierProMaterialStockService.selectSupplierProMaterialStockById(id);
        mmap.put("supplierProMaterialStock", supplierProMaterialStock);
        return prefix + "/edit";
    }

    /**
     * 修改保存来料检验数据
     */
    @RequiresPermissions("edi:stock:edit")
    @Log(title = "来料检验数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProMaterialStock supplierProMaterialStock)
    {
        return toAjax(supplierProMaterialStockService.updateSupplierProMaterialStock(supplierProMaterialStock));
    }

    /**
     * 删除来料检验数据
     */
    @RequiresPermissions("edi:stock:remove")
    @Log(title = "来料检验数据", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProMaterialStockService.deleteSupplierProMaterialStockByIds(ids));
    }
}
