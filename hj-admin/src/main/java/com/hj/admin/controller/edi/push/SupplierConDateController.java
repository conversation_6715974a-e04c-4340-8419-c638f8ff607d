package com.hj.admin.controller.edi.push;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.hj.common.annotation.Log;
import com.hj.common.enums.BusinessType;
import com.hj.admin.domain.push.SupplierConDate;
import com.hj.admin.service.push.ISupplierConDateService;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.utils.poi.ExcelUtil;
import com.hj.common.core.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 日物料需求计划风险确认Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/date")
public class SupplierConDateController extends BaseController
{
    private String prefix = "edi/date";

    @Resource
    private ISupplierConDateService supplierConDateService;

    @RequiresPermissions("edi:date:view")
    @GetMapping()
    public String date()
    {
        return prefix + "/date";
    }

    /**
     * 查询日物料需求计划风险确认列表
     */
    @RequiresPermissions("edi:date:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierConDate supplierConDate)
    {
        startPage();
        List<SupplierConDate> list = supplierConDateService.selectSupplierConDateList(supplierConDate);
        return getDataTable(list);
    }

    /**
     * 导出日物料需求计划风险确认列表
     */
    @RequiresPermissions("edi:date:export")
    @Log(title = "日物料需求计划风险确认", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierConDate supplierConDate)
    {
        List<SupplierConDate> list = supplierConDateService.selectSupplierConDateList(supplierConDate);
        ExcelUtil<SupplierConDate> util = new ExcelUtil<SupplierConDate>(SupplierConDate.class);
        return util.exportExcel(list, "日物料需求计划风险确认数据");
    }

    /**
     * 新增日物料需求计划风险确认
     */
    @RequiresPermissions("edi:date:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存日物料需求计划风险确认
     */
    @RequiresPermissions("edi:date:add")
    @Log(title = "日物料需求计划风险确认", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierConDate supplierConDate)
    {
        return toAjax(supplierConDateService.insertSupplierConDate(supplierConDate));
    }

    /**
     * 修改日物料需求计划风险确认
     */
    @RequiresPermissions("edi:date:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierConDate supplierConDate = supplierConDateService.selectSupplierConDateById(id);
        mmap.put("supplierConDate", supplierConDate);
        return prefix + "/edit";
    }

    /**
     * 修改保存日物料需求计划风险确认
     */
    @RequiresPermissions("edi:date:edit")
    @Log(title = "日物料需求计划风险确认", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierConDate supplierConDate)
    {
        return toAjax(supplierConDateService.updateSupplierConDate(supplierConDate));
    }

    /**
     * 删除日物料需求计划风险确认
     */
    @RequiresPermissions("edi:date:remove")
    @Log(title = "日物料需求计划风险确认", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierConDateService.deleteSupplierConDateByIds(ids));
    }
}
