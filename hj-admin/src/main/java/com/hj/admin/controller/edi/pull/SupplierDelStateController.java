package com.hj.admin.controller.edi.pull;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.hj.common.annotation.Log;
import com.hj.common.enums.BusinessType;
import com.hj.admin.domain.pull.SupplierDelState;
import com.hj.admin.service.pull.ISupplierDelStateService;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.utils.poi.ExcelUtil;
import com.hj.common.core.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 看板配送单Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/delState")
public class SupplierDelStateController extends BaseController
{
    private String prefix = "edi/delState";

    @Resource
    private ISupplierDelStateService supplierDelStateService;

    @RequiresPermissions("edi:state:view")
    @GetMapping()
    public String state()
    {
        return prefix + "/state";
    }

    /**
     * 查询看板配送单列表
     */
    @RequiresPermissions("edi:state:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierDelState supplierDelState)
    {
        startPage();
        List<SupplierDelState> list = supplierDelStateService.selectSupplierDelStateList(supplierDelState);
        return getDataTable(list);
    }

    /**
     * 导出看板配送单列表
     */
    @RequiresPermissions("edi:state:export")
    @Log(title = "看板配送单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierDelState supplierDelState)
    {
        List<SupplierDelState> list = supplierDelStateService.selectSupplierDelStateList(supplierDelState);
        ExcelUtil<SupplierDelState> util = new ExcelUtil<SupplierDelState>(SupplierDelState.class);
        return util.exportExcel(list, "看板配送单数据");
    }

    /**
     * 新增看板配送单
     */
    @RequiresPermissions("edi:state:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存看板配送单
     */
    @RequiresPermissions("edi:state:add")
    @Log(title = "看板配送单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierDelState supplierDelState)
    {
        return toAjax(supplierDelStateService.insertSupplierDelState(supplierDelState));
    }

    /**
     * 修改看板配送单
     */
    @RequiresPermissions("edi:state:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierDelState supplierDelState = supplierDelStateService.selectSupplierDelStateById(id);
        mmap.put("supplierDelState", supplierDelState);
        return prefix + "/edit";
    }

    /**
     * 修改保存看板配送单
     */
    @RequiresPermissions("edi:state:edit")
    @Log(title = "看板配送单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierDelState supplierDelState)
    {
        return toAjax(supplierDelStateService.updateSupplierDelState(supplierDelState));
    }

    /**
     * 删除看板配送单
     */
    @RequiresPermissions("edi:state:remove")
    @Log(title = "看板配送单", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierDelStateService.deleteSupplierDelStateByIds(ids));
    }
}
