package com.hj.admin.controller.edi;

import com.hj.admin.domain.base.CheryPushReq;
import com.hj.admin.domain.base.CheryPullRes;
import com.hj.admin.domain.base.BaseDomain;
import com.hj.admin.domain.push.SupplierProMaterialStock;
import com.hj.admin.service.IDoRequestService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("push")
public class PushController {

    private final IDoRequestService doRequestService;

    public PushController(IDoRequestService doRequestService) {
        this.doRequestService = doRequestService;
    }

    @Value("${chery.path.push.proMaterialStock}")
    private String proMaterialStockPath;

    @PostMapping("supplierProMaterialStock")
    public CheryPullRes supplierProMaterialStock() {
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setTotal(1);
        cheryPushReq.setPageSize(1);
        cheryPushReq.setPageNum(1);
        SupplierProMaterialStock supplierProMaterialStock = new SupplierProMaterialStock();
        supplierProMaterialStock.setSupplierCode("7PL");
        supplierProMaterialStock.setSupplierName("宏景电子");
        supplierProMaterialStock.setSupplierSubCode("111");
        supplierProMaterialStock.setSupplierSubName("111");
        supplierProMaterialStock.setSubSupplierCode("111");
        supplierProMaterialStock.setSubSupplierName("111");
        supplierProMaterialStock.setSubSupplierAddress("111");
        supplierProMaterialStock.setComponentCode("111");
        supplierProMaterialStock.setComponentName("111");
        supplierProMaterialStock.setSubBatchNo("111");
        supplierProMaterialStock.setSubBatchNum(111L);
        supplierProMaterialStock.setSubBatchSn("111");
        supplierProMaterialStock.setEmpCode("111");
        supplierProMaterialStock.setEmpName("111");
        supplierProMaterialStock.setDeviceCode("111");
        supplierProMaterialStock.setDeviceName("111");
        supplierProMaterialStock.setFeatureName("111");
        supplierProMaterialStock.setFeatureUnit("111");
        supplierProMaterialStock.setStandardValue("111");
        supplierProMaterialStock.setFeatureUpper("111");
        supplierProMaterialStock.setFeatureLower("111");
        supplierProMaterialStock.setFeatureValue("111");
        supplierProMaterialStock.setCheckNo("111");
        supplierProMaterialStock.setCheckResult("OK");
        supplierProMaterialStock.setCheckTime("2025-08-20 04:44:44");
        supplierProMaterialStock.setSamplingRate(111L);
        supplierProMaterialStock.setLimitUpdateTime("2025-08-20 04:44:44");
        supplierProMaterialStock.setVendorFieldDesc("111");
        supplierProMaterialStock.setVendorFieldCode("111");
        supplierProMaterialStock.setDeadLine("2025-08-20 04:44:44");

        List<BaseDomain> list = new ArrayList<>();
        list.add(supplierProMaterialStock);
        cheryPushReq.setList(list);

        return doRequestService.push(proMaterialStockPath, cheryPushReq);
    }

}
