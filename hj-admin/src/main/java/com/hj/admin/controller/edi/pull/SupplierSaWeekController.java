package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierSaWeek;
import com.hj.admin.service.pull.ISupplierSaWeekService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 计划协议Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/week")
public class SupplierSaWeekController extends BaseController
{
    private String prefix = "edi/week";

    @Resource
    private ISupplierSaWeekService supplierSaWeekService;

    @RequiresPermissions("edi:week:view")
    @GetMapping()
    public String week()
    {
        return prefix + "/week";
    }

    /**
     * 查询计划协议列表
     */
    @RequiresPermissions("edi:week:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierSaWeek supplierSaWeek)
    {
        startPage();
        List<SupplierSaWeek> list = supplierSaWeekService.selectSupplierSaWeekList(supplierSaWeek);
        return getDataTable(list);
    }

    /**
     * 导出计划协议列表
     */
    @RequiresPermissions("edi:week:export")
    @Log(title = "计划协议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierSaWeek supplierSaWeek)
    {
        List<SupplierSaWeek> list = supplierSaWeekService.selectSupplierSaWeekList(supplierSaWeek);
        ExcelUtil<SupplierSaWeek> util = new ExcelUtil<SupplierSaWeek>(SupplierSaWeek.class);
        return util.exportExcel(list, "计划协议数据");
    }

    /**
     * 新增计划协议
     */
    @RequiresPermissions("edi:week:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存计划协议
     */
    @RequiresPermissions("edi:week:add")
    @Log(title = "计划协议", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierSaWeek supplierSaWeek)
    {
        return toAjax(supplierSaWeekService.insertSupplierSaWeek(supplierSaWeek));
    }

    /**
     * 修改计划协议
     */
    @RequiresPermissions("edi:week:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierSaWeek supplierSaWeek = supplierSaWeekService.selectSupplierSaWeekById(id);
        mmap.put("supplierSaWeek", supplierSaWeek);
        return prefix + "/edit";
    }

    /**
     * 修改保存计划协议
     */
    @RequiresPermissions("edi:week:edit")
    @Log(title = "计划协议", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierSaWeek supplierSaWeek)
    {
        return toAjax(supplierSaWeekService.updateSupplierSaWeek(supplierSaWeek));
    }

    /**
     * 删除计划协议
     */
    @RequiresPermissions("edi:week:remove")
    @Log(title = "计划协议", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierSaWeekService.deleteSupplierSaWeekByIds(ids));
    }
}
