package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierConPo;
import com.hj.admin.service.push.ISupplierConPoService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 采购订单风险确认Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/conPoCon")
public class SupplierConPoController extends BaseController
{
    private String prefix = "edi/conPoCon";

    @Resource
    private ISupplierConPoService supplierConPoService;

    @RequiresPermissions("edi:po:view")
    @GetMapping()
    public String po()
    {
        return prefix + "/po";
    }

    /**
     * 查询采购订单风险确认列表
     */
    @RequiresPermissions("edi:po:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierConPo supplierConPo)
    {
        startPage();
        List<SupplierConPo> list = supplierConPoService.selectSupplierConPoList(supplierConPo);
        return getDataTable(list);
    }

    /**
     * 导出采购订单风险确认列表
     */
    @RequiresPermissions("edi:po:export")
    @Log(title = "采购订单风险确认", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierConPo supplierConPo)
    {
        List<SupplierConPo> list = supplierConPoService.selectSupplierConPoList(supplierConPo);
        ExcelUtil<SupplierConPo> util = new ExcelUtil<SupplierConPo>(SupplierConPo.class);
        return util.exportExcel(list, "采购订单风险确认数据");
    }

    /**
     * 新增采购订单风险确认
     */
    @RequiresPermissions("edi:po:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存采购订单风险确认
     */
    @RequiresPermissions("edi:po:add")
    @Log(title = "采购订单风险确认", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierConPo supplierConPo)
    {
        return toAjax(supplierConPoService.insertSupplierConPo(supplierConPo));
    }

    /**
     * 修改采购订单风险确认
     */
    @RequiresPermissions("edi:po:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierConPo supplierConPo = supplierConPoService.selectSupplierConPoById(id);
        mmap.put("supplierConPo", supplierConPo);
        return prefix + "/edit";
    }

    /**
     * 修改保存采购订单风险确认
     */
    @RequiresPermissions("edi:po:edit")
    @Log(title = "采购订单风险确认", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierConPo supplierConPo)
    {
        return toAjax(supplierConPoService.updateSupplierConPo(supplierConPo));
    }

    /**
     * 删除采购订单风险确认
     */
    @RequiresPermissions("edi:po:remove")
    @Log(title = "采购订单风险确认", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierConPoService.deleteSupplierConPoByIds(ids));
    }
}
