package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierMrpDate;
import com.hj.admin.service.pull.ISupplierMrpDateService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 日物料需求计划Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/supplierMrpDate")
public class SupplierMrpDateController extends BaseController
{
    private String prefix = "edi/supplierMrpDate";

    @Resource
    private ISupplierMrpDateService supplierMrpDateService;

    @RequiresPermissions("edi:date:view")
    @GetMapping()
    public String date()
    {
        return prefix + "/date";
    }

    /**
     * 查询日物料需求计划列表
     */
    @RequiresPermissions("edi:date:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierMrpDate supplierMrpDate)
    {
        startPage();
        List<SupplierMrpDate> list = supplierMrpDateService.selectSupplierMrpDateList(supplierMrpDate);
        return getDataTable(list);
    }

    /**
     * 导出日物料需求计划列表
     */
    @RequiresPermissions("edi:date:export")
    @Log(title = "日物料需求计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierMrpDate supplierMrpDate)
    {
        List<SupplierMrpDate> list = supplierMrpDateService.selectSupplierMrpDateList(supplierMrpDate);
        ExcelUtil<SupplierMrpDate> util = new ExcelUtil<SupplierMrpDate>(SupplierMrpDate.class);
        return util.exportExcel(list, "日物料需求计划数据");
    }

    /**
     * 新增日物料需求计划
     */
    @RequiresPermissions("edi:date:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存日物料需求计划
     */
    @RequiresPermissions("edi:date:add")
    @Log(title = "日物料需求计划", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierMrpDate supplierMrpDate)
    {
        return toAjax(supplierMrpDateService.insertSupplierMrpDate(supplierMrpDate));
    }

    /**
     * 修改日物料需求计划
     */
    @RequiresPermissions("edi:date:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierMrpDate supplierMrpDate = supplierMrpDateService.selectSupplierMrpDateById(id);
        mmap.put("supplierMrpDate", supplierMrpDate);
        return prefix + "/edit";
    }

    /**
     * 修改保存日物料需求计划
     */
    @RequiresPermissions("edi:date:edit")
    @Log(title = "日物料需求计划", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierMrpDate supplierMrpDate)
    {
        return toAjax(supplierMrpDateService.updateSupplierMrpDate(supplierMrpDate));
    }

    /**
     * 删除日物料需求计划
     */
    @RequiresPermissions("edi:date:remove")
    @Log(title = "日物料需求计划", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierMrpDateService.deleteSupplierMrpDateByIds(ids));
    }
}
