package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierProOeeAchievementRate;
import com.hj.admin.service.push.ISupplierProOeeAchievementRateService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备OEE达成率Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/rate")
public class SupplierProOeeAchievementRateController extends BaseController
{
    private String prefix = "edi/rate";

    @Resource
    private ISupplierProOeeAchievementRateService supplierProOeeAchievementRateService;

    @RequiresPermissions("edi:rate:view")
    @GetMapping()
    public String rate()
    {
        return prefix + "/rate";
    }

    /**
     * 查询设备OEE达成率列表
     */
    @RequiresPermissions("edi:rate:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProOeeAchievementRate supplierProOeeAchievementRate)
    {
        startPage();
        List<SupplierProOeeAchievementRate> list = supplierProOeeAchievementRateService.selectSupplierProOeeAchievementRateList(supplierProOeeAchievementRate);
        return getDataTable(list);
    }

    /**
     * 导出设备OEE达成率列表
     */
    @RequiresPermissions("edi:rate:export")
    @Log(title = "设备OEE达成率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProOeeAchievementRate supplierProOeeAchievementRate)
    {
        List<SupplierProOeeAchievementRate> list = supplierProOeeAchievementRateService.selectSupplierProOeeAchievementRateList(supplierProOeeAchievementRate);
        ExcelUtil<SupplierProOeeAchievementRate> util = new ExcelUtil<SupplierProOeeAchievementRate>(SupplierProOeeAchievementRate.class);
        return util.exportExcel(list, "设备OEE达成率数据");
    }

    /**
     * 新增设备OEE达成率
     */
    @RequiresPermissions("edi:rate:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存设备OEE达成率
     */
    @RequiresPermissions("edi:rate:add")
    @Log(title = "设备OEE达成率", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProOeeAchievementRate supplierProOeeAchievementRate)
    {
        return toAjax(supplierProOeeAchievementRateService.insertSupplierProOeeAchievementRate(supplierProOeeAchievementRate));
    }

    /**
     * 修改设备OEE达成率
     */
    @RequiresPermissions("edi:rate:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProOeeAchievementRate supplierProOeeAchievementRate = supplierProOeeAchievementRateService.selectSupplierProOeeAchievementRateById(id);
        mmap.put("supplierProOeeAchievementRate", supplierProOeeAchievementRate);
        return prefix + "/edit";
    }

    /**
     * 修改保存设备OEE达成率
     */
    @RequiresPermissions("edi:rate:edit")
    @Log(title = "设备OEE达成率", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProOeeAchievementRate supplierProOeeAchievementRate)
    {
        return toAjax(supplierProOeeAchievementRateService.updateSupplierProOeeAchievementRate(supplierProOeeAchievementRate));
    }

    /**
     * 删除设备OEE达成率
     */
    @RequiresPermissions("edi:rate:remove")
    @Log(title = "设备OEE达成率", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProOeeAchievementRateService.deleteSupplierProOeeAchievementRateByIds(ids));
    }
}
