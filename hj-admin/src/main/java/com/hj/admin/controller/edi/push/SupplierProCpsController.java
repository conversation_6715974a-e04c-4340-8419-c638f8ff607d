package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierProCps;
import com.hj.admin.service.push.ISupplierProCpsService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 过程控制项质量数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/cps")
public class SupplierProCpsController extends BaseController
{
    private String prefix = "edi/cps";

    @Resource
    private ISupplierProCpsService supplierProCpsService;

    @RequiresPermissions("edi:cps:view")
    @GetMapping()
    public String cps()
    {
        return prefix + "/cps";
    }

    /**
     * 查询过程控制项质量数据列表
     */
    @RequiresPermissions("edi:cps:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProCps supplierProCps)
    {
        startPage();
        List<SupplierProCps> list = supplierProCpsService.selectSupplierProCpsList(supplierProCps);
        return getDataTable(list);
    }

    /**
     * 导出过程控制项质量数据列表
     */
    @RequiresPermissions("edi:cps:export")
    @Log(title = "过程控制项质量数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProCps supplierProCps)
    {
        List<SupplierProCps> list = supplierProCpsService.selectSupplierProCpsList(supplierProCps);
        ExcelUtil<SupplierProCps> util = new ExcelUtil<SupplierProCps>(SupplierProCps.class);
        return util.exportExcel(list, "过程控制项质量数据数据");
    }

    /**
     * 新增过程控制项质量数据
     */
    @RequiresPermissions("edi:cps:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存过程控制项质量数据
     */
    @RequiresPermissions("edi:cps:add")
    @Log(title = "过程控制项质量数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProCps supplierProCps)
    {
        return toAjax(supplierProCpsService.insertSupplierProCps(supplierProCps));
    }

    /**
     * 修改过程控制项质量数据
     */
    @RequiresPermissions("edi:cps:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProCps supplierProCps = supplierProCpsService.selectSupplierProCpsById(id);
        mmap.put("supplierProCps", supplierProCps);
        return prefix + "/edit";
    }

    /**
     * 修改保存过程控制项质量数据
     */
    @RequiresPermissions("edi:cps:edit")
    @Log(title = "过程控制项质量数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProCps supplierProCps)
    {
        return toAjax(supplierProCpsService.updateSupplierProCps(supplierProCps));
    }

    /**
     * 删除过程控制项质量数据
     */
    @RequiresPermissions("edi:cps:remove")
    @Log(title = "过程控制项质量数据", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProCpsService.deleteSupplierProCpsByIds(ids));
    }
}
