package com.hj.admin.controller.system;

import javax.annotation.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.domain.entity.SysUser;
import com.hj.common.utils.StringUtils;
import com.hj.framework.shiro.service.SysRegisterService;
import com.hj.system.service.ISysConfigService;

/**
 * 注册验证
 * 
 * <AUTHOR>
 */
@Controller
public class SysRegisterController extends BaseController
{
    @Resource
    private SysRegisterService registerService;

    @Resource
    private ISysConfigService configService;

    @GetMapping("/register")
    public String register()
    {
        return "register";
    }

    @PostMapping("/register")
    @ResponseBody
    public AjaxResult ajaxRegister(SysUser user)
    {
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser"))))
        {
            return error("当前系统没有开启注册功能！");
        }
        String msg = registerService.register(user);
        return StringUtils.isEmpty(msg) ? success() : error(msg);
    }
}
