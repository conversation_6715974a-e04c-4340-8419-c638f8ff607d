package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierMrpMonth;
import com.hj.admin.service.pull.ISupplierMrpMonthService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * M+6月物料需求计划Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/month")
public class SupplierMrpMonthController extends BaseController
{
    private String prefix = "edi/month";

    @Resource
    private ISupplierMrpMonthService supplierMrpMonthService;

    @RequiresPermissions("edi:month:view")
    @GetMapping()
    public String month()
    {
        return prefix + "/month";
    }

    /**
     * 查询M+6月物料需求计划列表
     */
    @RequiresPermissions("edi:month:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierMrpMonth supplierMrpMonth)
    {
        startPage();
        List<SupplierMrpMonth> list = supplierMrpMonthService.selectSupplierMrpMonthList(supplierMrpMonth);
        return getDataTable(list);
    }

    /**
     * 导出M+6月物料需求计划列表
     */
    @RequiresPermissions("edi:month:export")
    @Log(title = "M+6月物料需求计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierMrpMonth supplierMrpMonth)
    {
        List<SupplierMrpMonth> list = supplierMrpMonthService.selectSupplierMrpMonthList(supplierMrpMonth);
        ExcelUtil<SupplierMrpMonth> util = new ExcelUtil<SupplierMrpMonth>(SupplierMrpMonth.class);
        return util.exportExcel(list, "M+6月物料需求计划数据");
    }

    /**
     * 新增M+6月物料需求计划
     */
    @RequiresPermissions("edi:month:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存M+6月物料需求计划
     */
    @RequiresPermissions("edi:month:add")
    @Log(title = "M+6月物料需求计划", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierMrpMonth supplierMrpMonth)
    {
        return toAjax(supplierMrpMonthService.insertSupplierMrpMonth(supplierMrpMonth));
    }

    /**
     * 修改M+6月物料需求计划
     */
    @RequiresPermissions("edi:month:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierMrpMonth supplierMrpMonth = supplierMrpMonthService.selectSupplierMrpMonthById(id);
        mmap.put("supplierMrpMonth", supplierMrpMonth);
        return prefix + "/edit";
    }

    /**
     * 修改保存M+6月物料需求计划
     */
    @RequiresPermissions("edi:month:edit")
    @Log(title = "M+6月物料需求计划", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierMrpMonth supplierMrpMonth)
    {
        return toAjax(supplierMrpMonthService.updateSupplierMrpMonth(supplierMrpMonth));
    }

    /**
     * 删除M+6月物料需求计划
     */
    @RequiresPermissions("edi:month:remove")
    @Log(title = "M+6月物料需求计划", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierMrpMonthService.deleteSupplierMrpMonthByIds(ids));
    }
}
