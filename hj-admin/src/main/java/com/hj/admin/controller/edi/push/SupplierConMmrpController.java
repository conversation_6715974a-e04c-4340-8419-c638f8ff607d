package com.hj.admin.controller.edi.push;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;

import javax.annotation.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.hj.common.annotation.Log;
import com.hj.common.enums.BusinessType;
import com.hj.admin.domain.push.SupplierConMmrp;
import com.hj.admin.service.push.ISupplierConMmrpService;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.utils.poi.ExcelUtil;
import com.hj.common.core.page.TableDataInfo;

/**
 * M+6月物料需求计划风险确认Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/mmrp")
public class SupplierConMmrpController extends BaseController
{
    private String prefix = "edi/mmrp";

    @Resource
    private ISupplierConMmrpService supplierConMmrpService;

    @RequiresPermissions("edi:mmrp:view")
    @GetMapping()
    public String mmrp()
    {
        return prefix + "/mmrp";
    }

    /**
     * 查询M+6月物料需求计划风险确认列表
     */
    @RequiresPermissions("edi:mmrp:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierConMmrp supplierConMmrp)
    {
        startPage();
        List<SupplierConMmrp> list = supplierConMmrpService.selectSupplierConMmrpList(supplierConMmrp);
        return getDataTable(list);
    }

    /**
     * 导出M+6月物料需求计划风险确认列表
     */
    @RequiresPermissions("edi:mmrp:export")
    @Log(title = "M+6月物料需求计划风险确认", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierConMmrp supplierConMmrp)
    {
        List<SupplierConMmrp> list = supplierConMmrpService.selectSupplierConMmrpList(supplierConMmrp);
        ExcelUtil<SupplierConMmrp> util = new ExcelUtil<SupplierConMmrp>(SupplierConMmrp.class);
        return util.exportExcel(list, "M+6月物料需求计划风险确认数据");
    }

    /**
     * 新增M+6月物料需求计划风险确认
     */
    @RequiresPermissions("edi:mmrp:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存M+6月物料需求计划风险确认
     */
    @RequiresPermissions("edi:mmrp:add")
    @Log(title = "M+6月物料需求计划风险确认", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierConMmrp supplierConMmrp)
    {
        return toAjax(supplierConMmrpService.insertSupplierConMmrp(supplierConMmrp));
    }

    /**
     * 修改M+6月物料需求计划风险确认
     */
    @RequiresPermissions("edi:mmrp:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierConMmrp supplierConMmrp = supplierConMmrpService.selectSupplierConMmrpById(id);
        mmap.put("supplierConMmrp", supplierConMmrp);
        return prefix + "/edit";
    }

    /**
     * 修改保存M+6月物料需求计划风险确认
     */
    @RequiresPermissions("edi:mmrp:edit")
    @Log(title = "M+6月物料需求计划风险确认", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierConMmrp supplierConMmrp)
    {
        return toAjax(supplierConMmrpService.updateSupplierConMmrp(supplierConMmrp));
    }

    /**
     * 删除M+6月物料需求计划风险确认
     */
    @RequiresPermissions("edi:mmrp:remove")
    @Log(title = "M+6月物料需求计划风险确认", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierConMmrpService.deleteSupplierConMmrpByIds(ids));
    }
}
