package com.hj.admin.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.hj.admin.domain.base.CheryPushReq;
import com.hj.admin.domain.base.BaseDomain;
import com.hj.admin.domain.push.SupplierProMaterialStock;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;

/**
 * 一个用于加密和生成随机字符串的工具类。
 */
public class CryptoUtils {

    private static final String ALPHANUMERIC = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final Random RANDOM = new SecureRandom();

    /**
     * 对输入字符串进行SHA-512 Hex加密。
     *
     * @param input 要加密的字符串。
     * @return 加密后的SHA-512 Hex字符串。
     */
    public static String generateSha512Hex(String input) {
        if (input == null) {
            return null;
        }
        try {
            // 获取SHA-512算法的MessageDigest实例 [6, 8]
            MessageDigest md = MessageDigest.getInstance("SHA-512");

            // 计算消息摘要
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));

            // 将字节数组转换为十六进制表示 [6]
            BigInteger no = new BigInteger(1, messageDigest);
            StringBuilder hashText = new StringBuilder(no.toString(16));

            // 补零，以确保结果是128位的十六进制字符串
            while (hashText.length() < 128) {
                hashText.insert(0, "0");
            }

            return hashText.toString();
        } catch (NoSuchAlgorithmException e) {
            // 在标准Java环境中，SHA-512算法总是可用的
            throw new RuntimeException("SHA-512 algorithm not found", e);
        }
    }

    /**
     * 生成一个指定长度的唯一随机字符串
     * <p>
     * 为了保证唯一性，此方法结合了UUID和安全随机数生成器。 [1, 5]
     *
     * @param length 期望的字符串长度
     * @return 生成的唯一随机字符串
     * @throws IllegalArgumentException 如果长度超出10-50的范围
     */
    public static String generateRandomString(int length) {
        // 使用UUID确保基本唯一性 [1, 2]
        String uuid = UUID.randomUUID().toString().replace("-", "");

        // 如果所需长度小于等于32，则截取UUID的一部分
        if (length <= 32) {
            return uuid.substring(0, length);
        }

        // 如果所需长度大于32，则在UUID基础上补充随机字符
        StringBuilder sb = new StringBuilder(uuid);
        for (int i = 0; i < length - 32; i++) {
            sb.append(ALPHANUMERIC.charAt(RANDOM.nextInt(ALPHANUMERIC.length())));
        }

        return sb.toString();
    }


    public static void main(String[] args) {
        String method = "POST";
        String path = "/v2/get/supplierProPlaning";

        path = "/v2/push/supplierProMaterialStock";
//        path = "/v2/push/supplierProScheduling";

        String timestamp = "" + new Date().getTime();
        String nonce = generateRandomString(RandomUtil.randomInt(40) + 10);
        String appKey = "7PLff4f4e5cfb6740a";
        String appSecret = "ddff40f0c7c84c9ebf2df08b3904eb6c";

        Map<String, Object> map = new LinkedHashMap<>();
//        map.put("date", "2025-08-01");
//        map.put("pageSize", 1000);
//        map.put("pageNum", 1);
//        map.put("isForce", false);

        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo("5345464665765767");
        cheryPushReq.setTotal(1);
        cheryPushReq.setPageSize(1);
        cheryPushReq.setPageNum(1);
        SupplierProMaterialStock supplierProMaterialStock = new SupplierProMaterialStock();
        supplierProMaterialStock.setSupplierCode("7PL");
        supplierProMaterialStock.setSupplierName("宏景电子");
        supplierProMaterialStock.setSupplierSubCode("111");
        supplierProMaterialStock.setSupplierSubName("111");
        supplierProMaterialStock.setSubSupplierCode("111");
        supplierProMaterialStock.setSubSupplierName("111");
        supplierProMaterialStock.setSubSupplierAddress("111");
        supplierProMaterialStock.setComponentCode("111");
        supplierProMaterialStock.setComponentName("111");
        supplierProMaterialStock.setSubBatchNo("111");
        supplierProMaterialStock.setSubBatchNum(111L);
        supplierProMaterialStock.setSubBatchSn("111");
        supplierProMaterialStock.setEmpCode("111");
        supplierProMaterialStock.setEmpName("111");
        supplierProMaterialStock.setDeviceCode("111");
        supplierProMaterialStock.setDeviceName("111");
        supplierProMaterialStock.setFeatureName("111");
        supplierProMaterialStock.setFeatureUnit("111");
        supplierProMaterialStock.setStandardValue("111");
        supplierProMaterialStock.setFeatureUpper("111");
        supplierProMaterialStock.setFeatureLower("111");
        supplierProMaterialStock.setFeatureValue("111");
        supplierProMaterialStock.setCheckNo("111");
        supplierProMaterialStock.setCheckResult("OK");
        supplierProMaterialStock.setCheckTime("2025-08-20 04:44:44");
        supplierProMaterialStock.setSamplingRate(111L);
        supplierProMaterialStock.setLimitUpdateTime("2025-08-20 04:44:44");
        supplierProMaterialStock.setVendorFieldDesc("111");
        supplierProMaterialStock.setVendorFieldCode("111");
        supplierProMaterialStock.setDeadLine("2025-08-20 04:44:44");
        List<BaseDomain> list = new ArrayList<>();
        list.add(supplierProMaterialStock);
        cheryPushReq.setList(list);

        String jsonBody = JSONUtil.toJsonStr(cheryPushReq);


        String originalString = "method=" + method + "&path=" + path + "&appKey=" + appKey
                + "&appSecret=" + appSecret + "&timestamp=" + timestamp +
                "&nonce=" + nonce + "&jsonBody=" + jsonBody;
        String encryptedString = generateSha512Hex(originalString);
        System.out.println("原始字符串: " + originalString);
        System.out.println("SHA-512 Hex加密后: " + encryptedString);
        System.out.println("timestamp: " + timestamp);
        System.out.println("nonce: " + nonce);
    }

}

