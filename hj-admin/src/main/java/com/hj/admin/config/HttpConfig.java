package com.hj.admin.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Configuration
public class HttpConfig {

    @Resource
    private RepConfig repConfig;

    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(repConfig.getConnectTimeout());
        factory.setReadTimeout(repConfig.getReadTimeout());
        return new RestTemplate(factory);
    }
}
